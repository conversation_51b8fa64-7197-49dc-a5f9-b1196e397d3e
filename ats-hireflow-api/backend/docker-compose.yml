version: "3.5"
services:
  web:
    restart: always
    platform: linux/amd64
    ports:
      - "8088:8080"
    build:
      context: .
      dockerfile: Dockerfile
    image: registry.gitlab.com/12bay.vn/ssl.12bay.vn/hireflow-alpine:0.0.7
    networks:
      - backend
    environment:
      - "DB_DATABASE=hire_flow"
      - "REDIS_HOST=redis"
      - "QUEUE_DRIVER=sync"
      - "NUMBER_OF_WORKER=1"
    volumes:
      - .storage/logs:/www/storage/logs
  redis:
    restart: always
    image: redis:4-alpine
    command: redis-server --requirepass redis
    ports:
      - 16379:6379
    volumes:
      - redis:/data
    networks:
      - backend
networks:
  backend:
volumes:
  redis:
    driver: "local"
