<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Add a new JSON column for notes and migrate existing data
     */
    public function up(): void
    {
        // Step 1: Add new JSON column
        Schema::table('candidates', function (Blueprint $table) {
            $table->json('notes_json')->nullable()->after('notes');
        });

        // Step 2: Migrate existing data
        $this->migrateNotesToJson();

        // Step 3: Drop old column and rename new one
        Schema::table('candidates', function (Blueprint $table) {
            $table->dropColumn('notes');
        });

        Schema::table('candidates', function (Blueprint $table) {
            $table->renameColumn('notes_json', 'notes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Step 1: Add text column back
        Schema::table('candidates', function (Blueprint $table) {
            $table->text('notes_text')->nullable()->after('notes');
        });

        // Step 2: Convert JSO<PERSON> back to text
        $this->convertJsonToText();

        // Step 3: Drop JSON column and rename text column
        Schema::table('candidates', function (Blueprint $table) {
            $table->dropColumn('notes');
        });

        Schema::table('candidates', function (Blueprint $table) {
            $table->renameColumn('notes_text', 'notes');
        });
    }

    /**
     * Migrate existing notes to JSON format
     */
    private function migrateNotesToJson(): void
    {
        $candidates = DB::table('candidates')
            ->whereNotNull('notes')
            ->where('notes', '!=', '')
            ->get();

        foreach ($candidates as $candidate) {
            $notesArray = [];

            // Check if notes is already in JSON format
            $decodedNotes = json_decode($candidate->notes, true);

            if (is_array($decodedNotes) && json_last_error() === JSON_ERROR_NONE) {
                // Already JSON, use as is
                $notesArray = $decodedNotes;
            } else {
                // Convert text to JSON structure
                if (!empty($candidate->notes)) {
                    $notesArray[] = [
                        'created_at' => $candidate->created_at ?? now()->toISOString(),
                        'created_by' => 'System Migration',
                        'created_id' => $candidate->created_by,
                        'content' => $candidate->notes
                    ];
                }
            }

            // Update candidate with JSON structure
            DB::table('candidates')
                ->where('id', $candidate->id)
                ->update([
                    'notes_json' => json_encode($notesArray)
                ]);
        }
    }

    /**
     * Convert JSON notes back to text format (for rollback)
     */
    private function convertJsonToText(): void
    {
        $candidates = DB::table('candidates')
            ->whereNotNull('notes')
            ->get();

        foreach ($candidates as $candidate) {
            $textNotes = '';

            if (!empty($candidate->notes)) {
                $notesArray = json_decode($candidate->notes, true);

                if (is_array($notesArray)) {
                    $noteTexts = [];
                    foreach ($notesArray as $note) {
                        if (isset($note['content'])) {
                            $noteText = $note['content'];
                            if (isset($note['created_by']) && isset($note['created_at'])) {
                                $noteText .= ' (by ' . $note['created_by'] . ' on ' . $note['created_at'] . ')';
                            }
                            $noteTexts[] = $noteText;
                        }
                    }
                    $textNotes = implode("\n\n", $noteTexts);
                }
            }

            // Update candidate with text format
            DB::table('candidates')
                ->where('id', $candidate->id)
                ->update([
                    'notes_text' => $textNotes
                ]);
        }
    }
};
