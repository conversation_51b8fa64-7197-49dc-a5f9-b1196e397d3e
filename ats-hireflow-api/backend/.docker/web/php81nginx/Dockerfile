ARG ALPINE_VERSION=3.17
FROM alpine:${ALPINE_VERSION}
LABEL Maintainer="<PERSON>er <<EMAIL>>"
LABEL Description="Lightweight container with Nginx 1.22 & PHP 8.1 based on Alpine Linux."
# Setup document root
WORKDIR /www

# Install packages and remove default server definition
RUN apk add --no-cache \
  curl \
  nginx \
  zip \
  unzip \
  vim \
  php81 \
  php81-bcmath \
  php81-ctype \
  php81-curl \
  php81-dom \
  php81-fpm \
  php81-gd \
  php81-intl \
  php81-fileinfo \
  php81-mbstring \
  php81-pdo_mysql \
  php81-pdo_sqlite \
  php81-mysqli \
  php81-opcache \
  php81-openssl \
  php81-phar \
  php81-iconv \
  php81-simplexml \
  php81-session \
  php81-xml \
  php81-xmlreader \
  php81-tokenizer \
  php81-xmlwriter \
  php81-redis \
  php81-zip \
  php81-posix \
  php81-pcntl \
  supervisor


# Create symlink so programs depending on `php` still function
RUN ln -sf /usr/bin/php81 /usr/bin/php

ARG DOCROOT=.docker/web/php81nginx/

# Configure nginx - http
COPY ${DOCROOT}config/nginx.conf /etc/nginx/nginx.conf
# Configure nginx - default server
COPY ${DOCROOT}config/conf.d /etc/nginx/conf.d/

# Configure PHP-FPM
COPY ${DOCROOT}config/fpm-pool.conf /etc/php81/php-fpm.d/www.conf
COPY ${DOCROOT}config/php.ini /etc/php81/conf.d/custom.ini

# Configure supervisord
COPY ${DOCROOT}config/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Make sure files/folders needed by the processes are accessable when they run under the nobody user
RUN chown -R nobody.nobody /www /run /var/lib/nginx /var/log/nginx

# Switch to use a non-root user from here on
#USER nobody

# Add application
#COPY --chown=nobody src /www

# Expose the port nginx is reachable on


ENV SERVER_NAME=ssl.12bay.vn
ENV SERVER_ROOT=/www/public

LABEL maintainer="<EMAIL>"

ENV TZ Asia/Ho_Chi_Minh

COPY .docker/web/php.ini /etc/php8/php.ini


RUN  mkdir -p /www/vendor
COPY composer.json /www/composer.json

## INSTALL COMPOSER
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer \
    && ln -s $(composer config --global home) /root/composer
ENV PATH=$PATH:/root/composer/vendor/bin COMPOSER_ALLOW_SUPERUSER=1
RUN php -d memory_limit=-1 /usr/local/bin/composer update --ignore-platform-reqs --prefer-dist --no-scripts --no-dev --no-autoloader && rm -rf /root/.composer

WORKDIR /www

## COPY SOURCE
COPY --chown=nobody.nobody: . /www
COPY .env.production /www/.env
#RUN chown -R nobody.nobody /var/www
RUN chown -R nobody.nobody /www/storage
RUN chown -R nobody.nobody /www/bootstrap/cache

RUN chmod 777 /www/storage
RUN mkdir /www/storage/logs
RUN chmod 777 /www/storage/logs
RUN chmod 777 /www/bootstrap/cache

RUN php /usr/local/bin/composer dump-autoload --no-dev --ignore-platform-reqs

USER nobody

EXPOSE 8080



# Let supervisord start nginx & php-fpm
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]

# Configure a healthcheck to validate that everything is up&running
HEALTHCHECK --timeout=10s CMD curl --silent --fail http://127.0.0.1:8080/fpm-ping

#docker exec -i -t 12bot_web_1 /bin/bash
