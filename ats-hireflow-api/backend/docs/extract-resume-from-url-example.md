# Extract Resume from URL - Implementation Example

## Overview

This document provides a complete implementation example of the new `extractResumeFromUrl` functionality that allows extracting candidate profile information directly from a resume URL without requiring a pre-existing candidate record.

## Implementation Summary

### 1. New Request Validation Class

**File:** `app/Http/Requests/ExtractResumeFromUrlRequest.php`

- Validates resume URL format and accessibility
- Checks supported file formats (PDF, DOC, DOCX)
- Validates file size (max 10MB)
- Ensures URL is accessible with proper content type

### 2. Enhanced Service Method

**File:** `app/Services/CandidateProfileAnalysisService.php`

- Added `extractResumeFromUrl()` method
- Added `callExternalExtractionServiceFromUrl()` method
- Added `generateMockExtractionDataFromUrl()` method for development

### 3. New Controller Method

**File:** `app/Http/Controllers/Api/CandidateProfileAnalysisController.php`

- Added `extractResumeFromUrl()` method
- Follows same authentication patterns as existing methods
- Returns structured response matching existing format

### 4. New API Route

**File:** `routes/api.php`

```php
Route::post('/extract-resume-from-url', [CandidateProfileAnalysisController::class, 'extractResumeFromUrl']);
```

## Usage Example

### Frontend Integration

```javascript
// Example: Resume URL extraction in React component
const extractResumeInfo = async (resumeUrl) => {
  try {
    setLoading(true);
    
    const response = await fetch('/api/v1/candidate-analysis/extract-resume-from-url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify({ resume_url: resumeUrl })
    });

    const data = await response.json();
    
    if (response.ok) {
      // Pre-populate candidate form with extracted data
      setCandidateData({
        name: data.data.extracted_information.name,
        email: data.data.extracted_information.email,
        phone: data.data.extracted_information.phone,
        address: data.data.extracted_information.address,
        skills: data.data.extracted_information.skills,
        experience: data.data.extracted_information.experience,
        education: data.data.extracted_information.education,
      });
      
      setExtractionSuccess(true);
    } else {
      setError(data.error || 'Extraction failed');
    }
  } catch (error) {
    setError('Network error occurred');
  } finally {
    setLoading(false);
  }
};

// Usage in component
const handleResumeUrlSubmit = (url) => {
  if (isValidUrl(url)) {
    extractResumeInfo(url);
  }
};
```

### API Request Example

```bash
curl -X POST \
  http://localhost:8000/api/v1/candidate-analysis/extract-resume-from-url \
  -H 'Authorization: Bearer YOUR_AUTH_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "resume_url": "https://example.com/resumes/john-doe-resume.pdf"
  }'
```

### Expected Response

```json
{
  "status": "success",
  "message": "Resume extraction from URL completed successfully",
  "data": {
    "extracted_information": {
      "name": "Nguyễn Văn An",
      "email": "<EMAIL>",
      "phone": "+84-90-123-4567",
      "address": "123 Đường Nguyễn Huệ, Quận 1, TP.HCM",
      "skills": [
        "PHP", "Laravel", "JavaScript", "React", "MySQL", "Git"
      ],
      "experience": [
        {
          "company": "Công ty TNHH Công nghệ ABC",
          "position": "Lập trình viên Senior",
          "duration": "2020-2023",
          "description": "Dẫn dắt phát triển các ứng dụng web..."
        }
      ],
      "education": [
        {
          "institution": "Đại học Bách khoa Hà Nội",
          "degree": "Cử nhân Công nghệ Thông tin",
          "graduation_year": "2018",
          "gpa": "3.7"
        }
      ]
    },
    "processing": {
      "started_at": "2025-07-31 10:30:00",
      "completed_at": "2025-07-31 10:30:02",
      "duration_seconds": 2
    },
    "resume_url": "https://example.com/resumes/john-doe-resume.pdf"
  }
}
```

## Key Features

### 1. URL Validation
- **Format validation**: Ensures proper URL format
- **File extension check**: Only allows PDF, DOC, DOCX files
- **Accessibility check**: Verifies URL returns HTTP 200
- **Content type validation**: Checks MIME type headers
- **File size limit**: Maximum 10MB file size

### 2. Error Handling
- **Graceful failures**: Returns structured error responses
- **Detailed logging**: Logs errors for debugging
- **User-friendly messages**: Clear error messages for different failure scenarios

### 3. Security
- **Authentication required**: Uses same auth patterns as existing endpoints
- **Input validation**: Comprehensive URL and file validation
- **Timeout protection**: Configurable timeout for external requests

### 4. Development Support
- **Mock data**: Vietnamese mock data for development/testing
- **Configurable**: Can switch between mock and real AI service
- **Logging**: Comprehensive logging for debugging

## Workflow Comparison

### Traditional Flow (Existing)
1. Create candidate record in database
2. Upload resume file
3. Extract information from existing candidate
4. Update candidate with extracted data

### New URL-Based Flow
1. User provides resume URL
2. Extract information directly from URL
3. Display extracted information for preview
4. User reviews and edits if needed
5. Create candidate record with confirmed data

## Benefits

1. **No Database Pollution**: No candidate records created until confirmed
2. **Better UX**: Immediate preview of extracted information
3. **Flexible**: Works with any accessible resume URL
4. **Consistent**: Uses same extraction service and data format
5. **Secure**: Same authentication and validation patterns

## Configuration

The endpoint uses the same configuration as existing extraction methods:

```env
CANDIDATE_ANALYSIS_API_URL=https://api.your-ai-service.com
CANDIDATE_ANALYSIS_API_KEY=your_api_key_here
CANDIDATE_ANALYSIS_TIMEOUT=30
CANDIDATE_ANALYSIS_USE_MOCK=true  # Development mode
```

## Testing

Comprehensive test suite covers:
- Successful extraction scenarios
- URL validation (format, accessibility, file type)
- File size validation
- Authentication requirements
- Service failure handling
- Multiple file format support

## Next Steps

1. **Frontend Integration**: Implement UI components for URL input and preview
2. **Error Handling**: Add user-friendly error messages and retry mechanisms
3. **Performance**: Monitor extraction times and optimize if needed
4. **Analytics**: Track usage patterns and success rates
5. **Enhancement**: Consider adding batch URL processing capability

This implementation provides a robust foundation for resume preview functionality while maintaining consistency with existing system patterns and security requirements.
