# Candidate Timeline API

## Overview

The Candidate Timeline API provides a comprehensive view of all activities related to a specific candidate in chronological order. This includes status changes, interview activities, communication events, profile updates, and more.

## Endpoint

```
GET /api/v1/candidates/{candidate}/timeline
```

## Authentication

Requires authentication via Sanctum token and `view_candidates` permission.

## Parameters

### Path Parameters
- `candidate` (integer, required) - The candidate ID

### Query Parameters
- `page` (integer, optional) - Page number for pagination (default: 1)
- `per_page` (integer, optional) - Number of activities per page (default: 20, max: 100)

## Response Format

### Success Response (200)

```json
{
  "status": "success",
  "data": {
    "activities": [
      {
        "id": "status_123",
        "type": "status_change",
        "category": "candidate",
        "title": "Status Changed",
        "description": "Status changed from 'applied' to 'screening'",
        "timestamp": "2025-07-28T10:30:00.000Z",
        "user": {
          "id": 1,
          "name": "<PERSON>",
          "email": "<EMAIL>"
        },
        "metadata": {
          "old_status": "applied",
          "new_status": "screening",
          "notes": "Moving to screening phase"
        },
        "icon": "status-change",
        "color": "yellow"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 45,
      "last_page": 3,
      "from": 1,
      "to": 20
    }
  }
}
```

## Activity Types

### 1. Status Changes (`status_change`)
- **Source**: `candidate_status_history` table
- **Description**: Tracks all candidate status transitions
- **Metadata**: `old_status`, `new_status`, `notes`

### 2. Interview Activities (`interview_scheduled`, `interview_feedback`)
- **Source**: `interviews` and `interview_feedback` tables
- **Description**: Interview scheduling, completion, and feedback submission
- **Metadata**: Interview details, feedback scores, recommendations

### 3. Communication Events (`communication`)
- **Source**: `messages` table
- **Description**: Emails, SMS, and other communications sent to/from candidate
- **Metadata**: Message type, status, subject, recipient

### 4. Profile Analysis (`profile_analysis`)
- **Source**: `candidate_profile_analyses` table
- **Description**: AI analysis completion with scores
- **Metadata**: Analysis scores, type, completion status

### 5. Profile Updates (`profile_updated`, `notes_updated`)
- **Source**: Spatie ActivityLog
- **Description**: Changes to candidate profile fields and notes
- **Metadata**: Field name, old/new values

### 6. Candidate Creation (`candidate_created`)
- **Source**: Candidate model
- **Description**: Initial candidate profile creation
- **Metadata**: Source, position, application date

## Activity Structure

Each activity contains:

- `id` - Unique identifier for the activity
- `type` - Activity type (see types above)
- `category` - Activity category (`candidate`, `interview`, `communication`)
- `title` - Human-readable title
- `description` - Detailed description
- `timestamp` - ISO 8601 timestamp
- `user` - User who performed the action (id, name, email)
- `metadata` - Type-specific additional data
- `icon` - Suggested icon for UI display
- `color` - Suggested color for UI display

## Color Coding

### Status Colors
- `sourced` - blue
- `applied` - indigo
- `screening` - yellow
- `interview` - purple
- `offer` - orange
- `hired` - green
- `rejected` - red

### Message Status Colors
- `sent` - green
- `delivered` - blue
- `read` - purple
- `failed` - red
- `queued` - yellow
- `draft` - gray

### Score Colors
- 80+ - green (excellent)
- 60-79 - blue (good)
- 40-59 - yellow (fair)
- <40 - red (poor)

## Error Responses

### 403 Unauthorized
```json
{
  "message": "Unauthorized"
}
```

### 404 Not Found
```json
{
  "message": "No query results for model [App\\Models\\Candidate] {id}"
}
```

### 500 Server Error
```json
{
  "status": "error",
  "message": "Failed to retrieve candidate timeline",
  "error": "Detailed error message"
}
```

## Usage Examples

### Basic Request
```bash
curl -H "Authorization: Bearer {token}" \
     -H "Accept: application/json" \
     "https://api.example.com/api/v1/candidates/123/timeline"
```

### With Pagination
```bash
curl -H "Authorization: Bearer {token}" \
     -H "Accept: application/json" \
     "https://api.example.com/api/v1/candidates/123/timeline?page=2&per_page=10"
```

## Implementation Notes

- Activities are sorted by timestamp in descending order (most recent first)
- The timeline aggregates data from multiple sources for comprehensive tracking
- Pagination is implemented using Laravel's Collection slice method
- All timestamps are returned in ISO 8601 format
- User information is included where available, with fallback to "System"
- The API respects Laravel's authorization policies
