# Candidate Notes Implementation

## Overview

This document describes the implementation of the enhanced candidate notes system that supports structured JSON data instead of simple text notes.

## Database Changes

### Migration: `2025_07_26_021555_add_notes_json_column_to_candidates_table.php`

- **Purpose**: Convert the existing `notes` column from TEXT to JSON-compatible format
- **Approach**: Creates a new JSON column, migrates data, then replaces the old column
- **Data Structure**: Each note is stored as an object with the following fields:
  - `created_at`: ISO timestamp when the note was created
  - `created_by`: Name of the user who created the note
  - `created_id`: ID of the user who created the note
  - `content`: The actual note content/text
  - `updated_at`: ISO timestamp when the note was last updated (optional)
  - `updated_by`: Name of the user who last updated the note (optional)
  - `updated_id`: ID of the user who last updated the note (optional)

### Example JSON Structure

```json
[
  {
    "created_at": "2025-07-26T02:15:00.000Z",
    "created_by": "<PERSON>",
    "created_id": 1,
    "content": "Candi<PERSON> has excellent Laravel experience and strong communication skills.",
    "updated_at": null,
    "updated_by": null,
    "updated_id": null
  },
  {
    "created_at": "2025-07-26T03:20:00.000Z",
    "created_by": "Jane Smith",
    "created_id": 2,
    "content": "Follow-up interview scheduled for next week."
  }
]
```

## Model Changes

### Candidate Model (`app/Models/Candidate.php`)

#### Updated Casts
```php
protected $casts = [
    // ... existing casts
    'notes' => 'array',
];
```

#### New Helper Methods

1. **`getNotesListAttribute(): array`**
   - Returns the notes array or empty array if null

2. **`addNote(string $content, User $user): void`**
   - Adds a new note to the candidate
   - Automatically sets creation timestamp and user information

3. **`updateNote(int $noteIndex, string $content, User $user): bool`**
   - Updates an existing note at the specified index
   - Adds update timestamp and user information
   - Returns true if successful, false if note doesn't exist

4. **`removeNote(int $noteIndex): bool`**
   - Removes a note at the specified index
   - Re-indexes the array to maintain consistency
   - Returns true if successful, false if note doesn't exist

5. **`hasNotes(): bool`**
   - Returns true if the candidate has any notes

6. **`getNotesCount(): int`**
   - Returns the number of notes for the candidate

7. **`getLatestNote(): ?array`**
   - Returns the most recently added note or null if no notes exist

## API Changes

### Request Validation

#### StoreCandidateRequest & UpdateCandidateRequest
```php
'notes' => 'nullable|array',
'notes.*.content' => 'required|string|max:2000',
'notes.*.created_by' => 'nullable|string|max:255',
'notes.*.created_id' => 'nullable|integer|exists:users,id',
'notes.*.created_at' => 'nullable|date',
```

### Response Format

#### CandidateResource
The notes field now returns a structured array:

```json
{
  "notes": [
    {
      "id": 0,
      "content": "Candidate has excellent Laravel experience...",
      "created_at": "2025-07-26T02:15:00.000Z",
      "created_by": "John Doe",
      "created_id": 1,
      "updated_at": null,
      "updated_by": null,
      "updated_id": null
    }
  ]
}
```

### Controller Updates

#### CandidateController (`app/Http/Controllers/Api/CandidateController.php`)

The `syncCandidateData` method now handles notes processing:
- Validates note structure
- Sets default values for missing fields
- Ensures proper user attribution
- Maintains data integrity

## API Usage Examples

### Creating a Candidate with Notes

```json
POST /api/v1/candidates
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "position": "Senior Developer",
  "applied_date": "2025-07-26",
  "notes": [
    {
      "content": "Excellent technical skills demonstrated in the interview."
    }
  ]
}
```

### Updating Candidate Notes

```json
PATCH /api/v1/candidates/1
{
  "notes": [
    {
      "content": "Initial assessment: Strong Laravel background.",
      "created_at": "2025-07-26T02:15:00.000Z",
      "created_by": "HR Manager",
      "created_id": 1
    },
    {
      "content": "Follow-up: Scheduled technical interview for next week."
    }
  ]
}
```

### Retrieving Candidate with Notes

```json
GET /api/v1/candidates/1

Response:
{
  "status": "success",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "notes": [
      {
        "id": 0,
        "content": "Initial assessment: Strong Laravel background.",
        "created_at": "2025-07-26T02:15:00.000Z",
        "created_by": "HR Manager",
        "created_id": 1,
        "updated_at": null,
        "updated_by": null,
        "updated_id": null
      }
    ]
  }
}
```

## Migration Notes

- The migration safely converts existing text notes to the new JSON structure
- Existing notes are preserved with "System Migration" as the creator
- The migration is reversible for rollback scenarios
- Data integrity is maintained throughout the process

## Backward Compatibility

- Existing API clients will continue to work
- The notes field now returns an array instead of a string
- Clients should be updated to handle the new structure
- The migration preserves all existing note content

## Testing

The implementation has been tested to ensure:
- ✅ Existing data is properly migrated
- ✅ New notes can be created with proper structure
- ✅ API responses include correctly formatted notes
- ✅ Model helper methods work as expected
- ✅ Validation rules prevent invalid data
- ✅ Database constraints are maintained
