# Extract Resume from URL API Documentation

## Overview

The Extract Resume from URL API endpoint allows extracting candidate profile information directly from a resume file URL without requiring a pre-existing candidate record. This enables resume preview and analysis functionality before candidate creation.

## Endpoint Details

### Extract Resume Information from URL

**Endpoint:** `POST /api/v1/candidate-analysis/extract-resume-from-url`

**Authentication:** Required (<PERSON><PERSON>)

**Description:** Extracts candidate information (name, email, phone, skills, experience, education) from a resume file at the provided URL.

#### Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `resume_url` | string | Yes | URL of the resume file to extract information from |

#### Request Validation

- **URL Format:** Must be a valid URL format
- **URL Length:** Maximum 2048 characters
- **File Extension:** Must be one of: `pdf`, `doc`, `docx`
- **URL Accessibility:** URL must be accessible (returns HTTP 200)
- **Content Type:** Must be a supported MIME type:
  - `application/pdf`
  - `application/msword`
  - `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
  - `application/octet-stream` (for binary files)
- **File Size:** Maximum 10MB

#### Request Example

```json
{
  "resume_url": "https://example.com/resumes/candidate-resume.pdf"
}
```

#### Success Response (200 OK)

```json
{
  "status": "success",
  "message": "Resume extraction from URL completed successfully",
  "data": {
    "extracted_information": {
      "name": "Nguyễn Văn An",
      "email": "<EMAIL>",
      "phone": "+84-90-123-4567",
      "address": "123 Đường Nguyễn Huệ, Quận 1, TP.HCM",
      "skills": [
        "PHP",
        "Laravel",
        "JavaScript",
        "React",
        "MySQL",
        "Git",
        "Docker",
        "AWS",
        "Giải quyết vấn đề",
        "Lãnh đạo nhóm"
      ],
      "experience": [
        {
          "company": "Công ty TNHH Công nghệ ABC",
          "position": "Lập trình viên Senior",
          "duration": "2020-2023",
          "description": "Dẫn dắt phát triển các ứng dụng web sử dụng Laravel và React, quản lý team 5 người, tham gia thiết kế kiến trúc hệ thống"
        },
        {
          "company": "Tập đoàn Phần mềm XYZ",
          "position": "Lập trình viên Full-stack",
          "duration": "2018-2020",
          "description": "Phát triển ứng dụng full-stack và APIs, tham gia các dự án lớn của công ty, làm việc với công nghệ mới"
        }
      ],
      "education": [
        {
          "institution": "Đại học Bách khoa Hà Nội",
          "degree": "Cử nhân Công nghệ Thông tin",
          "graduation_year": "2018",
          "gpa": "3.7"
        }
      ]
    },
    "processing": {
      "started_at": "2025-07-31 10:30:00",
      "completed_at": "2025-07-31 10:30:02",
      "duration_seconds": 2
    },
    "resume_url": "https://example.com/resumes/candidate-resume.pdf"
  }
}
```

#### Error Response - Validation Failed (422 Unprocessable Entity)

```json
{
  "status": "error",
  "message": "Failed to extract resume information from URL",
  "error": "External extraction service failed: Invalid file format",
  "data": {
    "processing": {
      "started_at": "2025-07-31 10:30:00",
      "completed_at": "2025-07-31 10:30:01",
      "duration_seconds": 1,
      "error_message": "External extraction service failed: Invalid file format"
    },
    "resume_url": "https://example.com/resumes/invalid-file.txt"
  }
}
```

#### Error Response - URL Validation Failed (422 Unprocessable Entity)

```json
{
  "message": "The given data was invalid.",
  "errors": {
    "resume_url": [
      "The resume URL is not accessible or contains an unsupported file format."
    ]
  }
}
```

#### Error Response - Server Error (500 Internal Server Error)

```json
{
  "status": "error",
  "message": "Failed to extract resume information from URL",
  "error": "Connection timeout"
}
```

#### Error Response - Unauthorized (401 Unauthorized)

```json
{
  "message": "Unauthenticated."
}
```

## Usage Examples

### JavaScript/Fetch

```javascript
const extractResumeFromUrl = async (resumeUrl) => {
  try {
    const response = await fetch('/api/v1/candidate-analysis/extract-resume-from-url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify({
        resume_url: resumeUrl
      })
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('Extraction successful:', data.data.extracted_information);
      return data.data.extracted_information;
    } else {
      console.error('Extraction failed:', data.error);
      throw new Error(data.error);
    }
  } catch (error) {
    console.error('Request failed:', error);
    throw error;
  }
};

// Usage
extractResumeFromUrl('https://example.com/resume.pdf')
  .then(extractedInfo => {
    // Use extracted information for candidate preview
    populateCandidateForm(extractedInfo);
  })
  .catch(error => {
    // Handle error
    showErrorMessage(error.message);
  });
```

### cURL

```bash
curl -X POST \
  https://your-api-domain.com/api/v1/candidate-analysis/extract-resume-from-url \
  -H 'Authorization: Bearer YOUR_AUTH_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "resume_url": "https://example.com/resumes/candidate-resume.pdf"
  }'
```

## Integration Notes

### Frontend Integration

1. **Resume Preview Flow:**
   - User provides resume URL
   - Call this endpoint to extract information
   - Display extracted information for review
   - Allow user to edit/confirm before creating candidate

2. **Error Handling:**
   - Handle URL validation errors gracefully
   - Provide clear feedback for unsupported file formats
   - Show loading states during extraction process

3. **Security Considerations:**
   - Validate URLs on frontend before sending
   - Implement proper error boundaries
   - Don't expose sensitive error details to users

### Backend Integration

1. **Service Configuration:**
   - Configure external AI service credentials
   - Set appropriate timeout values
   - Enable/disable mock data for development

2. **Monitoring:**
   - Log extraction attempts and results
   - Monitor API response times
   - Track success/failure rates

## Differences from Standard Extract Resume

| Feature | Standard Extract Resume | Extract Resume from URL |
|---------|------------------------|-------------------------|
| **Input** | Candidate ID (existing record) | Resume URL (no record needed) |
| **Database** | Creates analysis record | No database record created |
| **Use Case** | Analyze existing candidate | Preview before candidate creation |
| **Response** | Full analysis resource | Simplified extracted data |
| **Persistence** | Stored in database | Temporary extraction only |

## Rate Limiting

- Standard API rate limits apply
- Consider implementing specific limits for extraction endpoints
- Monitor usage to prevent abuse

## Configuration

### Environment Variables

```env
# External AI Service Configuration
CANDIDATE_ANALYSIS_API_URL=https://api.your-ai-service.com
CANDIDATE_ANALYSIS_API_KEY=your_api_key_here
CANDIDATE_ANALYSIS_TIMEOUT=30
CANDIDATE_ANALYSIS_USE_MOCK=true  # Set false in production
```

## Testing

Run the feature tests:

```bash
php artisan test --filter ExtractResumeFromUrlTest
```

The test suite covers:
- Successful extraction
- URL format validation
- File extension validation
- URL accessibility validation
- File size validation
- Authentication requirements
- Service failure handling
- Multiple file format support
