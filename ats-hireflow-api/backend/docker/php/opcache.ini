[opcache]
; OPcache configuration for production performance

; Enable OPcache
opcache.enable = 1
opcache.enable_cli = 1

; Memory settings
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 10000

; Performance settings
opcache.revalidate_freq = 2
opcache.validate_timestamps = 0
opcache.save_comments = 1
opcache.fast_shutdown = 1

; File cache settings
opcache.file_cache = /tmp/opcache
opcache.file_cache_only = 0
opcache.file_cache_consistency_checks = 1

; Optimization settings
opcache.optimization_level = 0x7FFFBFFF
opcache.enable_file_override = 1
opcache.blacklist_filename = /var/www/html/opcache-blacklist.txt

; JIT settings (PHP 8.0+)
opcache.jit_buffer_size = 64M
opcache.jit = 1255
