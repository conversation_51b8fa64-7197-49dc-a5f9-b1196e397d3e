[PHP]
; Production PHP configuration for HireFlow ATS

; Error handling
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php_errors.log
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT

; Memory and execution limits
memory_limit = 256M
max_execution_time = 60
max_input_time = 60

; File uploads
file_uploads = On
upload_max_filesize = 10M
max_file_uploads = 20
post_max_size = 12M

; Session configuration
session.save_handler = redis
session.save_path = "tcp://redis:6379"
session.gc_maxlifetime = 7200
session.cookie_lifetime = 0
session.cookie_secure = 1
session.cookie_httponly = 1
session.cookie_samesite = "Lax"

; Security
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off

; Date and timezone
date.timezone = Asia/Ho_Chi_Minh

; Realpath cache
realpath_cache_size = 4096K
realpath_cache_ttl = 600

; Output buffering
output_buffering = 4096

; Character encoding
default_charset = "UTF-8"
mbstring.internal_encoding = UTF-8
mbstring.http_output = UTF-8
