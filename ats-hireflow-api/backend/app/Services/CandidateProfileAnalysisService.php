<?php

namespace App\Services;

use App\Models\Candidate;
use App\Models\JobPosting;
use App\Models\CandidateProfileAnalysis;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class CandidateProfileAnalysisService
{
    protected string $externalApiUrl;
    protected string $apiKey;
    protected int $timeout;
    protected bool $useMockData;

    public function __construct()
    {
        $this->externalApiUrl = config('services.candidate_analysis.api_url', '');
        $this->apiKey = config('services.candidate_analysis.api_key', '');
        $this->timeout = config('services.candidate_analysis.timeout', 30);
        $this->useMockData = config('services.candidate_analysis.use_mock', true);
    }

    /**
     * Extract information from candidate's resume
     */
    public function extractResumeInformation(int $candidateId, ?int $userId = null): CandidateProfileAnalysis
    {
        $candidate = Candidate::findOrFail($candidateId);

        // Create analysis record
        $analysis = CandidateProfileAnalysis::create([
            'candidate_id' => $candidateId,
            'created_by' => $userId ?? auth()->id(),
            'analysis_type' => 'resume_extraction',
            'status' => 'pending',
            'analysis_started_at' => now(),
        ]);

        try {
            // Update status to processing
            $analysis->update(['status' => 'processing']);
            // Extract information from resume
            $extractedData = $this->useMockData
                ? $this->generateMockExtractionData($candidate)
                : $this->callExternalExtractionService($candidate);

            if (isset($extractedData['output']) &&  $extractedData['output'] != null) {

                $extractedData = $extractedData['output'];
                $analysis->update([
                    'status' => 'completed',
                    'external_service_id' =>  'analysis_' . uniqid(),
                    'extracted_name' => $extractedData['name'],
                    'extracted_email' => $extractedData['email'],
                    'extracted_phone' => $extractedData['phone'],
                    'extracted_address' => $extractedData['address'],
                    'extracted_skills' => $extractedData['skills'],
                    'extracted_experience' => $extractedData['experience'],
                    'extracted_education' => $extractedData['education'],
                    'analysis_completed_at' => now(),
                ]);

                Log::info('Resume extraction completed successfully', [
                    'candidate_id' => $candidateId,
                    'analysis_id' => $analysis->id,
                ]);
            } else {
                $analysis->update([
                    'status' => 'failed',
                    'error_message' => json_encode($extractedData),
                    'analysis_completed_at' => now(),
                ]);
            }
            // Update analysis with extracted data

        } catch (Exception $e) {
            // Update analysis with error
            $analysis->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'analysis_completed_at' => now(),
            ]);

            Log::error('Resume extraction failed', [
                'candidate_id' => $candidateId,
                'analysis_id' => $analysis->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }

        return $analysis->fresh();
    }

    /**
     * Generate AI analysis for candidate
     */
    public function generateAIAnalysis(int $candidateId, ?int $jobPostingId = null, ?int $userId = null): CandidateProfileAnalysis
    {
        $candidate = Candidate::findOrFail($candidateId);
        $jobPosting = $jobPostingId ? JobPosting::findOrFail($jobPostingId) : null;

        // Create analysis record
        $analysis = CandidateProfileAnalysis::create([
            'candidate_id' => $candidateId,
            'job_posting_id' => $jobPostingId,
            'created_by' => $userId ?? auth()->id(),
            'analysis_type' => $jobPostingId ? 'job_matching' : 'ai_analysis',
            'status' => 'pending',
            'analysis_started_at' => now(),
        ]);

        try {
            // Update status to processing
            $analysis->update(['status' => 'processing']);

            // Generate AI analysis
            $analysisData = $this->useMockData
                ? $this->generateMockAnalysisData($candidate, $jobPosting)
                : $this->callExternalAnalysisService($candidate, $jobPosting);

            if (isset($analysisData['output']) &&  $analysisData['output'] != null) {

                $analysisData = $analysisData['output'];
                // Update analysis with AI results
                $analysis->update([
                    'status' => 'completed',
                    'external_service_id' => 'analysis_' . uniqid(),
                    'ai_summary' => $analysisData['summary'],
                    'strengths' => $analysisData['strengths'],
                    'weaknesses' => $analysisData['weaknesses'],
                    'improvement_areas' => $analysisData['improvement_areas'],
                    'recommendations' => $analysisData['recommendations'],
                    'overall_score' => $analysisData['scores']['overall'],
                    'skills_score' => $analysisData['scores']['skills'],
                    'experience_score' => $analysisData['scores']['experience'],
                    'education_score' => $analysisData['scores']['education'],
                    'cultural_fit_score' => $analysisData['scores']['cultural_fit'],
                    'job_match_details' => $analysisData['job_match_details'] ?? null,
                    'missing_requirements' => $analysisData['missing_requirements'] ?? null,
                    'matching_criteria' => $analysisData['matching_criteria'] ?? null,
                    'analysis_completed_at' => now(),
                ]);

                // update candidate ai score
                if ($analysisData['scores']['overall'] != null) {
                    $candidate->update([
                        'ai_score' => $analysisData['scores']['overall'],
                    ]);
                }



                Log::info('AI analysis completed successfully', [
                    'candidate_id' => $candidateId,
                    'job_posting_id' => $jobPostingId,
                    'analysis_id' => $analysis->id,
                ]);
            } else {
                $analysis->update([
                    'status' => 'failed',
                    'error_message' => json_encode($analysisData),
                    'analysis_completed_at' => now(),
                ]);
            }
        } catch (Exception $e) {
            // Update analysis with error
            $analysis->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'analysis_completed_at' => now(),
            ]);

            Log::error('AI analysis failed', [
                'candidate_id' => $candidateId,
                'job_posting_id' => $jobPostingId,
                'analysis_id' => $analysis->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }

        return $analysis->fresh();
    }

    /**
     * Call external service for resume extraction
     */
    protected function callExternalExtractionService(Candidate $candidate): array
    {
        if (empty($candidate->resume_url)) {
            throw new Exception('Candidate does not have a resume URL');
        }

        $response = Http::timeout($this->timeout)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])
            ->get($this->externalApiUrl . '/extract-resume', [
                'cv' => $candidate->resume_url,
                'candidate_id' => $candidate->id,
            ]);

        if (!$response->successful()) {
            throw new Exception('External extraction service failed: ' . $response->body());
        }

        return $response->json();
    }

    /**
     * Call external service for AI analysis
     */
    protected function callExternalAnalysisService(Candidate $candidate, ?JobPosting $jobPosting = null): array
    {
        $payload = [
            'candidate' => [
                'id' => $candidate->id,
                'name' => $candidate->name,
                'email' => $candidate->email,
                'position' => $candidate->position,
                'experience' => $candidate->experience,
                'skills' => $candidate->skills ?? [],
                'resume_url' => $candidate->resume_url,
            ],
        ];

        if ($jobPosting) {
            $payload['job_posting'] = [
                'id' => $jobPosting->id,
                'title' => $jobPosting->title,
                'description' => $jobPosting->description,
                'requirements' => $jobPosting->requirements ?? [],
                'skills' => $jobPosting->skills ?? [],
            ];
        }

        $endpoint = $jobPosting ? '/analyze-job-match' : '/analyze-candidate';

        $response = Http::timeout($this->timeout)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])
            ->post($this->externalApiUrl . $endpoint, $payload);

        if (!$response->successful()) {
            throw new Exception('External analysis service failed: ' . $response->body());
        }

        return $response->json();
    }

    /**
     * Generate mock extraction data for development (Vietnamese)
     */
    protected function generateMockExtractionData(Candidate $candidate): array
    {
        // Simulate processing delay
        sleep(1);

        $vietnameseNames = ['Nguyễn Văn An', 'Trần Thị Bình', 'Lê Hoàng Cường', 'Phạm Minh Đức', 'Hoàng Thị Lan'];
        $vietnameseCompanies = ['Công ty TNHH Công nghệ ABC', 'Tập đoàn Phần mềm XYZ', 'Công ty CP Giải pháp số DEF'];
        $vietnameseUniversities = ['Đại học Bách khoa Hà Nội', 'Đại học Công nghệ Thông tin', 'Đại học Khoa học Tự nhiên'];

        return [
            'service_id' => 'mock_' . uniqid(),
            'name' => $candidate->name ?: $vietnameseNames[array_rand($vietnameseNames)],
            'email' => $candidate->email ?: strtolower(str_replace([' ', 'ă', 'â', 'ê', 'ô', 'ơ', 'ư', 'đ'], ['', 'a', 'a', 'e', 'o', 'o', 'u', 'd'], $vietnameseNames[0])) . '@example.com',
            'phone' => '+84-' . rand(90, 99) . '-' . rand(100, 999) . '-' . rand(1000, 9999),
            'address' => rand(1, 999) . ' Đường ' . ['Nguyễn Huệ', 'Lê Lợi', 'Trần Hưng Đạo', 'Hai Bà Trưng'][array_rand(['Nguyễn Huệ', 'Lê Lợi', 'Trần Hưng Đạo', 'Hai Bà Trưng'])] . ', Quận ' . rand(1, 12) . ', TP.HCM',
            'skills' => [
                'PHP',
                'Laravel',
                'JavaScript',
                'React',
                'MySQL',
                'Git',
                'Docker',
                'AWS',
                'Giải quyết vấn đề',
                'Lãnh đạo nhóm',
                'Giao tiếp',
                'Làm việc nhóm',
                'Quản lý thời gian',
                'Phân tích hệ thống'
            ],
            'experience' => [
                [
                    'company' => $vietnameseCompanies[0],
                    'position' => 'Lập trình viên Senior',
                    'duration' => '2020-2023',
                    'description' => 'Dẫn dắt phát triển các ứng dụng web sử dụng Laravel và React, quản lý team 5 người, tham gia thiết kế kiến trúc hệ thống'
                ],
                [
                    'company' => $vietnameseCompanies[1],
                    'position' => 'Lập trình viên Full-stack',
                    'duration' => '2018-2020',
                    'description' => 'Phát triển ứng dụng full-stack và APIs, tham gia các dự án lớn của công ty, làm việc với công nghệ mới'
                ]
            ],
            'education' => [
                [
                    'institution' => $vietnameseUniversities[array_rand($vietnameseUniversities)],
                    'degree' => 'Cử nhân Công nghệ Thông tin',
                    'graduation_year' => '2018',
                    'gpa' => '3.7'
                ]
            ]
        ];
    }

    /**
     * Generate mock analysis data for development (Vietnamese)
     */
    protected function generateMockAnalysisData(Candidate $candidate, ?JobPosting $jobPosting = null): array
    {
        // Simulate processing delay
        sleep(2);

        $isJobMatching = !is_null($jobPosting);

        $data = [
            'service_id' => 'mock_analysis_' . uniqid(),
            'summary' => $isJobMatching
                ? "Ứng viên có tiềm năng cao cho vị trí {$jobPosting->title} với kinh nghiệm và kỹ năng phù hợp. Có khả năng đóng góp tích cực cho team và dự án."
                : "Ứng viên có nền tảng kỹ thuật vững chắc và kinh nghiệm làm việc tốt. Thể hiện khả năng học hỏi và phát triển nghề nghiệp.",
            'strengths' => [
                $candidate->name . ' Kỹ năng lập trình vững chắc với các công nghệ yêu cầu',
                'Có kinh nghiệm lãnh đạo và quản lý nhóm',
                'Khả năng giải quyết vấn đề xuất sắc',
                'Kỹ năng giao tiếp và làm việc nhóm tốt',
                'Thái độ học hỏi và cầu tiến',
                'Có khả năng làm việc độc lập và chịu áp lực'
            ],
            'weaknesses' => [
                $candidate->name . ' Chưa có nhiều kinh nghiệm với một số công nghệ mới nổi',
                'Cần bổ sung thêm các chứng chỉ chuyên ngành',
                'Có thể cần thời gian để thích nghi với quy trình công ty',
                'Kỹ năng thuyết trình trước đám đông cần cải thiện'
            ],
            'improvement_areas' => [
                $candidate->name . ' Nâng cao kiến thức về kiến trúc cloud và microservices',
                'Học thêm về quản lý dự án và phương pháp Agile',
                'Cải thiện kỹ năng thuyết trình và public speaking',
                'Tìm hiểu thêm về DevOps và CI/CD',
                'Nâng cao khả năng mentoring và đào tạo junior'
            ],
            'recommendations' => [
                $candidate->name . ' Nên tổ chức phỏng vấn kỹ thuật để đánh giá khả năng coding',
                'Thảo luận về mục tiêu nghề nghiệp và cơ hội phát triển',
                'Xem xét cho các vị trí senior dựa trên kinh nghiệm',
                'Có thể cân nhắc làm tech lead cho các dự án nhỏ',
                'Khuyến khích tham gia các khóa đào tạo nâng cao'
            ],
            'scores' => [
                'overall' => rand(75, 95),
                'skills' => rand(80, 95),
                'experience' => rand(70, 90),
                'education' => rand(75, 85),
                'cultural_fit' => rand(80, 95)
            ]
        ];

        if ($isJobMatching) {
            $data['job_match_details'] = [
                'match_percentage' => rand(75, 95),
                'key_alignments' => [
                    'Kỹ năng lập trình phù hợp với yêu cầu công việc',
                    'Mức độ kinh nghiệm phù hợp với vị trí',
                    'Nền tảng học vấn liên quan đến công việc',
                    'Có kinh nghiệm với các công nghệ được sử dụng trong dự án'
                ]
            ];
            $data['missing_requirements'] = [
                'Chứng chỉ chuyên ngành cụ thể (AWS, Azure)',
                'Kinh nghiệm với framework hoặc công nghệ đặc thù',
                'Kỹ năng quản lý dự án chính thức',
                'Kinh nghiệm làm việc trong môi trường startup/enterprise'
            ];
            $data['matching_criteria'] = [
                'Ngôn ngữ lập trình: PHP, JavaScript, Python',
                'Số năm kinh nghiệm: 3-5 năm',
                'Trình độ học vấn: Cử nhân Công nghệ Thông tin',
                'Kỹ năng mềm: Giao tiếp, làm việc nhóm, giải quyết vấn đề'
            ];
        }

        return $data;
    }
}
