<?php

namespace App\Services;

use App\Models\Message;
use App\Models\MessageTemplate;
use App\Models\Candidate;
use App\Models\JobPosting;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Exception;

class MessageService
{
    /**
     * Send a message
     */
    public function sendMessage(array $data): Message
    {
        // Create message record
        $message = Message::create([
            'type' => $data['type'],
            'status' => (isset($data['status']) ? $data['status'] : Message::STATUS_SENT),
            'category' => $data['category'],
            'candidate_id' => $data['candidate_id'],
            'job_posting_id' => $data['job_posting_id'] ?? null,
            'template_id' => $data['template_id'] ?? null,
            'parent_message_id' => $data['parent_message_id'] ?? null,
            'thread_id' => $data['thread_id'] ?? null,
            'to_email' => $data['to_email'] ?? null,
            'to_phone' => $data['to_phone'] ?? null,
            'to_name' => $data['to_name'] ?? null,
            'from_email' => $data['from_email'] ?? config('mail.from.address'),
            'from_name' => $data['from_name'] ?? config('mail.from.name'),
            'subject' => $this->prepareSubject($data),
            'content' => $this->prepareContent($data),
            'priority' => $data['priority'] ?? 5,
            'scheduled_at' => $data['scheduled_at'] ?? null,
            'metadata' => $data['metadata'] ?? null,
            'created_by' => auth()->id(),
        ]);

        if ($data['status'] === 'draft') {
            return $message;
        }

        // If scheduled, mark as queued
        if (isset($data['scheduled_at'])) {
            $message->markAsQueued();
        } else {
            // Send immediately
            $this->processMessage($message);
        }

        return $message;
    }

    /**
     * Send bulk messages using template
     */
    public function sendBulkMessages(
        int $templateId,
        array $candidateIds,
        ?int $jobPostingId = null,
        ?string $scheduledAt = null,
        int $priority = 5
    ): array {
        $template = MessageTemplate::findOrFail($templateId);
        $jobPosting = $jobPostingId ? JobPosting::find($jobPostingId) : null;
        $candidates = Candidate::whereIn('id', $candidateIds)->get();

        $results = ['success' => [], 'failed' => []];

        foreach ($candidates as $candidate) {
            try {
                $templateData = $this->prepareTemplateData($candidate, $jobPosting);

                $messageData = [
                    'type' => $template->type,
                    'category' => $template->category,
                    'candidate_id' => $candidate->id,
                    'job_posting_id' => $jobPostingId,
                    'template_id' => $templateId,
                    'to_email' => $candidate->email,
                    'to_phone' => $candidate->phone,
                    'to_name' => $candidate->name,
                    'priority' => $priority,
                    'scheduled_at' => $scheduledAt,
                    'template_data' => $templateData,
                ];

                $message = $this->sendMessage($messageData);
                $results['success'][] = $message;
            } catch (Exception $e) {
                $results['failed'][] = [
                    'candidate_id' => $candidate->id,
                    'candidate_name' => $candidate->name,
                    'error' => $e->getMessage(),
                ];

                Log::error('Bulk message failed for candidate', [
                    'candidate_id' => $candidate->id,
                    'template_id' => $templateId,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $results;
    }

    /**
     * Process message for sending
     */
    protected function processMessage(Message $message): void
    {
        try {
            $message->markAsQueued();

            switch ($message->type) {
                case Message::TYPE_EMAIL:
                    $this->sendEmail($message);
                    break;
                case Message::TYPE_SMS:
                    $this->sendSMS($message);
                    break;
                case Message::TYPE_NOTE:
                    // Notes are just stored, not sent
                    $message->markAsSent();
                    break;
            }
        } catch (Exception $e) {
            $message->markAsFailed($e->getMessage());
            throw $e;
        }
    }

    /**
     * Send email message
     */
    protected function sendEmail(Message $message): void
    {
        try {
            // In a real implementation, you would use Laravel's Mail facade
            // For now, we'll simulate sending

            // Simulate email sending delay
            sleep(1);

            // Mark as sent
            $message->markAsSent();

            // Simulate delivery confirmation (in real implementation, this would come from webhook)
            $message->markAsDelivered();

            Log::info('Email sent successfully', [
                'message_id' => $message->id,
                'to_email' => $message->to_email,
                'subject' => $message->subject,
            ]);
        } catch (Exception $e) {
            Log::error('Email sending failed', [
                'message_id' => $message->id,
                'to_email' => $message->to_email,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Send SMS message
     */
    protected function sendSMS(Message $message): void
    {
        try {
            // In a real implementation, you would integrate with SMS provider
            // For now, we'll simulate sending

            // Simulate SMS sending delay
            sleep(1);

            // Mark as sent
            $message->markAsSent();

            // Simulate delivery confirmation
            $message->markAsDelivered();

            Log::info('SMS sent successfully', [
                'message_id' => $message->id,
                'to_phone' => $message->to_phone,
                'content_length' => strlen($message->content),
            ]);
        } catch (Exception $e) {
            Log::error('SMS sending failed', [
                'message_id' => $message->id,
                'to_phone' => $message->to_phone,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Prepare subject from template or direct input
     */
    protected function prepareSubject(array $data): ?string
    {

        if (isset($data['subject'])) {
            $templateHelper = new MessageTemplate();
            $subject = $templateHelper->renderTemplate($data['subject'], $data['template_data']);
            return $subject;
        }


        if (isset($data['template_id']) && isset($data['template_data'])) {
            $template = MessageTemplate::find($data['template_id']);
            if ($template) {
                return $template->renderSubject($data['template_data']);
            }
        }

        return null;
    }

    /**
     * Prepare content from template or direct input
     */
    protected function prepareContent(array $data): string
    {

        if (isset($data['content'])) {

            $templateHelper = new MessageTemplate();
            $content = $templateHelper->renderTemplate($data['content'], $data['template_data']);

            return $content;
        }

        if (isset($data['template_id'])) {
            $template = MessageTemplate::find($data['template_id']);
            if ($template) {
                $templateData = $data['template_data'] ?? $this->prepareTemplateData(
                    Candidate::find($data['candidate_id']),
                    isset($data['job_posting_id']) ? JobPosting::find($data['job_posting_id']) : null
                );
                return $template->renderContent($templateData);
            }
        }

        return $data['content'] ?? '';
    }

    /**
     * Prepare template data for rendering
     */
    protected function prepareTemplateData(?Candidate $candidate, ?JobPosting $jobPosting = null): array
    {
        $data = [];

        if ($candidate) {
            $data = array_merge($data, [
                'candidate_name' => $candidate->name,
                'candidate_email' => $candidate->email,
                'candidate_phone' => $candidate->phone,
                'candidate_position' => $candidate->position,
            ]);
        }

        if ($jobPosting) {
            $data = array_merge($data, [
                'job_title' => $jobPosting->title,
                'job_department' => $jobPosting->department,
                'job_location' => $jobPosting->location,
                'job_type' => $jobPosting->type,
            ]);
        }

        // Add common data
        $data = array_merge($data, [
            'company_name' => config('app.name', 'Công ty'),
            'current_date' => now()->format('d/m/Y'),
            'current_time' => now()->format('H:i'),
            'recruiter_name' => auth()->user()?->name ?? 'Nhà tuyển dụng',
            'recruiter_email' => auth()->user()?->email ?? config('mail.from.address'),
        ]);

        return $data;
    }
}
