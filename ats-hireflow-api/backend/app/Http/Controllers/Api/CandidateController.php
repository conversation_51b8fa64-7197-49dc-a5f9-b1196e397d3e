<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreCandidateRequest;
use App\Http\Requests\UpdateCandidateRequest;
use App\Http\Resources\CandidateResource;
use App\Models\Candidate;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class CandidateController extends Controller
{
    /**
     * Display a listing of candidates with filtering and pagination.
     */
    public function index(Request $request)
    {
        if (!auth()->user()->can('view_candidates')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $candidates = QueryBuilder::for(Candidate::class)
            ->allowedFilters([
                'name',
                'email',
                'position',
                'status',
                'source',
                'location',
                AllowedFilter::exact('job_posting_id'),
                AllowedFilter::exact('assigned_to'),
                AllowedFilter::exact('created_by'),
                AllowedFilter::scope('high_rated'),
                AllowedFilter::scope('high_ai_score'),
                AllowedFilter::callback('skills', function ($query, $value) {
                    $skills = is_array($value) ? $value : [$value];
                    foreach ($skills as $skill) {
                        $query->whereJsonContains('skills', $skill);
                    }
                }),
                AllowedFilter::callback('rating_min', function ($query, $value) {
                    $query->where('rating', '>=', $value);
                }),
                AllowedFilter::callback('ai_score_min', function ($query, $value) {
                    $query->where('ai_score', '>=', $value);
                }),
                AllowedFilter::callback('applied_date_from', function ($query, $value) {
                    $query->where('applied_date', '>=', $value);
                }),
                AllowedFilter::callback('applied_date_to', function ($query, $value) {
                    $query->where('applied_date', '<=', $value);
                }),
            ])
            ->allowedSorts([
                'name',
                'email',
                'position',
                'status',
                'applied_date',
                'rating',
                'ai_score',
                'created_at',
                'updated_at',
            ])
            ->allowedIncludes([
                'jobPosting',
                'createdBy',
                'assignedTo',
                'interviews',
                'statusHistory',
            ])
            ->defaultSort('-created_at')
            ->paginate($request->get('per_page', 15));

        return CandidateResource::collection($candidates);
    }

    /**
     * Get comprehensive timeline of all activities related to a specific candidate.
     *
     * @param Candidate $candidate
     * @return JsonResponse
     */
    public function timeline(Candidate $candidate): JsonResponse
    {
        if (!auth()->user()->can('view_candidates')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            $perPage = request()->get('per_page', 20);
            $activities = $this->getTimelineActivities($candidate);


            // Sort by timestamp (most recent first)
            $activities = $activities->sortByDesc('timestamp');

            // Paginate the results
            $currentPage = request()->get('page', 1);
            $offset = ($currentPage - 1) * $perPage;
            $paginatedActivities = $activities->slice($offset, $perPage)->values();

            return response()->json([
                'status' => 'success',
                'data' => [
                    'activities' => $paginatedActivities,
                    'pagination' => [
                        'current_page' => (int) $currentPage,
                        'per_page' => (int) $perPage,
                        'total' => $activities->count(),
                        'last_page' => (int) ceil($activities->count() / $perPage),
                        'from' => $offset + 1,
                        'to' => min($offset + $perPage, $activities->count()),
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve candidate timeline',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get all timeline activities for a candidate
     *
     * @param Candidate $candidate
     * @return \Illuminate\Support\Collection
     */
    private function getTimelineActivities(Candidate $candidate): \Illuminate\Support\Collection
    {
        $activities = collect();

        // 1. Status Changes from CandidateStatusHistory
        $statusChanges = $candidate->statusHistory()
            ->with('changedBy')
            ->orderBy('created_at', 'desc')
            ->get();

        foreach ($statusChanges as $statusChange) {
            $activities->push([
                'id' => 'status_' . $statusChange->id,
                'type' => 'status_change',
                'category' => 'candidate',
                'title' => 'Status Changed',
                'description' => "Status changed from '{$statusChange->old_status}' to '{$statusChange->new_status}'",
                'timestamp' => $statusChange->created_at->toISOString(),
                'user' => [
                    'id' => $statusChange->changedBy->id,
                    'name' => $statusChange->changedBy->name,
                    'email' => $statusChange->changedBy->email,
                ],
                'metadata' => [
                    'old_status' => $statusChange->old_status,
                    'new_status' => $statusChange->new_status,
                    'notes' => $statusChange->notes,
                ],
                'icon' => 'status-change',
                'color' => $this->getStatusColor($statusChange->new_status),
            ]);
        }

        // 2. Interview Activities
        $interviews = $candidate->interviews()
            ->with(['interviewer.user', 'feedback.interviewer'])
            ->orderBy('created_at', 'desc')
            ->get();



        foreach ($interviews as $interview) {


            // Interview creation
            $activities->push([
                'id' => 'interview_created_' . $interview->id,
                'type' => 'interview_scheduled',
                'category' => 'interview',
                'title' => 'Interview Scheduled',
                'description' => "Interview scheduled for {$interview->date->format('M d, Y')} at {$interview->time->format('H:i')}",
                'timestamp' => $interview->created_at->toISOString(),
                'user' => [
                    'id' => $interview->createdBy->id ?? null,
                    'name' => $interview->createdBy->name ?? 'System',
                    'email' => $interview->createdBy->email ?? null,
                ],
                'metadata' => [
                    'interview_id' => $interview->id,
                    'date' => $interview->date->format('Y-m-d'),
                    'time' => $interview->time->format('H:i'),
                    'type' => $interview->type,
                    'interview_type' => $interview->interview_type,
                    'round' => $interview->round,
                    'interviewer' => $interview->interviewer->name ?? null,
                ],
                'icon' => 'calendar',
                'color' => 'blue',
            ]);

            // Interview feedback
            if ($interview->feedback) {
                foreach (array($interview->feedback) as $feedback) {

                    if (isset($feedback->id))
                        $activities->push([
                            'id' => 'feedback_' . $feedback->id ?? null,
                            'type' => 'interview_feedback',
                            'category' => 'interview',
                            'title' => 'Interview Feedback Submitted',
                            'description' => "Feedback submitted by {$feedback->interviewer->name} \n {$feedback->comments}",
                            'timestamp' => $feedback->created_at->toISOString(),
                            'user' => [
                                'id' => $feedback->interviewer->user->id ?? null,
                                'name' => $feedback->interviewer->name,
                                'email' => $feedback->interviewer->email ?? null,
                            ],
                            'metadata' => [
                                'feedback_id' => $feedback->id ?? null,
                                'interview_id' => $interview->id ?? null,
                                'rating' => $feedback->rating,
                                'recommend' => $feedback->recommend,
                                'technical_score' => $feedback->technical_score,
                                'communication_score' => $feedback->communication_score,
                                'cultural_fit_score' => $feedback->cultural_fit_score,
                                'overall_score' => $feedback->overall_score,
                                'next_round_recommendation' => $feedback->next_round_recommendation,
                            ],
                            'icon' => 'feedback',
                            'color' => $feedback->recommend ? 'green' : 'orange',
                        ]);
                }
            }
        }

        // 3. Communication Events (Messages)
        $messages = \App\Models\Message::where('candidate_id', $candidate->id)
            ->with('createdBy')
            ->orderBy('created_at', 'desc')
            ->get();

        foreach ($messages as $message) {
            $activities->push([
                'id' => 'message_' . $message->id,
                'type' => 'communication',
                'category' => 'communication',
                'title' => $this->getMessageTitle($message),
                'description' => $this->getMessageDescription($message),
                'timestamp' => $message->created_at->toISOString(),
                'user' => [
                    'id' => $message->createdBy->id ?? null,
                    'name' => $message->createdBy->name ?? 'System',
                    'email' => $message->createdBy->email ?? null,
                ],
                'metadata' => [
                    'message_id' => $message->id,
                    'type' => $message->type,
                    'category' => $message->category,
                    'status' => $message->status,
                    'subject' => $message->subject,
                    'to_email' => $message->to_email,
                    'sent_at' => $message->sent_at?->toISOString(),
                ],
                'icon' => $this->getMessageIcon($message->type),
                'color' => $this->getMessageColor($message->status),
            ]);
        }

        // 4. Profile Analysis Activities
        $analyses = \App\Models\CandidateProfileAnalysis::where('candidate_id', $candidate->id)
            ->with('createdBy')
            ->orderBy('created_at', 'desc')
            ->get();

        foreach ($analyses as $analysis) {
            $activities->push([
                'id' => 'analysis_' . $analysis->id,
                'type' => 'profile_analysis',
                'category' => 'candidate',
                'title' => 'Profile Analysis Completed',
                'description' => "AI analysis completed with overall score: {$analysis->overall_score}%",
                'timestamp' => $analysis->analysis_completed_at?->toISOString() ?? $analysis->created_at->toISOString(),
                'user' => [
                    'id' => $analysis->createdBy->id ?? null,
                    'name' => $analysis->createdBy->name ?? 'AI System',
                    'email' => $analysis->createdBy->email ?? null,
                ],
                'metadata' => [
                    'analysis_id' => $analysis->id,
                    'analysis_type' => $analysis->analysis_type,
                    'overall_score' => $analysis->overall_score,
                    'skills_score' => $analysis->skills_score,
                    'experience_score' => $analysis->experience_score,
                    'education_score' => $analysis->education_score,
                    'cultural_fit_score' => $analysis->cultural_fit_score,
                ],
                'icon' => 'ai-analysis',
                'color' => $this->getScoreColor($analysis->overall_score),
            ]);
        }

        // 5. Activity Log Events (from Spatie ActivityLog)
        $activityLogs = \Spatie\Activitylog\Models\Activity::where('subject_type', 'App\\Models\\Candidate')
            ->where('subject_id', $candidate->id)
            ->with('causer')
            ->orderBy('created_at', 'desc')
            ->get();

        foreach ($activityLogs as $log) {
            // Skip if we already have specific handlers for these events
            if (in_array($log->description, ['updated', 'created']) && $log->event === 'updated') {
                $changes = $log->properties['attributes'] ?? [];
                $old = $log->properties['old'] ?? [];

                // Handle specific field changes
                foreach ($changes as $field => $newValue) {
                    $oldValue = $old[$field] ?? null;

                    if ($field === 'notes' && $this->isNotesChange($oldValue, $newValue)) {
                        $activities->push([
                            'id' => 'notes_' . $log->id . '_' . $field,
                            'type' => 'notes_updated',
                            'category' => 'candidate',
                            'title' => 'Notes Updated',
                            'description' => 'Candidate notes were updated',
                            'timestamp' => $log->created_at->toISOString(),
                            'user' => [
                                'id' => $log->causer->id ?? null,
                                'name' => $log->causer->name ?? 'System',
                                'email' => $log->causer->email ?? null,
                            ],
                            'metadata' => [
                                'field' => $field,
                                'old_value' => $oldValue,
                                'new_value' => $newValue,
                            ],
                            'icon' => 'notes',
                            'color' => 'blue',
                        ]);
                    } elseif (!in_array($field, ['status', 'updated_at'])) {
                        // Handle other profile updates
                        $activities->push([
                            'id' => 'profile_' . $log->id . '_' . $field,
                            'type' => 'profile_updated',
                            'category' => 'candidate',
                            'title' => 'Profile Updated',
                            'description' => "Updated {$field}",
                            'timestamp' => $log->created_at->toISOString(),
                            'user' => [
                                'id' => $log->causer->id ?? null,
                                'name' => $log->causer->name ?? 'System',
                                'email' => $log->causer->email ?? null,
                            ],
                            'metadata' => [
                                'field' => $field,
                                'old_value' => $oldValue,
                                'new_value' => $newValue,
                            ],
                            'icon' => 'profile-update',
                            'color' => 'gray',
                        ]);
                    }
                }
            }
        }

        // 6. Candidate Creation Event
        $activities->push([
            'id' => 'created_' . $candidate->id,
            'type' => 'candidate_created',
            'category' => 'candidate',
            'title' => 'Candidate Added',
            'description' => 'Candidate profile was created',
            'timestamp' => $candidate->created_at->toISOString(),
            'user' => [
                'id' => $candidate->createdBy->id ?? null,
                'name' => $candidate->createdBy->name ?? 'System',
                'email' => $candidate->createdBy->email ?? null,
            ],
            'metadata' => [
                'source' => $candidate->source,
                'position' => $candidate->position,
                'applied_date' => $candidate->applied_date?->format('Y-m-d'),
            ],
            'icon' => 'user-plus',
            'color' => 'green',
        ]);

        return $activities;
    }

    /**
     * Get color for status
     */
    private function getStatusColor(string $status): string
    {
        return match ($status) {
            'sourced' => 'blue',
            'applied' => 'indigo',
            'screening' => 'yellow',
            'interview' => 'purple',
            'offer' => 'orange',
            'hired' => 'green',
            'rejected' => 'red',
            default => 'gray'
        };
    }

    /**
     * Get message title based on message type and category
     */
    private function getMessageTitle(\App\Models\Message $message): string
    {
        return match ($message->category) {
            'interview' => 'Interview Communication',
            'offer' => 'Offer Communication',
            'feedback' => 'Feedback Request',
            'reminder' => 'Reminder Sent',
            'rejection' => 'Rejection Notice',
            default => 'Communication Sent'
        };
    }

    /**
     * Get message description
     */
    private function getMessageDescription(\App\Models\Message $message): string
    {
        $type = ucfirst($message->type);
        $subject = $message->subject ? ": {$message->subject}" : '';
        return "{$type} sent to {$message->to_email}{$subject}";
    }

    /**
     * Get message icon based on type
     */
    private function getMessageIcon(string $type): string
    {
        return match ($type) {
            'email' => 'mail',
            'sms' => 'phone',
            'note' => 'note',
            default => 'message'
        };
    }

    /**
     * Get message color based on status
     */
    private function getMessageColor(string $status): string
    {
        return match ($status) {
            'sent' => 'green',
            'delivered' => 'blue',
            'read' => 'purple',
            'failed' => 'red',
            'queued' => 'yellow',
            'draft' => 'gray',
            default => 'gray'
        };
    }

    /**
     * Get color based on score
     */
    private function getScoreColor(?int $score): string
    {
        if ($score === null) return 'gray';

        return match (true) {
            $score >= 80 => 'green',
            $score >= 60 => 'blue',
            $score >= 40 => 'yellow',
            default => 'red'
        };
    }

    /**
     * Check if notes field has meaningful changes
     */
    private function isNotesChange($oldValue, $newValue): bool
    {
        // Convert to arrays if they're JSON strings
        $oldNotes = is_string($oldValue) ? json_decode($oldValue, true) : $oldValue;
        $newNotes = is_string($newValue) ? json_decode($newValue, true) : $newValue;

        // Compare the actual content
        return json_encode($oldNotes) !== json_encode($newNotes);
    }

    /**
     * Store a newly created candidate.
     */
    public function store(StoreCandidateRequest $request): JsonResponse
    {
        if (!auth()->user()->can('create_candidates')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            DB::beginTransaction();

            $candidate = Candidate::create([
                ...$request->validated(),
                'created_by' => $request->user()->id,
            ]);

            // Create related records
            $this->syncCandidateData($candidate, $request);

            // Log status history
            $candidate->statusHistory()->create([
                'old_status' => null,
                'new_status' => $candidate->status,
                'changed_by' => $request->user()->id,
                'notes' => 'Initial candidate creation',
            ]);

            DB::commit();

            $candidate->load([
                'jobPosting',
                'createdBy',
                'assignedTo',
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Candidate created successfully',
                'data' => new CandidateResource($candidate),
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create candidate',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified candidate.
     */
    public function show(Candidate $candidate): JsonResponse
    {
        if (!auth()->user()->can('view_candidates')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $candidate->load([
            'jobPosting',
            'createdBy',
            'assignedTo',
            'interviews.interviewer.user',
            'statusHistory.changedBy',
        ]);

        return response()->json([
            'status' => 'success',
            'data' => new CandidateResource($candidate),
        ]);
    }

    /**
     * Update the specified candidate.
     */
    public function update(UpdateCandidateRequest $request, Candidate $candidate): JsonResponse
    {
        if (!auth()->user()->can('edit_candidates')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            DB::beginTransaction();

            $oldStatus = $candidate->status;
            $candidate->update($request->validated());

            // Update related records
            $this->syncCandidateData($candidate, $request);

            // Log status change if status was updated
            if ($request->has('status') && $oldStatus !== $candidate->status) {
                $candidate->statusHistory()->create([
                    'old_status' => $oldStatus,
                    'new_status' => $candidate->status,
                    'changed_by' => $request->user()->id,
                    'notes' => $request->get('status_notes', 'Status updated via API'),
                ]);
            }

            DB::commit();

            $candidate->load([
                'jobPosting',
                'createdBy',
                'assignedTo',
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Candidate updated successfully',
                'data' => new CandidateResource($candidate),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update candidate',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified candidate.
     */
    public function destroy(Candidate $candidate): JsonResponse
    {
        if (!auth()->user()->can('delete_candidates')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            $candidate->delete();

            return response()->json([
                'status' => 'success',
                'message' => 'Candidate deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete candidate',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update candidate status.
     */
    public function updateStatus(Request $request, Candidate $candidate): JsonResponse
    {
        if (!auth()->user()->can('manage_candidate_status')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'status' => 'required|in:sourced,applied,screening,interview,offer,hired,rejected',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $oldStatus = $candidate->status;

            $candidate->updateStatus(
                $request->status,
                $request->user(),
                $request->notes
            );

            return response()->json([
                'status' => 'success',
                'message' => 'Candidate status updated successfully',
                'data' => [
                    'old_status' => $oldStatus,
                    'new_status' => $candidate->status,
                    'updated_at' => $candidate->updated_at->format('Y-m-d H:i:s'),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update candidate status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Upload candidate resume.
     */
    public function uploadResume(Request $request, Candidate $candidate): JsonResponse
    {
        if (!auth()->user()->can('edit_candidates')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'resume' => 'required|file|mimes:pdf,doc,docx|max:10240', // 10MB max
        ]);

        try {
            if ($request->hasFile('resume')) {
                // Delete old resume if exists
                if ($candidate->resume_url) {
                    Storage::delete($candidate->resume_url);
                }

                // Store new resume
                $path = $request->file('resume')->store('resumes', 'public');

                $candidate->update([
                    'resume_url' => $path,
                ]);

                return response()->json([
                    'status' => 'success',
                    'message' => 'Resume uploaded successfully',
                    'data' => [
                        'resume_url' => $candidate->resume_url,
                        'resume_full_url' => Storage::url($candidate->resume_url),
                    ],
                ]);
            }

            return response()->json([
                'status' => 'error',
                'message' => 'No resume file provided',
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to upload resume',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Trigger AI analysis for candidate.
     */
    public function triggerAiAnalysis(Candidate $candidate): JsonResponse
    {
        if (!auth()->user()->can('edit_candidates')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            // This would integrate with your AI service
            // For now, we'll simulate with a random score
            $aiScore = rand(60, 95);

            $candidate->update([
                'ai_score' => $aiScore,
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'AI analysis completed successfully',
                'data' => [
                    'ai_score' => $candidate->ai_score,
                    'analysis_date' => now()->format('Y-m-d H:i:s'),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to perform AI analysis',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Sync candidate related data (education, work history, skills, tags, notes).
     */
    private function syncCandidateData(Candidate $candidate, Request $request): void
    {
        // Education and work_history are now simple text fields, handled automatically by model update
        // No need for separate sync logic as they are part of the main candidate record

        // Note: education and work_history are now stored as text fields in the candidates table
        // and are updated directly through the model's fillable attributes

        // Sync skills - now stored as JSON array
        if ($request->has('skills')) {
            $skills = is_array($request->skills) ? $request->skills : [];
            // Clean and validate skills
            $skills = array_values(array_unique(array_filter($skills, 'trim')));
            $candidate->skills = $skills;
        }

        // Sync tags - now stored as JSON array
        if ($request->has('tags')) {
            $tags = is_array($request->tags) ? $request->tags : [];
            // Clean and validate tags
            $tags = array_values(array_unique(array_filter($tags, 'trim')));
            $candidate->tags = $tags;
        }

        // Sync notes - now stored as JSON array with structured format
        if ($request->has('notes')) {
            $notes = is_array($request->notes) ? $request->notes : [];
            $processedNotes = [];

            foreach ($notes as $note) {
                if (is_array($note) && !empty($note['content'])) {
                    $processedNotes[] = [
                        'created_at' => $note['created_at'] ?? now()->toISOString(),
                        'created_by' => $note['created_by'] ?? $request->user()->name,
                        'created_id' => $note['created_id'] ?? $request->user()->id,
                        'content' => trim($note['content']),
                        'updated_at' => $note['updated_at'] ?? null,
                        'updated_by' => $note['updated_by'] ?? null,
                        'updated_id' => $note['updated_id'] ?? null,
                    ];
                }
            }

            $candidate->notes = $processedNotes;
        }
    }
}
