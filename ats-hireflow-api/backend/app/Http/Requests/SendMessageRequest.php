<?php

namespace App\Http\Requests;

use App\Models\Message;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SendMessageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type' => [
                'required',
                'string',
                Rule::in([
                    Message::TYPE_EMAIL,
                    Message::TYPE_SMS,
                    Message::TYPE_NOTE,
                ])
            ],
            'category' => [
                'required',
                'string',
                Rule::in([
                    Message::CATEGORY_INTERVIEW,
                    Message::CATEGORY_OFFER,
                    Message::CATEGORY_FEEDBACK,
                    Message::CATEGORY_REMINDER,
                    Message::CATEGORY_REJECTION,
                    Message::CATE<PERSON><PERSON><PERSON>_GENERAL,
                ])
            ],
            'candidate_id' => 'required|exists:candidates,id',
            'job_posting_id' => 'nullable|exists:job_postings,id',
            'template_id' => 'nullable|exists:message_templates,id',
            'parent_message_id' => 'nullable|exists:messages,id',
            'to_email' => 'required_if:type,email|email|max:255',
            'to_phone' => 'required_if:type,sms|string|max:20',
            'to_name' => 'nullable|string|max:255',
            'from_email' => 'nullable|email|max:255',
            'from_name' => 'nullable|string|max:255',
            'subject' => 'required_if:type,email|string|max:500',
            'content' => 'required|string',
            'status' => 'nullable|string',
            'priority' => 'nullable|integer|min:1|max:10',
            'scheduled_at' => 'nullable|date|after:now',
            'metadata' => 'nullable|array',
            'template_data' => 'nullable|array', // Data for template rendering
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'type.required' => 'Loại tin nhắn là bắt buộc.',
            'type.in' => 'Loại tin nhắn không hợp lệ.',
            'category.required' => 'Danh mục tin nhắn là bắt buộc.',
            'category.in' => 'Danh mục tin nhắn không hợp lệ.',
            'candidate_id.required' => 'Ứng viên là bắt buộc.',
            'candidate_id.exists' => 'Ứng viên không tồn tại.',
            'job_posting_id.exists' => 'Công việc không tồn tại.',
            'template_id.exists' => 'Template không tồn tại.',
            'status' => 'Trạng thái tin nhắn là bắt buộc.',
            'parent_message_id.exists' => 'Tin nhắn gốc không tồn tại.',
            'to_email.required_if' => 'Email người nhận là bắt buộc khi gửi email.',
            'to_email.email' => 'Email người nhận không hợp lệ.',
            'to_phone.required_if' => 'Số điện thoại người nhận là bắt buộc khi gửi SMS.',
            'subject.required_if' => 'Tiêu đề là bắt buộc khi gửi email.',
            'content.required' => 'Nội dung tin nhắn là bắt buộc.',
            'priority.min' => 'Độ ưu tiên phải từ 1 đến 10.',
            'priority.max' => 'Độ ưu tiên phải từ 1 đến 10.',
            'scheduled_at.after' => 'Thời gian lên lịch phải sau thời điểm hiện tại.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default priority if not provided
        if (!$this->has('priority')) {
            $this->merge(['priority' => 5]);
        }

        // Set default from_name if not provided
        if (!$this->has('from_name') && $this->type === Message::TYPE_EMAIL) {
            $this->merge(['from_name' => config('app.name', 'ATS System')]);
        }
    }
}
