<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCandidateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('create_candidates');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'position' => 'required|string|max:255',
            'experience' => 'nullable|string|max:100',
            'status' => 'nullable|in:sourced,applied,screening,interview,offer,hired,rejected',
            'applied_date' => 'required|date',
            'source' => 'nullable|string|max:100',
            'location' => 'nullable|string|max:255',
            'salary_expectation_min' => 'nullable|numeric|min:0',
            'salary_expectation_max' => 'nullable|numeric|min:0|gte:salary_expectation_min',
            'salary_currency' => 'nullable|string|max:3',
            'rating' => 'nullable|numeric|min:0|max:5',
            'ai_score' => 'nullable|integer|min:0|max:100',
            'linkedin_url' => 'nullable|url|max:500',
            'github_url' => 'nullable|url|max:500',
            'resume_url' => 'nullable|url|max:500',
            'portfolio_url' => 'nullable|url|max:500',
            'notes' => 'nullable|array',
            'notes.*.content' => 'required|string|max:2000',
            'notes.*.created_by' => 'nullable|string|max:255',
            'notes.*.created_id' => 'nullable|integer|exists:users,id',
            'notes.*.created_at' => 'nullable|date',
            'job_posting_id' => 'nullable|exists:job_postings,id',
            'interviewer_id' => 'nullable|exists:interviewers,id',
            'assigned_to' => 'nullable|exists:users,id',

            // Simplified education and work_history - now just text fields
            'education' => 'nullable|string|max:2000',
            'work_history' => 'nullable|string|max:2000',

            // Simplified skills and tags - now just arrays of strings
            'skills' => 'nullable|array',
            'skills.*' => 'string|max:100|distinct',

            'tags' => 'nullable|array',
            'tags.*' => 'string|max:100|distinct',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Candidate name is required.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please provide a valid email address.',
            'position.required' => 'Position is required.',
            'applied_date.required' => 'Applied date is required.',
            'job_posting_id.exists' => 'Selected job posting does not exist.',
            'salary_expectation_max.gte' => 'Maximum salary must be greater than or equal to minimum salary.',
            'rating.min' => 'Rating must be between 0 and 5.',
            'rating.max' => 'Rating must be between 0 and 5.',
            'ai_score.min' => 'AI score must be between 0 and 100.',
            'ai_score.max' => 'AI score must be between 0 and 100.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        $this->merge([
            'status' => $this->status ?? 'applied',
            'salary_currency' => $this->salary_currency ?? 'VND',
            'applied_date' => $this->applied_date ?? now()->format('Y-m-d'),
        ]);
    }
}
