<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Spatie\Activitylog\Traits\LogsActivity;

class Interview extends Model
{
    /** @use HasFactory<\Database\Factories\InterviewFactory> */
    use HasFactory, LogsActivity;

    protected $fillable = [
        'candidate_id',
        'job_posting_id',
        'interviewer_id',
        'date',
        'time',
        'duration',
        'type',
        'status',
        'meeting_link',
        'meeting_password',
        'location',
        'address',
        'notes',
        'agenda',
        'round',
        'interview_type',
        'reminder_sent',
        'created_by',
    ];

    protected $casts = [
        'date' => 'date',
        'time' => 'datetime:H:i',
        'duration' => 'integer',
        'agenda' => 'array',
        'reminder_sent' => 'boolean',
        'round' => 'integer',
    ];

    protected static $logAttributes = ['status', 'date', 'time', 'type'];
    protected static $logOnlyDirty = true;

    public function getActivitylogOptions(): \Spatie\Activitylog\LogOptions
    {
        return \Spatie\Activitylog\LogOptions::defaults()
            ->logOnly(['status', 'date', 'time', 'type'])
            ->logOnlyDirty();
    }

    // Relationships
    public function candidate(): BelongsTo
    {
        return $this->belongsTo(Candidate::class);
    }

    public function jobPosting(): BelongsTo
    {
        return $this->belongsTo(JobPosting::class);
    }

    public function interviewer(): BelongsTo
    {
        return $this->belongsTo(Interviewer::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function feedback(): HasOne
    {
        return $this->hasOne(InterviewFeedback::class);
    }

    // Scopes
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeToday($query)
    {
        return $query->whereDate('date', today());
    }

    public function scopeUpcoming($query)
    {
        return $query->where('date', '>=', today())
            ->where('status', 'scheduled')
            ->orderBy('date')
            ->orderBy('time');
    }

    public function scopeByInterviewer($query, $interviewerId)
    {
        return $query->where('interviewer_id', $interviewerId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('interview_type', $type);
    }

    // Accessors & Mutators
    public function getDateTimeAttribute(): string
    {
        return $this->date->format('Y-m-d') . ' ' . $this->time->format('H:i');
    }

    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'scheduled' => 'bg-primary',
            'completed' => 'bg-success',
            'cancelled' => 'bg-danger',
            'rescheduled' => 'bg-warning',
            'no-show' => 'bg-gray-500',
            default => 'bg-gray-500'
        };
    }

    public function getTypeIconAttribute(): string
    {
        return match ($this->type) {
            'video' => 'video-camera',
            'phone' => 'phone',
            'in-person' => 'location-marker',
            default => 'calendar'
        };
    }

    // Helper methods
    public function isUpcoming(): bool
    {
        $interviewDateTime = $this->date->setTimeFromTimeString($this->time->format('H:i:s'));
        return $interviewDateTime->isFuture() && $this->status === 'scheduled';
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['scheduled', 'rescheduled']);
    }

    public function canBeRescheduled(): bool
    {
        return in_array($this->status, ['scheduled']);
    }

    public function hasFeedback(): bool
    {
        return $this->feedback()->exists();
    }
}
