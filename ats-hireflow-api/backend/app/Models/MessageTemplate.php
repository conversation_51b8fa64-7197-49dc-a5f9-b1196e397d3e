<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class MessageTemplate extends Model
{
    /** @use HasFactory<\Database\Factories\MessageTemplateFactory> */
    use HasFactory, LogsActivity;

    // Template categories
    const CATEGORY_INTERVIEW = 'interview';
    const CATEGORY_OFFER = 'offer';
    const CATEGORY_FEEDBACK = 'feedback';
    const CATEGORY_REMINDER = 'reminder';
    const CATEGORY_REJECTION = 'rejection';
    const CATEGORY_WELCOME = 'welcome';

    // Template types
    const TYPE_EMAIL = 'email';
    const TYPE_SMS = 'sms';

    protected $fillable = [
        'name',
        'subject',
        'content',
        'variables',
        'category',
        'type',
        'is_active',
        'language',
        'version',
        'parent_template_id',
        'created_by',
    ];

    protected $casts = [
        'variables' => 'array',
        'is_active' => 'boolean',
        'version' => 'integer',
    ];

    protected static $logAttributes = [
        'name',
        'subject',
        'category',
        'type',
        'is_active',
        'language',
        'version'
    ];
    protected static $logOnlyDirty = true;

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'subject', 'category', 'type', 'is_active', 'language', 'version'])
            ->logOnlyDirty();
    }

    // Relationships
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function messages(): HasMany
    {
        return $this->hasMany(Message::class, 'template_id');
    }

    public function parentTemplate(): BelongsTo
    {
        return $this->belongsTo(MessageTemplate::class, 'parent_template_id');
    }

    public function childTemplates(): HasMany
    {
        return $this->hasMany(MessageTemplate::class, 'parent_template_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByLanguage($query, $language)
    {
        return $query->where('language', $language);
    }

    public function scopeLatestVersion($query)
    {
        return $query->orderBy('version', 'desc');
    }

    // Template rendering methods
    public function renderContent(array $data = []): string
    {
        return $this->renderTemplate($this->content, $data);
    }

    public function renderSubject(array $data = []): string
    {
        return $this->renderTemplate($this->subject, $data);
    }

    public function renderTemplate(string $template, array $data = []): string
    {
        $rendered = $template;

        // Replace variables with actual values
        foreach ($data as $key => $value) {
            $placeholder = '{{' . $key . '}}';
            $rendered = str_replace($placeholder, (string) $value, $rendered);
        }

        // Handle conditional blocks {{#if variable}}...{{/if}}
        $rendered = preg_replace_callback(
            '/\{\{#if\s+(\w+)\}\}(.*?)\{\{\/if\}\}/s',
            function ($matches) use ($data) {
                $variable = $matches[1];
                $content = $matches[2];
                return !empty($data[$variable]) ? $content : '';
            },
            $rendered
        );

        // Handle loops {{#each items}}...{{/each}}
        $rendered = preg_replace_callback(
            '/\{\{#each\s+(\w+)\}\}(.*?)\{\{\/each\}\}/s',
            function ($matches) use ($data) {
                $variable = $matches[1];
                $template = $matches[2];
                $result = '';

                if (isset($data[$variable]) && is_array($data[$variable])) {
                    foreach ($data[$variable] as $item) {
                        $itemData = is_array($item) ? $item : ['item' => $item];
                        $result .= $this->renderTemplate($template, $itemData);
                    }
                }

                return $result;
            },
            $rendered
        );

        return $rendered;
    }

    public function getAvailableVariables(): array
    {
        return $this->variables ?? [];
    }

    public function extractVariablesFromContent(): array
    {
        $content = $this->subject . ' ' . $this->content;
        preg_match_all('/\{\{(\w+)\}\}/', $content, $matches);
        return array_unique($matches[1]);
    }

    public function validateVariables(array $data): array
    {
        $required = $this->getAvailableVariables();
        $missing = [];

        foreach ($required as $variable) {
            if (!isset($data[$variable]) || empty($data[$variable])) {
                $missing[] = $variable;
            }
        }

        return $missing;
    }

    public function createNewVersion(): self
    {
        $newTemplate = $this->replicate();
        $newTemplate->version = $this->version + 1;
        $newTemplate->parent_template_id = $this->id;
        $newTemplate->save();

        return $newTemplate;
    }

    public function getLatestVersion(): self
    {
        if ($this->parent_template_id) {
            return $this->parentTemplate->getLatestVersion();
        }

        return $this->childTemplates()->latestVersion()->first() ?? $this;
    }

    public function isLatestVersion(): bool
    {
        return $this->getLatestVersion()->id === $this->id;
    }

    // Static helper methods
    public static function getCategories(): array
    {
        return [
            self::CATEGORY_INTERVIEW => 'Phỏng vấn',
            self::CATEGORY_OFFER => 'Đề nghị công việc',
            self::CATEGORY_FEEDBACK => 'Phản hồi',
            self::CATEGORY_REMINDER => 'Nhắc nhở',
            self::CATEGORY_REJECTION => 'Từ chối',
            self::CATEGORY_WELCOME => 'Chào mừng',
        ];
    }

    public static function getTypes(): array
    {
        return [
            self::TYPE_EMAIL => 'Email',
            self::TYPE_SMS => 'SMS',
        ];
    }

    public function getCategoryNameAttribute(): string
    {
        return self::getCategories()[$this->category] ?? $this->category;
    }

    public function getTypeNameAttribute(): string
    {
        return self::getTypes()[$this->type] ?? $this->type;
    }
}
