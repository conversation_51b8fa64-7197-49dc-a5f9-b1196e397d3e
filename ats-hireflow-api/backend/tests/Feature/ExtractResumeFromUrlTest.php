<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class ExtractResumeFromUrlTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create and authenticate a user
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');
    }

    public function test_can_extract_resume_information_from_url(): void
    {
        // Mock HTTP response for URL validation
        Http::fake([
            'https://example.com/resume.pdf' => Http::response('', 200, [
                'Content-Type' => 'application/pdf',
                'Content-Length' => '1024000', // 1MB
            ]),
        ]);

        $response = $this->postJson('/api/v1/candidate-analysis/extract-resume-from-url', [
            'resume_url' => 'https://example.com/resume.pdf'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'extracted_information' => [
                            'name',
                            'email',
                            'phone',
                            'address',
                            'skills',
                            'experience',
                            'education',
                        ],
                        'processing' => [
                            'started_at',
                            'completed_at',
                            'duration_seconds',
                        ],
                        'resume_url',
                    ]
                ])
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Resume extraction from URL completed successfully',
                ]);

        // Verify the resume URL is included in response
        $this->assertEquals('https://example.com/resume.pdf', $response->json('data.resume_url'));
    }

    public function test_validates_resume_url_format(): void
    {
        $response = $this->postJson('/api/v1/candidate-analysis/extract-resume-from-url', [
            'resume_url' => 'invalid-url'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['resume_url']);
    }

    public function test_validates_resume_url_file_extension(): void
    {
        // Mock HTTP response for unsupported file type
        Http::fake([
            'https://example.com/resume.txt' => Http::response('', 200, [
                'Content-Type' => 'text/plain',
            ]),
        ]);

        $response = $this->postJson('/api/v1/candidate-analysis/extract-resume-from-url', [
            'resume_url' => 'https://example.com/resume.txt'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['resume_url']);
    }

    public function test_validates_resume_url_accessibility(): void
    {
        // Mock HTTP response for inaccessible URL
        Http::fake([
            'https://example.com/nonexistent.pdf' => Http::response('', 404),
        ]);

        $response = $this->postJson('/api/v1/candidate-analysis/extract-resume-from-url', [
            'resume_url' => 'https://example.com/nonexistent.pdf'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['resume_url']);
    }

    public function test_validates_resume_url_file_size(): void
    {
        // Mock HTTP response for oversized file
        Http::fake([
            'https://example.com/large-resume.pdf' => Http::response('', 200, [
                'Content-Type' => 'application/pdf',
                'Content-Length' => '20971520', // 20MB (over 10MB limit)
            ]),
        ]);

        $response = $this->postJson('/api/v1/candidate-analysis/extract-resume-from-url', [
            'resume_url' => 'https://example.com/large-resume.pdf'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['resume_url']);
    }

    public function test_requires_authentication(): void
    {
        // Remove authentication
        $this->app['auth']->forgetGuards();

        $response = $this->postJson('/api/v1/candidate-analysis/extract-resume-from-url', [
            'resume_url' => 'https://example.com/resume.pdf'
        ]);

        $response->assertStatus(401);
    }

    public function test_handles_extraction_service_failure(): void
    {
        // Mock HTTP response for URL validation (success)
        Http::fake([
            'https://example.com/resume.pdf' => Http::response('', 200, [
                'Content-Type' => 'application/pdf',
                'Content-Length' => '1024000',
            ]),
        ]);

        // Force service to use real API (which will fail in test environment)
        config(['services.candidate_analysis.use_mock' => false]);
        config(['services.candidate_analysis.api_url' => 'https://invalid-api-url.com']);

        $response = $this->postJson('/api/v1/candidate-analysis/extract-resume-from-url', [
            'resume_url' => 'https://example.com/resume.pdf'
        ]);

        $response->assertStatus(422)
                ->assertJson([
                    'status' => 'error',
                    'message' => 'Failed to extract resume information from URL',
                ]);
    }

    public function test_supports_different_file_formats(): void
    {
        $supportedFormats = [
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ];

        foreach ($supportedFormats as $extension => $contentType) {
            Http::fake([
                "https://example.com/resume.{$extension}" => Http::response('', 200, [
                    'Content-Type' => $contentType,
                    'Content-Length' => '1024000',
                ]),
            ]);

            $response = $this->postJson('/api/v1/candidate-analysis/extract-resume-from-url', [
                'resume_url' => "https://example.com/resume.{$extension}"
            ]);

            $response->assertStatus(200)
                    ->assertJson([
                        'status' => 'success',
                    ]);
        }
    }
}
