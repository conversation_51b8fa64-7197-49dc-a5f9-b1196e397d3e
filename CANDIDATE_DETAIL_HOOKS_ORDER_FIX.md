# CandidateDetail Hooks Order Fix Summary

## Vấn đề
CandidateDetail component bị lỗi vi phạm Rules of Hooks với các thông báo lỗi:
- "React has detected a change in the order of Hooks"
- "Rendered more hooks than during the previous render"

## Nguyên nhân
Hook `useContextPageTitle` được gọi **sau** các return statements có điều kiện:

```typescript
// TRƯỚC - WRONG: Hook được gọi sau conditional returns
export default function CandidateDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { data: candidateResponse, isLoading, error, refetch } = useCandidate(id || "", "");

  // Loading state - EARLY RETURN!
  if (isLoading) {
    return <LoadingComponent />;
  }

  // Candidate data extraction  
  const candidate = candidateResponse?.data;

  // Hook được gọi SAU early return - VI PHẠM RULES!
  useContextPageTitle("pageTitle.candidates.detail", "name", candidate?.name || "Loading...");

  // Error state - CONDITIONAL RETURN!
  if (error) {
    return <ErrorComponent />;
  }
  
  return <SuccessComponent />;
}
```

Vấn đề này dẫn đến:
- Khi `isLoading = true`: Hook không được gọi (early return)
- Khi `isLoading = false`: Hook được gọi  
- Thứ tự hooks thay đổi giữa các renders → React error

## Giải pháp

Di chuyển tất cả hooks lên đầu component, **trước** mọi conditional logic:

```typescript
// SAU - CORRECT: Tất cả hooks ở đầu component
export default function CandidateDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // API hook
  const { data: candidateResponse, isLoading, error, refetch } = useCandidate(id || "", "");

  // Data extraction
  const candidate = candidateResponse?.data;

  // Page title hook - LUÔN được gọi ở vị trí này
  useContextPageTitle(
    "pageTitle.candidates.detail",
    "name", 
    candidate?.name || "Loading..."
  );

  // Conditional returns sau khi tất cả hooks đã được gọi
  if (isLoading) {
    return <LoadingComponent />;
  }

  if (error) {
    return <ErrorComponent />;
  }

  return <SuccessComponent />;
}
```

## Rules of Hooks được tuân thủ

### ✅ **Consistent Order**
- Tất cả hooks được gọi ở cùng thứ tự mỗi render
- Không có conditional hook calls
- Hooks luôn ở top level của function

### ✅ **Always Called**
```typescript
// Hooks này luôn được gọi bất kể loading/error state
useParams()          // 1
useNavigate()        // 2  
useTranslation()     // 3
useCandidate()       // 4
useContextPageTitle() // 5

// Conditional logic CHỈ ảnh hưởng đến JSX return, không ảnh hưởng hooks
```

### ✅ **Safe Data Access**
```typescript
// candidate?.name fallback đảm bảo page title luôn có giá trị hợp lệ
useContextPageTitle(
  "pageTitle.candidates.detail",
  "name",
  candidate?.name || "Loading..." // Fallback an toàn
);
```

## Benefits

### 🔧 **Technical**
- **No more React warnings**: Hooks order consistency
- **Predictable behavior**: Same execution path every render  
- **Better debugging**: Clear hook call sequence

### 📱 **User Experience**  
- **Faster page titles**: Set immediately khi có data
- **No page title flashing**: Smooth transitions
- **Consistent navigation**: Browser back/forward works correctly

### 🧹 **Code Quality**
- **Rules compliance**: Follows React best practices
- **Maintainable**: Clear separation of hooks vs rendering logic
- **Readable**: Hooks grouped at top, conditional rendering at bottom

## Testing

Để verify fix hoạt động:

1. ✅ **Navigate to candidate detail** - No React warnings
2. ✅ **Refresh page while loading** - No hooks order errors
3. ✅ **Test with invalid candidate ID** - Error state renders without hook violations
4. ✅ **Test page title updates** - Title changes correctly based on candidate name

## Key Takeaway

**Luôn nhớ Rules of Hooks:**
1. **Only call hooks at the top level** - never inside loops, conditions, or nested functions
2. **Only call hooks from React functions** - components or custom hooks  
3. **Call hooks in the same order every time** - maintain consistent execution path

Việc sửa này đảm bảo CandidateDetail component tuân thủ đúng React patterns và không bị lỗi hooks order nữa.
