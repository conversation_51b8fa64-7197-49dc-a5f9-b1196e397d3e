# HireFlow ATS - Documentation

Welcome to the HireFlow ATS documentation. This directory contains comprehensive documentation for both backend and frontend components of the system.

## 📁 Documentation Structure

### 🔧 Backend Documentation
- **[Backend Overview](./backend/README.md)** - Complete backend system documentation
- **[API Documentation](./backend/api/README.md)** - RESTful API endpoints and usage
- **[Database Schema](./backend/database-schema.md)** - Database structure and relationships
- **[Technical Architecture](./backend/technical-architecture.md)** - System architecture overview
- **[Development Plan](./backend/development-plan.md)** - Development roadmap and guidelines

### 🎨 Frontend Documentation
- **[Frontend Overview](./frontend/README.md)** - Complete frontend system documentation
- **[Design System](./frontend/design-system.md)** - UI components and design guidelines
- **[System Guidelines](./frontend/system-guidelines.md)** - Development standards and practices
- **[Components](./frontend/components/README.md)** - UI component library documentation
- **[Features](./frontend/features/README.md)** - Feature-specific implementation guides

## 🚀 Quick Start

### For Developers
1. **Backend Setup**: See [Backend README](./backend/README.md)
2. **Frontend Setup**: See [Frontend README](./frontend/README.md)
3. **API Usage**: Check [API Documentation](./backend/api/README.md)

### For Designers
1. **Design System**: Review [Design System](./frontend/design-system.md)
2. **Component Library**: Explore [Components](./frontend/components/README.md)

## 📋 System Overview

HireFlow ATS is a comprehensive Applicant Tracking System built with:

- **Backend**: Laravel 12.x with RESTful API
- **Frontend**: React with TypeScript and Tailwind CSS
- **Database**: MySQL with optimized schema
- **Authentication**: Laravel Sanctum with Bearer tokens

## 🔗 Key Resources

- [API v2.0.1 Documentation](./backend/api/core-api.md) - Latest API specification
- [Message System API](./backend/api/message-system.md) - Messaging functionality
- [User Management API](./backend/api/user-management.md) - User and role management
- [Design System](./frontend/design-system.md) - Complete UI design guidelines

## 📝 Contributing

When adding new documentation:

1. Place backend-related docs in `docs/backend/`
2. Place frontend-related docs in `docs/frontend/`
3. Update relevant README files with links to new documentation
4. Follow the existing documentation structure and formatting

## 📞 Support

For questions about the documentation or system:

1. Check the relevant section in this documentation
2. Review the API documentation for endpoint-specific questions
3. Consult the technical architecture for system design questions

---

*Last updated: $(date)*
*Documentation structure organized for optimal developer experience*
