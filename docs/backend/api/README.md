# API Documentation

This directory contains comprehensive API documentation for the HireFlow ATS backend system.

## 📋 Available APIs

### 🔌 Core API v2.0.1
**[Core API Documentation](./core-api.md)**
- **51 endpoints** covering all ATS functionality
- Complete CRUD operations for all entities
- Authentication and user management
- Candidate and job posting management
- Application tracking and processing

### 💬 Message System API
**[Message System Documentation](./message-system.md)**
- Message template management
- Direct messaging functionality
- Vietnamese content optimization
- Email and SMS integration
- Message tracking and analytics

### 👥 User Management API
**[User Management Documentation](./user-management.md)**
- User registration and authentication
- Role-based access control
- Permission management
- Profile management
- Team and organization features

### 📝 Interview & Feedback API
**[Interview Feedback Documentation](./interview-feedback.md)**
- Interview scheduling and management
- Comprehensive feedback system
- Rating and scoring mechanisms
- Interview analytics
- Feedback templates

### 👨‍💼 Interviewer API
**[Interviewer Documentation](./interviewer.md)**
- Interviewer-specific endpoints
- Schedule management
- Feedback submission
- Performance tracking
- Availability management

## 🚀 Quick Start

### Base URL
```
http://localhost:8000/api/v1
```

### Authentication
All API endpoints require Bearer token authentication:
```http
Authorization: Bearer {your_access_token}
Content-Type: application/json
Accept: application/json
```

### Getting Started
1. **Authentication**: Use `/auth/login` to get access token
2. **Core Entities**: Start with candidates, jobs, and applications
3. **Advanced Features**: Explore messaging and interview systems
4. **Testing**: Use provided Postman collections

## 📊 API Overview

### Response Format
All APIs follow consistent response structure:

**Success Response:**
```json
{
  "status": "success",
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

**Error Response:**
```json
{
  "status": "error",
  "message": "Error description",
  "errors": {
    // Validation errors (if applicable)
  }
}
```

### Pagination
List endpoints support pagination:
```json
{
  "status": "success",
  "data": {
    "data": [...],
    "current_page": 1,
    "per_page": 15,
    "total": 100,
    "last_page": 7
  }
}
```

## 🔧 Key Features

### Performance Optimizations
- **60-75% reduction** in database queries
- **Simplified JSON structure** for better performance
- **Efficient eager loading** for related data
- **Optimized database indexes** for common queries

### Data Structure Improvements
- **JSON arrays** for candidate skills and tags
- **TEXT fields** for education and work history
- **Simplified relationships** with fewer junction tables
- **Consistent data formats** across all endpoints

### Language Support
- **Full Vietnamese language support** with UTF-8 encoding
- **Localized error messages** and responses
- **Vietnamese-specific validation** for names and addresses
- **Cultural considerations** in data formatting

## 🧪 Testing

### Postman Collections
- **HireFlow_ATS_API_v2.0.1.postman_collection.json** - Complete API collection
- **Pre-configured requests** with sample data
- **Environment variables** for easy testing
- **Automated test scripts** for validation

### Testing Guidelines
1. **Start with authentication** endpoints
2. **Test CRUD operations** for each entity
3. **Verify relationships** between entities
4. **Test error scenarios** and validation
5. **Performance testing** for large datasets

## 📈 Performance Metrics

### Database Optimization
- **Removed 8 obsolete tables** for cleaner architecture
- **Simplified candidate data structure** with JSON fields
- **Optimized job posting relationships** 
- **Efficient query patterns** with proper indexing

### Response Times
- **Average response time**: < 200ms for simple queries
- **Complex queries**: < 500ms with proper optimization
- **Bulk operations**: Optimized for large datasets
- **Real-time features**: WebSocket support for live updates

## 🔐 Security Features

### Authentication & Authorization
- **Laravel Sanctum** for API authentication
- **Role-based permissions** with Spatie Laravel Permission
- **Rate limiting** to prevent abuse
- **CORS configuration** for frontend integration

### Data Protection
- **Input validation** on all endpoints
- **SQL injection prevention** with Eloquent ORM
- **XSS protection** with proper data sanitization
- **Secure password hashing** with bcrypt

## 📝 Development Guidelines

### API Design Principles
1. **RESTful conventions** for endpoint naming
2. **Consistent response formats** across all endpoints
3. **Proper HTTP status codes** for different scenarios
4. **Comprehensive error handling** with meaningful messages
5. **Version management** for backward compatibility

### Adding New Endpoints
1. **Follow existing patterns** for consistency
2. **Use API resources** for response formatting
3. **Implement proper validation** with Form Requests
4. **Add comprehensive tests** for new functionality
5. **Update documentation** with examples and usage

## 🔗 Related Documentation

- [Backend Overview](../README.md)
- [Database Schema](../database-schema.md)
- [Technical Architecture](../technical-architecture.md)
- [Frontend Integration](../../frontend/README.md)

---

*For specific endpoint details, refer to the individual API documentation files above.*
