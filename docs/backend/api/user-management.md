# User Management API Documentation

## Overview

API endpoints for managing users in the HireFlow ATS system. Most endpoints require admin privileges for security.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

All requests must include the Authorization header:

```
Authorization: Bearer {your-token}
```

## Admin Requirements

Most user management endpoints require admin role. Only users with `admin` role can:

- View all users
- Create new users
- Update user roles
- Deactivate users
- View user statistics

## Endpoints

### 1. List Users

Get a paginated list of all users with filtering options.

**Endpoint:** `GET /users`

**Permission:** Admin only

**Query Parameters:**

- `page` (integer, optional) - Page number (default: 1)
- `per_page` (integer, optional) - Items per page (default: 15, max: 50)
- `filter[role]` (string, optional) - Filter by role: admin, recruiter, hiring_manager, interviewer
- `filter[department]` (string, optional) - Filter by department
- `filter[is_active]` (boolean, optional) - Filter by active status
- `filter[name]` (string, optional) - Search by name (partial match)
- `filter[email]` (string, optional) - Search by email (partial match)
- `sort` (string, optional) - Sort by field: name, email, role, department, created_at, last_login_at
- `include` (string, optional) - Include relationships: roles, permissions

**Example Request:**

```bash
GET /api/v1/users?filter[role]=recruiter&filter[is_active]=true&sort=name&include=roles,permissions
```

**Example Response:**

```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "name": "Nguyễn Văn An",
      "email": "<EMAIL>",
      "role": "admin",
      "role_display_name": "Quản trị viên",
      "department": "IT",
      "title": "System Administrator",
      "phone": "0123456789",
      "avatar": null,
      "is_active": true,
      "last_login_at": "2024-07-22 10:30:00",
      "created_at": "2024-01-15 09:00:00",
      "updated_at": "2024-07-22 10:30:00",
      "roles": [
        {
          "id": 1,
          "name": "admin",
          "display_name": "Quản trị viên"
        }
      ],
      "permissions": ["manage_users", "view_candidates", "..."],
      "initials": "NA",
      "is_interviewer": false,
      "statistics": {
        "created_candidates_count": 25,
        "assigned_candidates_count": 10,
        "created_job_postings_count": 5,
        "managed_job_postings_count": 3,
        "recruited_job_postings_count": 8,
        "created_interviews_count": 15
      }
    }
  ],
  "meta": {
    "current_page": 1,
    "last_page": 3,
    "per_page": 15,
    "total": 42,
    "from": 1,
    "to": 15
  },
  "links": {
    "first": "http://localhost:8000/api/v1/users?page=1",
    "last": "http://localhost:8000/api/v1/users?page=3",
    "prev": null,
    "next": "http://localhost:8000/api/v1/users?page=2"
  }
}
```

### 2. Create User

Create a new user and assign role.

**Endpoint:** `POST /users`

**Permission:** Admin only

**Request Body:**

```json
{
  "name": "Trần Thị Bình",
  "email": "<EMAIL>",
  "password": "SecurePassword123",
  "password_confirmation": "SecurePassword123",
  "role": "recruiter",
  "department": "HR",
  "title": "Senior Recruiter",
  "phone": "0987654321",
  "is_active": true
}
```

**Validation Rules:**

- `name`: required, string, max 255 characters
- `email`: required, valid email, unique, max 255 characters
- `password`: required, min 8 characters, confirmed
- `role`: required, one of: admin, recruiter, hiring_manager, interviewer
- `department`: optional, string, max 100 characters
- `title`: optional, string, max 100 characters
- `phone`: optional, string, max 20 characters
- `is_active`: optional, boolean (default: true)

**Example Response:**

```json
{
  "status": "success",
  "message": "Người dùng đã được tạo thành công",
  "data": {
    "id": 15,
    "name": "Trần Thị Bình",
    "email": "<EMAIL>",
    "role": "recruiter",
    "role_display_name": "Nhà tuyển dụng",
    "department": "HR",
    "title": "Senior Recruiter",
    "phone": "0987654321",
    "avatar": null,
    "is_active": true,
    "last_login_at": null,
    "created_at": "2024-07-22 14:30:00",
    "updated_at": "2024-07-22 14:30:00",
    "roles": [
      {
        "id": 2,
        "name": "recruiter",
        "display_name": "Nhà tuyển dụng"
      }
    ],
    "permissions": ["view_candidates", "create_candidates", "..."],
    "initials": "TB",
    "is_interviewer": false
  }
}
```

### 3. Get User Details

Get detailed information about a specific user.

**Endpoint:** `GET /users/{id}`

**Permission:** Admin can view any user, users can view their own profile

**Example Response:**

```json
{
  "status": "success",
  "data": {
    "id": 15,
    "name": "Trần Thị Bình",
    "email": "<EMAIL>",
    "role": "recruiter",
    "role_display_name": "Nhà tuyển dụng",
    "department": "HR",
    "title": "Senior Recruiter",
    "phone": "0987654321",
    "avatar": null,
    "is_active": true,
    "last_login_at": "2024-07-22 09:15:00",
    "created_at": "2024-07-22 14:30:00",
    "updated_at": "2024-07-22 14:30:00",
    "roles": [...],
    "permissions": [...],
    "initials": "TB",
    "is_interviewer": false,
    "statistics": {...}
  }
}
```

### 4. Update User

Update user information.

**Endpoint:** `PUT /users/{id}`

**Permission:** Admin can update any user, users can update their own profile (limited fields)

**Request Body (Admin):**

```json
{
  "name": "Trần Thị Bình Cập Nhật",
  "email": "<EMAIL>",
  "password": "NewPassword123",
  "password_confirmation": "NewPassword123",
  "role": "hiring_manager",
  "department": "HR",
  "title": "HR Manager",
  "phone": "0987654322",
  "is_active": true
}
```

**Request Body (Self-update):**

```json
{
  "name": "Trần Thị Bình Cập Nhật",
  "phone": "0987654322",
  "password": "NewPassword123",
  "password_confirmation": "NewPassword123"
}
```

**Example Response:**

```json
{
  "status": "success",
  "message": "Thông tin người dùng đã được cập nhật",
  "data": {
    "id": 15,
    "name": "Trần Thị Bình Cập Nhật",
    "email": "<EMAIL>",
    "role": "hiring_manager",
    "role_display_name": "Quản lý tuyển dụng",
    "department": "HR",
    "title": "HR Manager",
    "phone": "0987654322",
    "avatar": null,
    "is_active": true,
    "last_login_at": "2024-07-22 09:15:00",
    "created_at": "2024-07-22 14:30:00",
    "updated_at": "2024-07-22 15:45:00",
    "roles": [...],
    "permissions": [...],
    "initials": "TB",
    "is_interviewer": false
  }
}
```

### 5. Deactivate User

Deactivate a user (soft delete to preserve data integrity).

**Endpoint:** `DELETE /users/{id}`

**Permission:** Admin only

**Note:** Users cannot delete themselves. This endpoint deactivates the user instead of hard deletion.

**Example Response:**

```json
{
  "status": "success",
  "message": "Người dùng đã được vô hiệu hóa"
}
```

### 6. Update User Roles

Update a user's role and permissions.

**Endpoint:** `PUT /users/{id}/roles`

**Permission:** Admin only

**Request Body:**

```json
{
  "role": "hiring_manager"
}
```

**Validation Rules:**

- `role`: required, one of: admin, recruiter, hiring_manager, interviewer

**Example Response:**

```json
{
  "status": "success",
  "message": "Quyền người dùng đã được cập nhật thành công",
  "data": {
    "id": 15,
    "name": "Trần Thị Bình",
    "email": "<EMAIL>",
    "role": "hiring_manager",
    "role_display_name": "Quản lý tuyển dụng",
    "department": "HR",
    "title": "Senior Recruiter",
    "phone": "0987654321",
    "avatar": null,
    "is_active": true,
    "last_login_at": "2024-07-22 09:15:00",
    "created_at": "2024-07-22 14:30:00",
    "updated_at": "2024-07-22 16:00:00",
    "roles": [
      {
        "id": 3,
        "name": "hiring_manager",
        "display_name": "Quản lý tuyển dụng"
      }
    ],
    "permissions": ["view_candidates", "edit_candidates", "manage_jobs", "..."],
    "initials": "TB",
    "is_interviewer": false
  }
}
```

### 7. Get Available Roles

Get list of available roles for user assignment.

**Endpoint:** `GET /users/roles`

**Permission:** Admin only

**Example Response:**

```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "name": "admin",
      "display_name": "Quản trị viên"
    },
    {
      "id": 2,
      "name": "recruiter",
      "display_name": "Nhà tuyển dụng"
    },
    {
      "id": 3,
      "name": "hiring_manager",
      "display_name": "Quản lý tuyển dụng"
    },
    {
      "id": 4,
      "name": "interviewer",
      "display_name": "Người phỏng vấn"
    }
  ]
}
```

### 8. Get User Statistics

Get comprehensive user statistics for admin dashboard.

**Endpoint:** `GET /users/statistics`

**Permission:** Admin only

**Example Response:**

```json
{
  "status": "success",
  "data": {
    "total_users": 42,
    "active_users": 38,
    "inactive_users": 4,
    "by_role": {
      "admin": 2,
      "recruiter": 15,
      "hiring_manager": 8,
      "interviewer": 17
    },
    "recent_logins": 25,
    "never_logged_in": 3
  }
}
```

## Error Responses

### 401 Unauthorized

```json
{
  "status": "error",
  "message": "Bạn cần đăng nhập để truy cập tài nguyên này"
}
```

### 403 Forbidden

```json
{
  "status": "error",
  "message": "Chỉ quản trị viên mới có quyền truy cập tài nguyên này"
}
```

### 422 Validation Error

```json
{
  "status": "error",
  "message": "Validation failed",
  "errors": {
    "email": ["Email này đã được sử dụng."],
    "password": ["Mật khẩu phải có ít nhất 8 ký tự."],
    "role": ["Vai trò không hợp lệ."]
  }
}
```

### 500 Server Error

```json
{
  "status": "error",
  "message": "Không thể tạo người dùng mới",
  "error": "Database connection failed"
}
```

## Security Features

### 1. Role-Based Access Control

- Only admin users can access most user management endpoints
- Users can only view/update their own profiles (limited fields)
- Admins cannot delete themselves
- Admins cannot change their own role

### 2. Input Validation

- Comprehensive validation for all input fields
- Vietnamese error messages for better UX
- Password confirmation required
- Email uniqueness validation

### 3. Audit Logging

- All user management actions are logged
- Includes user ID, action type, and timestamp
- Failed attempts are also logged

### 4. Data Protection

- Passwords are hashed using Laravel's Hash facade
- Soft deletion preserves data integrity
- Sensitive data excluded from API responses

## Usage Examples

### Create a new recruiter

```bash
curl -X POST http://localhost:8000/api/v1/users \
  -H "Authorization: Bearer your-admin-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Lê Văn Cường",
    "email": "<EMAIL>",
    "password": "SecurePass123",
    "password_confirmation": "SecurePass123",
    "role": "recruiter",
    "department": "HR",
    "title": "Junior Recruiter",
    "phone": "0123456789"
  }'
```

### Update user role

```bash
curl -X PUT http://localhost:8000/api/v1/users/15/roles \
  -H "Authorization: Bearer your-admin-token" \
  -H "Content-Type: application/json" \
  -d '{
    "role": "hiring_manager"
  }'
```

### Get user statistics

```bash
curl -X GET http://localhost:8000/api/v1/users/statistics \
  -H "Authorization: Bearer your-admin-token"
```

### List users with filtering

```bash
curl -X GET "http://localhost:8000/api/v1/users?filter[role]=recruiter&filter[is_active]=true&sort=name" \
  -H "Authorization: Bearer your-admin-token"
```

## Best Practices

1. **Always validate admin permissions** before allowing user management operations
2. **Use soft deletion** instead of hard deletion to preserve data integrity
3. **Log all user management activities** for audit purposes
4. **Implement rate limiting** for user creation endpoints
5. **Use strong password policies** and enforce password confirmation
6. **Provide clear Vietnamese error messages** for better user experience
7. **Validate email uniqueness** to prevent duplicate accounts
8. **Hash passwords** using Laravel's built-in Hash facade
9. **Implement proper authorization** checks for each endpoint
10. **Use pagination** for large user lists to improve performance

## Integration Notes

### Frontend Integration

- Use the UserResource format for consistent data structure
- Implement proper error handling for all scenarios
- Cache user roles and permissions for better performance
- Show Vietnamese role names in the UI

### Database Considerations

- Ensure proper indexing on frequently queried fields (email, role, is_active)
- Use foreign key constraints for data integrity
- Consider archiving old user data instead of hard deletion
- Monitor query performance for large user datasets

## Testing

### Unit Tests

- Test all validation rules
- Test permission checks
- Test role assignment logic
- Test error handling scenarios

### Integration Tests

- Test complete user creation workflow
- Test role update workflow
- Test authentication and authorization
- Test API response formats

### Security Tests

- Test unauthorized access attempts
- Test SQL injection prevention
- Test XSS prevention
- Test rate limiting effectiveness
