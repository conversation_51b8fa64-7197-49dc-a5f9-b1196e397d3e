# HireFlow ATS - Database Schema Design

## Overview

This document outlines the complete database schema for the HireFlow ATS system, designed to support all features outlined in the API documentation while maintaining data integrity, performance, and scalability.

## Schema Design Principles

1. **Normalization**: Proper normalization to reduce data redundancy
2. **Indexing**: Strategic indexing for query performance
3. **Relationships**: Clear foreign key relationships with cascading rules
4. **Audit Trail**: Comprehensive tracking of data changes
5. **Soft Deletes**: Preserve data integrity with soft deletion
6. **Timestamps**: Automatic created_at and updated_at tracking

## Core Tables

### 1. Users & Authentication

```sql
-- Users table for system authentication
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'recruiter', 'hiring_manager', 'interviewer') NOT NULL DEFAULT 'recruiter',
    department VARCHAR(100) NULL,
    title VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    avatar VARCHAR(500) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP NULL,
    remember_token VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_users_email (email),
    INDEX idx_users_role (role),
    INDEX idx_users_department (department),
    INDEX idx_users_is_active (is_active)
);

-- Personal access tokens for API authentication (Laravel Sanctum)
CREATE TABLE personal_access_tokens (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tokenable_type VARCHAR(255) NOT NULL,
    tokenable_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    token VARCHAR(64) UNIQUE NOT NULL,
    abilities TEXT NULL,
    last_used_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_personal_access_tokens_tokenable (tokenable_type, tokenable_id),
    INDEX idx_personal_access_tokens_token (token)
);

-- User permissions
CREATE TABLE user_permissions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    permission VARCHAR(100) NOT NULL,
    granted_by BIGINT UNSIGNED NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_permission (user_id, permission),
    INDEX idx_user_permissions_user_id (user_id)
);
```

### 2. Jobs Management

```sql
-- Main jobs table
CREATE TABLE jobs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    department VARCHAR(100) NOT NULL,
    location VARCHAR(255) NOT NULL,
    type ENUM('full-time', 'part-time', 'contract', 'internship') NOT NULL DEFAULT 'full-time',
    work_location ENUM('remote', 'onsite', 'hybrid') NOT NULL DEFAULT 'onsite',
    salary_min DECIMAL(15,2) NULL,
    salary_max DECIMAL(15,2) NULL,
    currency VARCHAR(3) DEFAULT 'VND',
    description TEXT NOT NULL,
    education_required TEXT NULL,
    company_culture TEXT NULL,
    status ENUM('draft', 'active', 'paused', 'closed') NOT NULL DEFAULT 'draft',
    priority ENUM('low', 'medium', 'high') NOT NULL DEFAULT 'medium',
    posted_date DATE NULL,
    closing_date DATE NULL,
    hiring_manager_id BIGINT UNSIGNED NULL,
    recruiter_id BIGINT UNSIGNED NULL,
    experience_level ENUM('entry', 'mid', 'senior', 'lead', 'junior') NOT NULL,
    applicant_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    created_by BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,

    FOREIGN KEY (hiring_manager_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (recruiter_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,

    INDEX idx_jobs_status (status),
    INDEX idx_jobs_department (department),
    INDEX idx_jobs_location (location),
    INDEX idx_jobs_type (type),
    INDEX idx_jobs_experience_level (experience_level),
    INDEX idx_jobs_posted_date (posted_date),
    INDEX idx_jobs_hiring_manager (hiring_manager_id),
    INDEX idx_jobs_recruiter (recruiter_id),
    FULLTEXT idx_jobs_search (title, description)
);

-- Job requirements
CREATE TABLE job_requirements (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    job_id BIGINT UNSIGNED NOT NULL,
    requirement_text TEXT NOT NULL,
    is_mandatory BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,

    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
    INDEX idx_job_requirements_job_id (job_id)
);

-- Job responsibilities
CREATE TABLE job_responsibilities (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    job_id BIGINT UNSIGNED NOT NULL,
    responsibility_text TEXT NOT NULL,
    sort_order INT DEFAULT 0,

    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
    INDEX idx_job_responsibilities_job_id (job_id)
);

-- Job benefits
CREATE TABLE job_benefits (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    job_id BIGINT UNSIGNED NOT NULL,
    benefit_text TEXT NOT NULL,
    sort_order INT DEFAULT 0,

    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
    INDEX idx_job_benefits_job_id (job_id)
);

-- Job skills
CREATE TABLE job_skills (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    job_id BIGINT UNSIGNED NOT NULL,
    skill_name VARCHAR(100) NOT NULL,
    importance_level ENUM('required', 'preferred', 'nice-to-have') DEFAULT 'required',
    proficiency_level ENUM('beginner', 'intermediate', 'advanced', 'expert') NULL,

    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
    INDEX idx_job_skills_job_id (job_id),
    INDEX idx_job_skills_skill_name (skill_name)
);

-- Job interview process
CREATE TABLE job_interview_process (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    job_id BIGINT UNSIGNED NOT NULL,
    stage_name VARCHAR(100) NOT NULL,
    stage_description TEXT NULL,
    duration_minutes INT NULL,
    sort_order INT DEFAULT 0,

    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
    INDEX idx_job_interview_process_job_id (job_id)
);
```

### 3. Candidates Management

```sql
-- Main candidates table
CREATE TABLE candidates (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NULL,
    position VARCHAR(255) NOT NULL,
    experience VARCHAR(100) NULL,
    status ENUM('sourced', 'applied', 'screening', 'interview', 'offer', 'hired', 'rejected')
           NOT NULL DEFAULT 'applied',
    applied_date DATE NOT NULL,
    source VARCHAR(100) NULL,
    location VARCHAR(255) NULL,
    salary_expectation_min DECIMAL(15,2) NULL,
    salary_expectation_max DECIMAL(15,2) NULL,
    salary_currency VARCHAR(3) DEFAULT 'VND',
    rating DECIMAL(3,2) NULL CHECK (rating >= 0 AND rating <= 5),
    ai_score INT NULL CHECK (ai_score >= 0 AND ai_score <= 100),
    linkedin_url VARCHAR(500) NULL,
    github_url VARCHAR(500) NULL,
    portfolio_url VARCHAR(500) NULL,
    avatar VARCHAR(500) NULL,
    resume_url VARCHAR(500) NULL,
    notes TEXT NULL,
    job_id BIGINT UNSIGNED NOT NULL,
    created_by BIGINT UNSIGNED NOT NULL,
    assigned_to BIGINT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,

    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE RESTRICT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,

    INDEX idx_candidates_email (email),
    INDEX idx_candidates_status (status),
    INDEX idx_candidates_job_id (job_id),
    INDEX idx_candidates_applied_date (applied_date),
    INDEX idx_candidates_source (source),
    INDEX idx_candidates_location (location),
    INDEX idx_candidates_ai_score (ai_score),
    INDEX idx_candidates_assigned_to (assigned_to),
    FULLTEXT idx_candidates_search (name, email, position)
);

-- Candidate education
CREATE TABLE candidate_education (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    candidate_id BIGINT UNSIGNED NOT NULL,
    institution VARCHAR(255) NOT NULL,
    degree VARCHAR(255) NOT NULL,
    field_of_study VARCHAR(255) NULL,
    start_year YEAR NULL,
    end_year YEAR NULL,
    gpa VARCHAR(10) NULL,
    description TEXT NULL,
    sort_order INT DEFAULT 0,

    FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
    INDEX idx_candidate_education_candidate_id (candidate_id)
);

-- Candidate work history
CREATE TABLE candidate_work_history (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    candidate_id BIGINT UNSIGNED NOT NULL,
    company VARCHAR(255) NOT NULL,
    position VARCHAR(255) NOT NULL,
    start_date DATE NULL,
    end_date DATE NULL,
    is_current BOOLEAN DEFAULT FALSE,
    description TEXT NULL,
    achievements TEXT NULL,
    sort_order INT DEFAULT 0,

    FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
    INDEX idx_candidate_work_history_candidate_id (candidate_id),
    INDEX idx_candidate_work_history_company (company)
);

-- Candidate skills
CREATE TABLE candidate_skills (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    candidate_id BIGINT UNSIGNED NOT NULL,
    skill_name VARCHAR(100) NOT NULL,
    proficiency_level ENUM('beginner', 'intermediate', 'advanced', 'expert') NULL,
    years_of_experience INT NULL,
    is_verified BOOLEAN DEFAULT FALSE,

    FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
    INDEX idx_candidate_skills_candidate_id (candidate_id),
    INDEX idx_candidate_skills_skill_name (skill_name),
    UNIQUE KEY unique_candidate_skill (candidate_id, skill_name)
);

-- Candidate tags
CREATE TABLE candidate_tags (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    candidate_id BIGINT UNSIGNED NOT NULL,
    tag_name VARCHAR(100) NOT NULL,
    created_by BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_candidate_tags_candidate_id (candidate_id),
    INDEX idx_candidate_tags_tag_name (tag_name),
    UNIQUE KEY unique_candidate_tag (candidate_id, tag_name)
);

-- Candidate status history
CREATE TABLE candidate_status_history (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    candidate_id BIGINT UNSIGNED NOT NULL,
    old_status ENUM('sourced', 'applied', 'screening', 'interview', 'offer', 'hired', 'rejected') NULL,
    new_status ENUM('sourced', 'applied', 'screening', 'interview', 'offer', 'hired', 'rejected') NOT NULL,
    changed_by BIGINT UNSIGNED NOT NULL,
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_candidate_status_history_candidate_id (candidate_id),
    INDEX idx_candidate_status_history_created_at (created_at)
);
```

### 4. Interviews Management

```sql
-- Interviewers table
CREATE TABLE interviewers (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    department VARCHAR(100) NULL,
    expertise JSON NULL, -- Array of expertise areas
    is_active BOOLEAN DEFAULT TRUE,
    location VARCHAR(255) NULL,
    max_interviews_per_day INT DEFAULT 4,
    availability JSON NULL, -- Weekly availability schedule
    time_slots JSON NULL, -- Available time slots
    timezone VARCHAR(50) DEFAULT 'Asia/Ho_Chi_Minh',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_interviewer_user (user_id),
    INDEX idx_interviewers_department (department),
    INDEX idx_interviewers_is_active (is_active)
);

-- Main interviews table
CREATE TABLE interviews (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    candidate_id BIGINT UNSIGNED NOT NULL,
    job_id BIGINT UNSIGNED NOT NULL,
    interviewer_id BIGINT UNSIGNED NOT NULL,
    date DATE NOT NULL,
    time TIME NOT NULL,
    duration INT NOT NULL DEFAULT 60, -- Duration in minutes
    type ENUM('video', 'phone', 'in-person') NOT NULL DEFAULT 'video',
    status ENUM('scheduled', 'completed', 'cancelled', 'rescheduled', 'no-show')
           NOT NULL DEFAULT 'scheduled',
    meeting_link VARCHAR(500) NULL,
    meeting_password VARCHAR(100) NULL,
    location VARCHAR(255) NULL,
    address TEXT NULL,
    notes TEXT NULL,
    agenda JSON NULL, -- Interview agenda items
    round INT DEFAULT 1,
    interview_type ENUM('screening', 'technical', 'case-study', 'portfolio', 'cultural', 'final')
                   NOT NULL DEFAULT 'screening',
    reminder_sent BOOLEAN DEFAULT FALSE,
    created_by BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE RESTRICT,
    FOREIGN KEY (interviewer_id) REFERENCES interviewers(id) ON DELETE RESTRICT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,

    INDEX idx_interviews_candidate_id (candidate_id),
    INDEX idx_interviews_job_id (job_id),
    INDEX idx_interviews_interviewer_id (interviewer_id),
    INDEX idx_interviews_date (date),
    INDEX idx_interviews_status (status),
    INDEX idx_interviews_type (type),
    INDEX idx_interviews_interview_type (interview_type)
);

-- Interview feedback
CREATE TABLE interview_feedback (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    interview_id BIGINT UNSIGNED NOT NULL,
    interviewer_id BIGINT UNSIGNED NOT NULL,
    rating DECIMAL(3,2) NULL CHECK (rating >= 0 AND rating <= 5),
    comments TEXT NULL,
    recommend BOOLEAN NULL,
    strengths JSON NULL, -- Array of strengths
    concerns JSON NULL, -- Array of concerns
    next_round_recommendation ENUM('screening', 'technical', 'case-study', 'portfolio', 'cultural', 'final', 'offer', 'reject') NULL,
    technical_score INT NULL CHECK (technical_score >= 0 AND technical_score <= 100),
    communication_score INT NULL CHECK (communication_score >= 0 AND communication_score <= 100),
    cultural_fit_score INT NULL CHECK (cultural_fit_score >= 0 AND cultural_fit_score <= 100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (interview_id) REFERENCES interviews(id) ON DELETE CASCADE,
    FOREIGN KEY (interviewer_id) REFERENCES interviewers(id) ON DELETE RESTRICT,
    UNIQUE KEY unique_interview_feedback (interview_id, interviewer_id),
    INDEX idx_interview_feedback_interview_id (interview_id),
    INDEX idx_interview_feedback_interviewer_id (interviewer_id)
);
```

### 5. Messages & Communications

```sql
-- Message templates
CREATE TABLE message_templates (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    variables JSON NULL, -- Available template variables
    category ENUM('interview', 'offer', 'feedback', 'reminder', 'rejection', 'welcome') NOT NULL,
    type ENUM('email', 'sms') NOT NULL DEFAULT 'email',
    is_active BOOLEAN DEFAULT TRUE,
    created_by BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_message_templates_category (category),
    INDEX idx_message_templates_type (type),
    INDEX idx_message_templates_is_active (is_active)
);

-- Messages
CREATE TABLE messages (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    type ENUM('email', 'note', 'sms') NOT NULL DEFAULT 'email',
    category ENUM('interview', 'offer', 'feedback', 'reminder', 'rejection', 'general') NOT NULL,
    candidate_id BIGINT UNSIGNED NULL,
    job_id BIGINT UNSIGNED NULL,
    template_id BIGINT UNSIGNED NULL,
    to_email VARCHAR(255) NULL,
    to_phone VARCHAR(20) NULL,
    subject VARCHAR(500) NULL,
    content TEXT NOT NULL,
    status ENUM('draft', 'sent', 'delivered', 'read', 'failed') NOT NULL DEFAULT 'draft',
    sent_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    read_at TIMESTAMP NULL,
    error_message TEXT NULL,
    created_by BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE SET NULL,
    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE SET NULL,
    FOREIGN KEY (template_id) REFERENCES message_templates(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,

    INDEX idx_messages_candidate_id (candidate_id),
    INDEX idx_messages_job_id (job_id),
    INDEX idx_messages_type (type),
    INDEX idx_messages_category (category),
    INDEX idx_messages_status (status),
    INDEX idx_messages_sent_at (sent_at),
    INDEX idx_messages_created_by (created_by)
);

-- Message attachments
CREATE TABLE message_attachments (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    message_id BIGINT UNSIGNED NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    INDEX idx_message_attachments_message_id (message_id)
);
```

## Indexes and Performance Optimization

### Primary Indexes

- All tables have auto-incrementing primary keys
- Foreign key constraints with appropriate cascading rules
- Unique constraints for business logic requirements

### Search Indexes

- Full-text search indexes for candidates and jobs
- Composite indexes for common query patterns
- Covering indexes for frequently accessed columns

### Performance Considerations

- Partitioning for large tables (analytics_events, candidate_status_history)
- Archive strategy for old data
- Query optimization with proper indexing
- Connection pooling and read replicas for scaling

## Data Integrity Rules

### Foreign Key Constraints

- Cascade deletes for dependent data
- Restrict deletes for critical references
- Set null for optional references

### Check Constraints

- Rating values between 0-5
- AI scores between 0-100
- Salary values must be positive

### Business Rules

- Candidates must be associated with a job
- Interviews require valid candidate, job, and interviewer
- Status transitions must follow logical flow

This schema provides a robust foundation for the HireFlow ATS system with proper normalization, indexing, and data integrity constraints.
