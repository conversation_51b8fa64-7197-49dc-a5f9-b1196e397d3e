# HireFlow ATS - Laravel Backend Technical Architecture

## 1. Project Overview

HireFlow ATS is a comprehensive Applicant Tracking System built with <PERSON><PERSON> backend and React frontend. The system manages the complete recruitment lifecycle from job posting to candidate hiring with AI-powered features and workflow automation.

## 2. Technology Stack

### Backend Framework
- **<PERSON>vel 11.x** (Latest LTS)
- **PHP 8.3+**
- **MySQL 8.0** (Primary database)
- **Redis** (Caching, sessions, queues)
- **Elasticsearch** (Advanced search capabilities)

### Additional Services
- **n8n** (Workflow automation platform)
- **MinIO/S3** (File storage)
- **Pusher/<PERSON>vel WebSockets** (Real-time notifications)
- **<PERSON>vel Horizon** (Queue monitoring)
- **<PERSON><PERSON> Telescope** (Debugging and monitoring)

### AI/ML Integration
- **OpenAI API** (Resume parsing, candidate analysis)
- **Python microservice** (Custom ML models)
- **<PERSON><PERSON>/Laravel-PDF** (Report generation)

## 3. Database Schema Design

### Core Tables

#### Users & Authentication
```sql
users (id, name, email, password, role, department, permissions, created_at, updated_at)
personal_access_tokens (Laravel Sanctum)
password_reset_tokens
```

#### Candidates Management
```sql
candidates (
    id, name, email, phone, position, experience, skills, status, 
    applied_date, source, location, salary_expectation, rating, 
    ai_score, linkedin_url, github_url, avatar, resume_url, notes, 
    job_id, created_by, created_at, updated_at
)

candidate_education (id, candidate_id, institution, degree, year, gpa)
candidate_work_history (id, candidate_id, company, position, start_date, end_date, description)
candidate_skills (id, candidate_id, skill_name, proficiency_level)
candidate_tags (id, candidate_id, tag_name)
candidate_status_history (id, candidate_id, old_status, new_status, changed_by, notes, created_at)
```

#### Jobs Management
```sql
jobs (
    id, title, department, location, type, work_location, salary_min, 
    salary_max, currency, description, requirements, responsibilities, 
    benefits, skills, status, priority, posted_date, closing_date, 
    hiring_manager_id, recruiter_id, experience_level, education_required, 
    company_culture, interview_process, created_at, updated_at
)

job_requirements (id, job_id, requirement_text, is_mandatory)
job_responsibilities (id, job_id, responsibility_text)
job_benefits (id, job_id, benefit_text)
job_skills (id, job_id, skill_name, importance_level)
job_analytics (id, job_id, applicant_count, view_count, conversion_rate, avg_time_to_hire)
```

#### Interviews Management
```sql
interviews (
    id, candidate_id, job_id, interviewer_id, date, time, duration, 
    type, status, meeting_link, meeting_password, location, address, 
    notes, agenda, round, interview_type, created_at, updated_at
)

interview_feedback (
    id, interview_id, interviewer_id, rating, comments, recommend, 
    strengths, concerns, next_round_recommendation, created_at
)

interviewers (
    id, user_id, department, expertise, is_active, location, 
    max_interviews_per_day, availability, time_slots, created_at, updated_at
)
```

#### Messages & Communications
```sql
messages (
    id, type, category, candidate_id, job_id, template_id, 
    subject, content, status, sent_at, delivered_at, read_at, 
    created_by, created_at, updated_at
)

message_templates (
    id, name, subject, content, variables, category, is_active, created_at, updated_at
)

message_attachments (id, message_id, filename, file_path, file_size, mime_type)
```

#### Analytics & Reporting
```sql
analytics_events (
    id, event_type, entity_type, entity_id, user_id, 
    metadata, created_at
)

pipeline_metrics (
    id, date, stage, count, conversion_rate, avg_time_in_stage
)

source_effectiveness (
    id, source_name, applications_count, hires_count, 
    conversion_rate, avg_quality_score, period_start, period_end
)
```

## 4. API Architecture

### RESTful API Design
- **Base URL**: `/api/v1`
- **Authentication**: Laravel Sanctum (SPA authentication)
- **Response Format**: Standardized JSON with status, data, meta, error fields
- **Pagination**: Cursor-based for large datasets
- **Rate Limiting**: Per-user and per-endpoint limits
- **API Versioning**: URL-based versioning

### Middleware Stack
1. **CORS** - Cross-origin resource sharing
2. **Authentication** - Sanctum token validation
3. **Authorization** - Role-based permissions
4. **Rate Limiting** - API throttling
5. **Request Validation** - Form request validation
6. **Response Formatting** - Consistent JSON responses
7. **Logging** - Request/response logging
8. **Error Handling** - Global exception handling

### API Resources & Collections
- Eloquent API Resources for data transformation
- Resource Collections for paginated responses
- Conditional field inclusion based on user permissions
- Nested resource loading with eager loading optimization

## 5. Service Layer Architecture

### Core Services
```php
// Candidate Management
CandidateService
CandidateStatusService
CandidateAnalyticsService
ResumeParsingService

// Job Management
JobService
JobAnalyticsService
JobMatchingService

// Interview Management
InterviewService
InterviewSchedulingService
CalendarService
AvailabilityService

// Communication
EmailService
NotificationService
TemplateService

// Analytics
AnalyticsService
ReportingService
DashboardService

// AI Integration
AIAnalysisService
CandidateMatchingService
ResumeParsingService
```

### Repository Pattern
- Interface-based repositories for data access
- Eloquent repository implementations
- Query optimization and caching
- Search repository with Elasticsearch integration

## 6. Queue System & Background Jobs

### Job Types
```php
// Email & Notifications
SendEmailJob
SendBulkEmailJob
SendNotificationJob

// AI Processing
AnalyzeCandidateJob
ParseResumeJob
GenerateInsightsJob

// Data Processing
GenerateReportJob
UpdateAnalyticsJob
SyncCalendarJob

// File Processing
ProcessUploadedFileJob
GenerateThumbnailJob
```

### Queue Configuration
- **High Priority**: Real-time notifications, urgent emails
- **Default**: Standard processing, API responses
- **Low Priority**: Analytics, reports, bulk operations
- **Failed Job Handling**: Retry logic with exponential backoff

## 7. Caching Strategy

### Cache Layers
1. **Application Cache** (Redis)
   - API responses
   - Database query results
   - Computed analytics
   - User sessions

2. **Database Query Cache**
   - Eloquent model caching
   - Complex query result caching
   - Aggregation result caching

3. **File Cache**
   - Uploaded file metadata
   - Generated reports
   - Processed images

### Cache Keys Strategy
```
hireflow:candidates:list:{filters_hash}
hireflow:jobs:analytics:{job_id}:{period}
hireflow:dashboard:overview:{user_id}
hireflow:interviews:availability:{interviewer_id}:{date}
```

## 8. Security Implementation

### Authentication & Authorization
- **Laravel Sanctum** for SPA authentication
- **Role-based permissions** with Spatie Laravel Permission
- **Multi-factor authentication** support
- **Session management** with Redis

### Data Protection
- **Input validation** with Form Requests
- **SQL injection prevention** with Eloquent ORM
- **XSS protection** with output escaping
- **CSRF protection** for state-changing operations
- **File upload security** with validation and scanning

### API Security
- **Rate limiting** per user and endpoint
- **Request signing** for sensitive operations
- **IP whitelisting** for admin operations
- **Audit logging** for all data changes

## 9. File Management

### Storage Strategy
- **Local Storage**: Development environment
- **S3/MinIO**: Production file storage
- **CDN Integration**: Fast file delivery
- **File Versioning**: Resume version management

### File Types
- **Resumes**: PDF, DOC, DOCX parsing
- **Avatars**: Image processing and thumbnails
- **Documents**: Interview materials, contracts
- **Reports**: Generated PDF reports
- **Attachments**: Email attachments

## 10. Integration Architecture

### n8n Workflow Integration
```php
// Workflow triggers
WorkflowTriggerService
CandidateWorkflowService
InterviewWorkflowService
HiringWorkflowService

// Webhook handlers
N8nWebhookController
WorkflowEventHandler
```

### External API Integrations
- **Calendar APIs**: Google Calendar, Outlook
- **Email Services**: SendGrid, Mailgun
- **Video Conferencing**: Zoom, Google Meet
- **AI Services**: OpenAI, custom ML models
- **Social Media**: LinkedIn API integration

## 11. Performance Optimization

### Database Optimization
- **Indexing strategy** for frequently queried fields
- **Query optimization** with Eloquent relationships
- **Database connection pooling**
- **Read/write splitting** for high-load scenarios

### Application Performance
- **Eager loading** to prevent N+1 queries
- **Response caching** for expensive operations
- **Background processing** for heavy tasks
- **CDN integration** for static assets

### Monitoring & Profiling
- **Laravel Telescope** for development debugging
- **Application Performance Monitoring** (APM)
- **Database query monitoring**
- **Queue performance tracking**

## 12. Testing Strategy

### Test Types
1. **Unit Tests**: Service layer, utilities
2. **Feature Tests**: API endpoints, workflows
3. **Integration Tests**: External service integration
4. **Browser Tests**: End-to-end user flows

### Test Coverage
- **Minimum 80% code coverage**
- **Critical path 100% coverage**
- **API endpoint testing**
- **Database transaction testing**

## 13. Deployment Architecture

### Environment Setup
- **Development**: Local with Docker
- **Staging**: Cloud environment mirroring production
- **Production**: Load-balanced, auto-scaling setup

### CI/CD Pipeline
1. **Code Quality**: PHPStan, PHP CS Fixer
2. **Testing**: PHPUnit test suite
3. **Security**: Security vulnerability scanning
4. **Deployment**: Zero-downtime deployment
5. **Monitoring**: Health checks and alerts

### Infrastructure
- **Load Balancer**: Nginx/HAProxy
- **Application Servers**: PHP-FPM with multiple workers
- **Database**: MySQL with read replicas
- **Cache**: Redis cluster
- **File Storage**: S3-compatible storage
- **Monitoring**: Prometheus, Grafana

## 14. Development Phases

### Phase 1: Core Foundation (Weeks 1-2)
- Laravel project setup
- Database schema implementation
- Authentication system
- Basic CRUD operations for candidates and jobs

### Phase 2: Core Features (Weeks 3-4)
- Interview management
- File upload system
- Email system with templates
- Basic analytics

### Phase 3: Advanced Features (Weeks 5-6)
- AI integration for candidate analysis
- Advanced search with Elasticsearch
- Real-time notifications
- Dashboard with insights

### Phase 4: Integration & Optimization (Weeks 7-8)
- n8n workflow integration
- Performance optimization
- Security hardening
- Comprehensive testing

### Phase 5: Deployment & Monitoring (Week 9)
- Production deployment
- Monitoring setup
- Documentation completion
- User training materials

## 15. Next Steps

1. **Environment Setup**: Initialize Laravel project with required dependencies
2. **Database Design**: Create migrations for core tables
3. **API Foundation**: Implement authentication and basic CRUD operations
4. **Service Layer**: Build core business logic services
5. **Testing Framework**: Set up comprehensive testing suite

This architecture provides a solid foundation for building a scalable, maintainable, and feature-rich ATS system that can handle enterprise-level requirements while maintaining performance and security standards.
