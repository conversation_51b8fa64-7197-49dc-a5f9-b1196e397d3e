# Backend Documentation

This section contains comprehensive documentation for the HireFlow ATS backend system built with Laravel 12.x.

## 📋 Table of Contents

### 🔌 API Documentation
- **[Core API v2.0.1](./api/core-api.md)** - Main API endpoints (51 endpoints)
- **[Message System API](./api/message-system.md)** - Messaging and templates
- **[User Management API](./api/user-management.md)** - Users, roles, and permissions
- **[Interview Feedback API](./api/interview-feedback.md)** - Interview and feedback management
- **[Interviewer API](./api/interviewer.md)** - Interviewer-specific endpoints

### 🗄️ Database & Architecture
- **[Database Schema](./database-schema.md)** - Complete database structure
- **[Technical Architecture](./technical-architecture.md)** - System architecture overview
- **[Development Plan](./development-plan.md)** - Development roadmap

### 📝 Implementation Guides
- **[Candidate Profile Analysis](./implementation-summaries/candidate-profile-analysis.md)** - AI analysis implementation
- **[User Management Implementation](./implementation-summaries/user-management-implementation.md)** - User system setup

## 🚀 Quick Start

### Prerequisites
- PHP 8.2+
- Composer
- MySQL 8.0+
- Laravel 12.x

### Installation
```bash
cd ats-hireflow-api/backend
composer install
cp .env.example .env
php artisan key:generate
php artisan migrate --seed
php artisan serve
```

### API Base URL
```
http://localhost:8000/api/v1
```

### Authentication
All API endpoints require Bearer token authentication:
```
Authorization: Bearer {your_access_token}
```

## 🔧 Key Features

### API v2.0.1 Improvements
- **51 endpoints** covering all ATS functionality
- **Simplified JSON structure** for better performance
- **60-75% reduction** in database queries
- **Full Vietnamese language support**
- **Removed 8 obsolete tables** for cleaner architecture

### Core Modules
1. **Authentication & Users** - Laravel Sanctum with role-based permissions
2. **Candidates** - Complete candidate lifecycle management
3. **Job Postings** - Job creation and management
4. **Applications** - Application tracking and processing
5. **Interviews** - Interview scheduling and feedback
6. **Messages** - Template-based messaging system
7. **Analytics** - Reporting and dashboard data

## 📊 Database Overview

### Optimized Schema
- **Simplified candidate data** - Skills and tags as JSON arrays
- **Streamlined job postings** - Requirements and benefits as JSON
- **Efficient relationships** - Optimized foreign key structure
- **Performance focused** - Indexed for common queries

### Key Tables
- `users` - System users and authentication
- `candidates` - Candidate profiles and data
- `job_postings` - Available positions
- `applications` - Candidate applications
- `interviews` - Interview scheduling
- `messages` - Communication tracking

## 🔐 Security Features

- **Laravel Sanctum** authentication
- **Role-based permissions** with Spatie Laravel Permission
- **API rate limiting** for protection
- **Input validation** on all endpoints
- **CORS configuration** for frontend integration

## 📈 Performance Optimizations

- **Database query optimization** - Reduced queries by 60-75%
- **Eager loading** for related data
- **JSON field usage** for flexible data storage
- **Proper indexing** on frequently queried columns
- **Caching strategies** for common operations

## 🧪 Testing

```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit
```

## 📝 Development Guidelines

1. **Follow Laravel conventions** for naming and structure
2. **Use API resources** for consistent response formatting
3. **Implement proper validation** using Form Requests
4. **Write tests** for all new functionality
5. **Document API changes** in the appropriate documentation files

## 🔗 Related Documentation

- [Frontend Documentation](../frontend/README.md)
- [API Testing with Postman](./api/README.md)
- [Database Migration Guide](./database-schema.md)

---

*For detailed API usage, see the [API Documentation](./api/README.md)*
