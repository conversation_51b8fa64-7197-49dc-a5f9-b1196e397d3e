# Frontend Documentation

This section contains comprehensive documentation for the HireFlow ATS frontend system built with React, TypeScript, and Tailwind CSS.

## 📋 Table of Contents

### 🎨 Design & UI
- **[Design System](./design-system.md)** - Complete UI design guidelines and tokens
- **[System Guidelines](./system-guidelines.md)** - Development standards and practices
- **[Component Library](./components/README.md)** - Reusable UI components

### ⚡ Features & Implementation
- **[Page Title System](./features/page-titles.md)** - Dynamic page title management
- **[Calendar Integration](./features/calendar-integration.md)** - Interview scheduling
- **[Message System](./features/message-system.md)** - Communication features
- **[User Management](./features/user-management.md)** - User interface components

### 🔄 Migration & Updates
- **[Next.js Migration Plan](./migration/nextjs-migration-plan.md)** - Framework migration strategy

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- Modern browser with ES2020 support

### Installation
```bash
cd client
npm install
npm run dev
```

### Development Server
```
http://localhost:5173
```

## 🏗️ Architecture Overview

### Tech Stack
- **React 18** - UI library with hooks and concurrent features
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **React Query** - Server state management
- **React Hook Form** - Form handling
- **Radix UI** - Accessible component primitives

### Project Structure
```
client/
├── components/          # Reusable UI components
│   ├── ui/             # Base UI components (Radix + Tailwind)
│   ├── layout/         # Layout components
│   └── dashboard/      # Feature-specific components
├── pages/              # Route components
├── hooks/              # Custom React hooks
├── lib/                # Utilities and configurations
├── domains/            # Domain-specific logic
└── shared/             # Shared utilities and types
```

## 🎨 Design System

### Color Palette
- **Primary**: Modern green theme for ATS branding
- **Secondary**: Complementary colors for hierarchy
- **Semantic**: Success, warning, error, and info colors
- **Neutral**: Grayscale for text and backgrounds

### Typography
- **Font Family**: Inter (system fallback)
- **Scale**: Harmonious type scale from 12px to 48px
- **Weights**: 400 (regular), 500 (medium), 600 (semibold), 700 (bold)

### Spacing System
- **Base Unit**: 4px (0.25rem)
- **Scale**: 4px, 8px, 12px, 16px, 20px, 24px, 32px, 40px, 48px, 64px
- **Consistent**: Applied to margins, padding, and gaps

## 🧩 Component Architecture

### Base Components (ui/)
- Built on **Radix UI** primitives
- Styled with **Tailwind CSS**
- **Class Variance Authority (CVA)** for variants
- **TypeScript** for full type safety

### Layout Components
- **Header** - Navigation and user menu
- **Sidebar** - Main navigation
- **Layout** - Page wrapper with consistent structure
- **Breadcrumbs** - Navigation context

### Feature Components
- **Dashboard** - Analytics and overview
- **Candidates** - Candidate management
- **Jobs** - Job posting management
- **Calendar** - Interview scheduling
- **Messages** - Communication system

## 🔧 Development Guidelines

### Code Standards
1. **TypeScript** - Strict mode enabled
2. **ESLint** - Airbnb configuration with React rules
3. **Prettier** - Consistent code formatting
4. **Husky** - Pre-commit hooks for quality

### Component Guidelines
1. **Functional components** with hooks
2. **Props interface** for all components
3. **Default exports** for page components
4. **Named exports** for utility components
5. **JSDoc comments** for complex logic

### Styling Guidelines
1. **Tailwind CSS** for all styling
2. **CSS custom properties** for design tokens
3. **Component variants** using CVA
4. **Responsive design** mobile-first approach
5. **Dark mode support** using CSS variables

## 🌐 Internationalization

### Language Support
- **Vietnamese (vi)** - Primary language
- **English (en)** - Secondary language
- **Dynamic switching** - Runtime language changes
- **Type-safe translations** - TypeScript integration

### Translation Structure
```typescript
// lib/translations/vi.ts
export const vi = {
  pageTitle: {
    dashboard: "Bảng điều khiển",
    candidates: "Ứng viên",
    // ...
  },
  // ...
}
```

## 📱 Responsive Design

### Breakpoints
- **sm**: 640px - Small tablets
- **md**: 768px - Tablets
- **lg**: 1024px - Small desktops
- **xl**: 1280px - Desktops
- **2xl**: 1536px - Large screens

### Mobile-First Approach
- Base styles for mobile
- Progressive enhancement for larger screens
- Touch-friendly interactions
- Optimized performance

## 🧪 Testing Strategy

### Testing Tools
- **Vitest** - Unit and integration testing
- **React Testing Library** - Component testing
- **MSW** - API mocking
- **Playwright** - E2E testing (planned)

### Testing Guidelines
1. **Test user behavior** not implementation
2. **Mock external dependencies** appropriately
3. **Test accessibility** with screen readers
4. **Performance testing** for critical paths

## 🚀 Performance Optimization

### Build Optimization
- **Vite** for fast builds and HMR
- **Code splitting** by routes
- **Tree shaking** for smaller bundles
- **Asset optimization** for images and fonts

### Runtime Performance
- **React.memo** for expensive components
- **useMemo/useCallback** for expensive calculations
- **Lazy loading** for non-critical components
- **Virtual scrolling** for large lists

## 🔗 API Integration

### Data Fetching
- **React Query** for server state
- **Axios** for HTTP requests
- **Error boundaries** for error handling
- **Loading states** for better UX

### Authentication
- **Bearer tokens** stored securely
- **Automatic token refresh** when possible
- **Route protection** for authenticated pages
- **Permission-based UI** rendering

---

*For detailed component usage, see the [Component Library](./components/README.md)*
