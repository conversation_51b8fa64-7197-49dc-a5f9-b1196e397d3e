# HireFlow ATS - Design System Documentation

> **Comprehensive Design System Guide** for HireFlow ATS - AI-Inspired Modern Green Theme

## 📋 Table of Contents

1. [Design Philosophy](#design-philosophy)
2. [Color System](#color-system)
3. [Typography](#typography)
4. [Spacing & Layout](#spacing--layout)
5. [Component Architecture](#component-architecture)
6. [Theme System](#theme-system)
7. [Animation & Effects](#animation--effects)
8. [UI Component Library](#ui-component-library)
9. [CSS Utilities](#css-utilities)
10. [Development Guidelines](#development-guidelines)

---

## 🎨 Design Philosophy

### Core Principles

- **AI-Inspired Modern Design**: Contemporary design patterns inspired by modern AI tools
- **Green Primary Palette**: Professional green theme conveying growth and success
- **Glass Morphism**: Subtle backdrop blur effects for modern aesthetics
- **Micro-interactions**: Smooth animations and hover effects
- **Dark Mode First**: Complete dark/light theme support with system preference detection

### Brand Identity

- **Primary Brand Color**: Modern Green (#10b981)
- **Visual Style**: Clean, minimal, professional
- **User Experience**: Intuitive, accessible, responsive

---

## 🌈 Color System

### CSS Custom Properties (Design Tokens)

#### Light Theme

```css
:root {
  /* Base Colors */
  --background: 0 0% 100%; /* #ffffff */
  --foreground: 240 10% 3.9%; /* #0f0f0f */

  /* Surface Colors */
  --card: 0 0% 100%; /* #ffffff */
  --card-foreground: 240 10% 3.9%; /* #0f0f0f */
  --popover: 0 0% 100%; /* #ffffff */
  --popover-foreground: 240 10% 3.9%; /* #0f0f0f */

  /* Brand Colors */
  --primary: 142.1 76.2% 36.3%; /* #10b981 - Modern Green */
  --primary-foreground: 355.7 100% 97.3%; /* #fef7f7 */

  /* Semantic Colors */
  --secondary: 210 40% 98%; /* #f8fafc */
  --secondary-foreground: 222.2 84% 4.9%; /* #0f172a */
  --success: 142.1 76.2% 36.3%; /* #10b981 */
  --warning: 32.1 94.6% 43.7%; /* #f59e0b */
  --destructive: 0 84.2% 60.2%; /* #ef4444 */

  /* Interactive Elements */
  --muted: 210 40% 98%; /* #f8fafc */
  --muted-foreground: 215.4 16.3% 46.9%; /* #64748b */
  --accent: 142.1 76.2% 36.3%; /* #10b981 */
  --accent-foreground: 355.7 100% 97.3%; /* #fef7f7 */

  /* Form Controls */
  --border: 214.3 31.8% 91.4%; /* #e2e8f0 */
  --input: 214.3 31.8% 91.4%; /* #e2e8f0 */
  --ring: 142.1 76.2% 36.3%; /* #10b981 */
}
```

#### Dark Theme

```css
.dark {
  /* Base Colors */
  --background: 222.2 84% 4.9%; /* #0f172a */
  --foreground: 210 40% 98%; /* #f8fafc */

  /* Surface Colors */
  --card: 222.2 84% 4.9%; /* #0f172a */
  --card-foreground: 210 40% 98%; /* #f8fafc */

  /* Interactive Elements */
  --secondary: 217.2 32.6% 17.5%; /* #1e293b */
  --secondary-foreground: 210 40% 98%; /* #f8fafc */
  --muted: 217.2 32.6% 17.5%; /* #1e293b */
  --muted-foreground: 215 20.2% 65.1%; /* #94a3b8 */

  /* Form Controls */
  --border: 217.2 32.6% 17.5%; /* #1e293b */
  --input: 217.2 32.6% 17.5%; /* #1e293b */
}
```

### Extended Color Palette

#### Primary Green Scales

```css
.primary-50 {
  color: #f0fdf4;
} /* Lightest green */
.primary-100 {
  color: #dcfce7;
}
.primary-200 {
  color: #bbf7d0;
}
.primary-300 {
  color: #86efac;
}
.primary-400 {
  color: #4ade80;
}
.primary-500 {
  color: #10b981;
} /* Primary brand */
.primary-600 {
  color: #16a34a;
}
.primary-700 {
  color: #15803d;
}
.primary-800 {
  color: #166534;
}
.primary-900 {
  color: #14532d;
} /* Darkest green */
```

#### Emerald Accent Scale

```css
.emerald-50 {
  color: #ecfdf5;
}
.emerald-100 {
  color: #d1fae5;
}
.emerald-200 {
  color: #a7f3d0;
}
.emerald-300 {
  color: #6ee7b7;
}
.emerald-400 {
  color: #34d399;
}
.emerald-500 {
  color: #10b981;
}
.emerald-600 {
  color: #059669;
}
.emerald-700 {
  color: #047857;
}
.emerald-800 {
  color: #065f46;
}
.emerald-900 {
  color: #064e3b;
}
```

### Sidebar Color System

```css
/* Sidebar specific colors */
--sidebar-background: 240 10% 3.9%; /* Dark sidebar */
--sidebar-foreground: 0 0% 98%; /* Light text */
--sidebar-primary: 142.1 76.2% 36.3%; /* Green accent */
--sidebar-primary-foreground: 355.7 100% 97.3%;
--sidebar-accent: 240 3.7% 15.9%; /* Hover states */
--sidebar-accent-foreground: 240 4.8% 95.9%;
--sidebar-border: 240 3.7% 15.9%;
--sidebar-ring: 142.1 76.2% 36.3%;
```

---

## 📝 Typography

### Font Family

```css
font-family:
  "Inter",
  -apple-system,
  BlinkMacSystemFont,
  "Segoe UI",
  Roboto,
  sans-serif;
```

### Font Weights & Styles

```css
/* Headings */
h1,
h2,
h3,
h4,
h5,
h6 {
  @apply font-semibold;
}

/* Font feature settings for better rendering */
body {
  font-feature-settings:
    "rlig" 1,
    "calt" 1;
}
```

### Typography Scale

```css
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
} /* 12px */
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
} /* 14px */
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
} /* 16px */
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
} /* 18px */
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
} /* 20px */
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
} /* 24px */
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
} /* 30px */
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
} /* 36px */
```

---

## 📐 Spacing & Layout

### Border Radius System

```css
--radius: 0.75rem; /* 12px - Base radius */

.rounded-sm {
  border-radius: calc(var(--radius) - 4px);
} /* 8px */
.rounded-md {
  border-radius: calc(var(--radius) - 2px);
} /* 10px */
.rounded-lg {
  border-radius: var(--radius);
} /* 12px */
.rounded-xl {
  border-radius: 1rem;
} /* 16px */
.rounded-2xl {
  border-radius: 1.5rem;
} /* 24px */
```

### Container System

```css
.container {
  center: true;
  padding: 2rem;
  screens: {
    "2xl": "1400px";
  }
}
```

### Spacing Scale (Tailwind)

```css
.p-1 {
  padding: 0.25rem;
} /* 4px */
.p-2 {
  padding: 0.5rem;
} /* 8px */
.p-3 {
  padding: 0.75rem;
} /* 12px */
.p-4 {
  padding: 1rem;
} /* 16px */
.p-6 {
  padding: 1.5rem;
} /* 24px */
.p-8 {
  padding: 2rem;
} /* 32px */
.p-12 {
  padding: 3rem;
} /* 48px */
```

---

## 🧩 Component Architecture

### Base UI Components

Built using **Radix UI** primitives with **class-variance-authority** for variant management.

#### Button Component Structure

```typescript
const buttonVariants = cva(
  // Base styles
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);
```

#### Card Component Structure

```typescript
const Card = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "rounded-lg border bg-card text-card-foreground shadow-sm",
        className
      )}
      {...props}
    />
  )
);
```

---

## 🌙 Theme System

### Theme Provider Implementation

```typescript
type Theme = "dark" | "light" | "system";

export function ThemeProvider({
  children,
  defaultTheme = "system",
  storageKey = "hireflow-ui-theme",
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(
    () => (localStorage.getItem(storageKey) as Theme) || defaultTheme,
  );

  // System theme detection
  useEffect(() => {
    const root = window.document.documentElement;
    root.classList.remove("light", "dark");

    if (theme === "system") {
      const systemTheme = window.matchMedia("(prefers-color-scheme: dark)")
        .matches
        ? "dark"
        : "light";
      root.classList.add(systemTheme);
      return;
    }

    root.classList.add(theme);
  }, [theme]);
}
```

### Dark Mode Implementation

- **CSS Class Strategy**: Uses `.dark` class on `<html>` element
- **System Preference**: Automatically detects and follows system theme
- **Local Storage**: Persists user preference across sessions
- **Smooth Transitions**: All theme changes include smooth transitions

---

## ✨ Animation & Effects

### Keyframe Animations

```css
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-green {
  0%,
  100% {
    box-shadow: 0 0 0 0 hsl(var(--primary) / 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px hsl(var(--primary) / 0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
```

### Animation Utilities

```css
.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}
.animate-fade-in-up {
  animation: fade-in-up 0.5s ease-out;
}
.animate-slide-in {
  animation: slide-in 0.3s ease-out;
}
.animate-pulse-green {
  animation: pulse-green 2s infinite;
}
.animate-shimmer {
  animation: shimmer 2s linear infinite;
}
```

### Transition System

```css
/* Global transitions */
* {
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease,
    color 0.3s ease;
}

/* Theme transition helpers */
.theme-transition {
  @apply transition-all duration-300 ease-in-out;
}
.theme-transition-fast {
  @apply transition-all duration-150 ease-in-out;
}
```

---

## 🎯 UI Component Library

### Core Components

#### MetricCard Component

```css
.metric-card {
  @apply bg-card border border-border rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 relative overflow-hidden;
}

.metric-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
}
```

#### Sidebar Navigation

```css
.sidebar-nav-item {
  @apply flex items-center gap-3 rounded-xl px-4 py-3 text-sidebar-foreground transition-all duration-200 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground relative;
}

.sidebar-nav-item.active {
  @apply bg-sidebar-primary text-sidebar-primary-foreground shadow-lg;
}
```

#### Glass Card Effect

```css
.glass-card {
  @apply bg-card/80 backdrop-blur-sm border border-border/50 rounded-xl;
}

.dark .glass-card {
  @apply bg-card/60 backdrop-blur-md border-border/30;
}
```

### Custom Button Styles

```css
.ai-button {
  @apply bg-primary text-primary-foreground hover:bg-primary/90 rounded-xl px-6 py-3 font-medium transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98];
}

.ai-button-outline {
  @apply border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground rounded-xl px-6 py-3 font-medium transition-all duration-200;
}
```

---

## 🛠 CSS Utilities

### Custom Gradient Utilities

```css
.ai-gradient-bg {
  background: var(--gradient-primary);
}

.ai-gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
```

### Shadow System

```css
.modern-shadow {
  box-shadow:
    0 1px 3px 0 rgb(0 0 0 / 0.1),
    0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.modern-shadow-lg {
  box-shadow:
    0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Dark mode shadows */
.dark .modern-shadow {
  box-shadow:
    0 1px 3px 0 rgb(0 0 0 / 0.3),
    0 1px 2px -1px rgb(0 0 0 / 0.3);
}
```

### Custom Scrollbar

```css
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.5);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}
```

---

## 📋 Development Guidelines

### CSS Architecture

1. **Tailwind CSS** as primary utility framework
2. **CSS Custom Properties** for design tokens
3. **Component-specific CSS** in global.css
4. **PostCSS** for processing and optimization

### Code Organization

```
client/
├── components/
│   ├── ui/           # Base UI components (Radix + CVA)
│   ├── layout/       # Layout components
│   ├── dashboard/    # Feature-specific components
│   └── ...
├── global.css        # Global styles and custom CSS
└── ...
```

### Naming Conventions

- **CSS Classes**: Use kebab-case (`metric-card`, `sidebar-nav-item`)
- **CSS Variables**: Use kebab-case with double dashes (`--primary`, `--sidebar-background`)
- **Component Names**: Use PascalCase (`MetricCard`, `ThemeProvider`)

### Best Practices

#### 1. Design Token Usage

```css
/* ✅ Good - Use design tokens */
.custom-component {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

/* ❌ Avoid - Hard-coded colors */
.custom-component {
  background: #10b981;
  color: white;
}
```

#### 2. Responsive Design

```css
/* ✅ Use Tailwind responsive utilities */
<div className="p-4 md:p-6 lg:p-8">

/* ✅ Mobile-first approach */
.component {
  @apply text-sm md:text-base lg:text-lg;
}
```

#### 3. Dark Mode Support

```css
/* ✅ Always provide dark mode variants */
.custom-card {
  @apply bg-card text-card-foreground;
}

.dark .custom-card {
  @apply bg-card/80 backdrop-blur-sm;
}
```

#### 4. Accessibility

```css
/* ✅ Proper focus styles */
.focus-visible:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* ✅ Sufficient color contrast */
.text-muted {
  @apply text-muted-foreground; /* Meets WCAG AA standards */
}
```

### Component Development Checklist

- [ ] **Responsive**: Works on all screen sizes
- [ ] **Dark Mode**: Supports both light and dark themes
- [ ] **Accessible**: Proper ARIA labels and keyboard navigation
- [ ] **Type Safe**: Full TypeScript support
- [ ] **Consistent**: Follows design system patterns
- [ ] **Performant**: Minimal CSS, efficient animations

---

## 📱 Responsive Breakpoints

```css
/* Tailwind CSS Breakpoints */
sm:   640px   /* @media (min-width: 640px) */
md:   768px   /* @media (min-width: 768px) */
lg:   1024px  /* @media (min-width: 1024px) */
xl:   1280px  /* @media (min-width: 1280px) */
2xl:  1536px  /* @media (min-width: 1536px) */

/* Custom container max-width */
2xl:  1400px  /* Custom container breakpoint */
```

---

## 🎨 Toast/Notification System

### Toast Styling

```css
.toaster {
  --normal-bg: hsl(var(--background));
  --normal-border: hsl(var(--border));
  --success-bg: 240 253 244;
  --success-border: 34 197 94;
  --error-bg: 254 242 242;
  --error-border: 239 68 68;
  --warning-bg: 254 252 232;
  --warning-border: 245 158 11;
}

/* Toast animations */
@keyframes slideInFromTop {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.group.toast {
  backdrop-filter: blur(8px);
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
```

---

## 🔧 Build Configuration

### Tailwind Config

```typescript
// tailwind.config.ts
export default {
  darkMode: ["class"],
  content: ["./client/**/*.{ts,tsx}"],
  theme: {
    extend: {
      colors: {
        /* Custom color tokens */
      },
      borderRadius: {
        /* Custom radius system */
      },
      fontFamily: {
        /* Inter font stack */
      },
      keyframes: {
        /* Custom animations */
      },
      animation: {
        /* Animation utilities */
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
```

### PostCSS Config

```javascript
// postcss.config.js
export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
};
```

---

## 📈 Performance Considerations

### CSS Optimization

- **PurgeCSS**: Automatic unused CSS removal via Tailwind
- **Critical CSS**: Inline critical styles for above-fold content
- **CSS Minification**: Automatic minification in production
- **Tree Shaking**: Only include used Tailwind utilities

### Animation Performance

- **GPU Acceleration**: Use `transform` and `opacity` for animations
- **Reduced Motion**: Respect `prefers-reduced-motion` preference
- **Efficient Transitions**: 0.3s or less for micro-interactions

---

## 🎯 Future Enhancements

### Planned Features

1. **Component Variants**: Expand CVA usage across all components
2. **Design Tokens**: Convert more hard-coded values to CSS custom properties
3. **Animation Library**: Custom animation component library
4. **Accessibility**: Enhanced focus management and ARIA support
5. **RTL Support**: Right-to-left language support

### Migration Path

1. **Phase 1**: Complete CSS custom property adoption
2. **Phase 2**: Implement design token system
3. **Phase 3**: Add advanced animations and micro-interactions
4. **Phase 4**: Full accessibility audit and improvements

---

_This design system documentation is living and should be updated as the system evolves. All components should follow these guidelines for consistency and maintainability._
