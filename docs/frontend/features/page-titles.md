# Dynamic Page Title System

A comprehensive internationalized page title management system for HireFlow ATS.

## Overview

The dynamic page title system automatically manages `document.title` updates based on the current route and selected language. It supports both Vietnamese (vi) and English (en) languages with context variable interpolation.

## Features

- ✅ **Automatic i18n**: Titles update when language changes
- ✅ **Context Variables**: Support for dynamic content like `{name}`, `{title}`
- ✅ **SEO Optimized**: Follows pattern: "[Context] - [Feature] - HireFlow ATS"
- ✅ **Meta Description**: Automatically updates meta description
- ✅ **TypeScript**: Full type safety with TypeScript
- ✅ **React Router**: Seamless integration with routing

## Installation

The system is already integrated into the application. Simply import the hooks:

```typescript
import { usePageTitle, useSimplePageTitle, useContextPageTitle } from "@/hooks/usePageTitle";
```

## Usage

### 1. Simple Page Title

For static page titles without context variables:

```typescript
import { useSimplePageTitle } from "@/hooks/usePageTitle";

function Dashboard() {
  useSimplePageTitle("pageTitle.dashboard");
  // Result: "Dashboard - HireFlow ATS" (EN) or "Trang chủ - HireFlow ATS" (VI)
  
  return <div>Dashboard content</div>;
}
```

### 2. Context Page Title

For page titles with a single context variable:

```typescript
import { useContextPageTitle } from "@/hooks/usePageTitle";

function CandidateDetail({ candidateName }: { candidateName: string }) {
  useContextPageTitle("pageTitle.candidates.detail", "name", candidateName);
  // Result: "John Doe - Candidate Profile - HireFlow ATS"
  
  return <div>Candidate details</div>;
}
```

### 3. Advanced Page Title

For complex page titles with multiple context variables:

```typescript
import { usePageTitle } from "@/hooks/usePageTitle";

function JobDetail({ job }: { job: Job }) {
  usePageTitle({
    titleKey: "pageTitle.jobs.detail",
    context: { 
      title: job.title,
      department: job.department 
    },
    additionalContext: job.status === 'active' ? 'Active Position' : undefined
  });
  // Result: "Frontend Developer - Job Details - Active Position - HireFlow ATS"
  
  return <div>Job details</div>;
}
```

## Translation Structure

### English (`client/lib/translations/en.ts`)

```typescript
pageTitle: {
  dashboard: "Dashboard",
  analytics: "Analytics & Reports",
  
  candidates: {
    list: "Candidates",
    detail: "{name} - Candidate Profile",
    create: "Add New Candidate",
    edit: "Edit Candidate - {name}",
  },
  
  jobs: {
    list: "Job Postings",
    detail: "{title} - Job Details",
    create: "Create New Job",
    edit: "Edit Job - {title}",
  },
  
  // ... more translations
}
```

### Vietnamese (`client/lib/translations/vi.ts`)

```typescript
pageTitle: {
  dashboard: "Trang chủ",
  analytics: "Phân tích & Báo cáo",
  
  candidates: {
    list: "Ứng viên",
    detail: "{name} - Hồ sơ ứng viên",
    create: "Thêm ứng viên mới",
    edit: "Chỉnh sửa ứng viên - {name}",
  },
  
  jobs: {
    list: "Tin tuyển dụng",
    detail: "{title} - Chi tiết công việc",
    create: "Tạo tin tuyển dụng mới",
    edit: "Chỉnh sửa công việc - {title}",
  },
  
  // ... more translations
}
```

## Available Page Title Categories

### 1. Dashboard & Analytics
- `pageTitle.dashboard` - Main dashboard
- `pageTitle.analytics` - Analytics and reports

### 2. Candidates
- `pageTitle.candidates.list` - Candidate listing
- `pageTitle.candidates.detail` - Candidate profile (with `{name}`)
- `pageTitle.candidates.create` - Add new candidate
- `pageTitle.candidates.edit` - Edit candidate (with `{name}`)

### 3. Jobs
- `pageTitle.jobs.list` - Job listings
- `pageTitle.jobs.detail` - Job details (with `{title}`)
- `pageTitle.jobs.create` - Create job
- `pageTitle.jobs.edit` - Edit job (with `{title}`)

### 4. Pipeline
- `pageTitle.pipeline.overview` - Pipeline overview
- `pageTitle.pipeline.kanban` - Kanban view
- `pageTitle.pipeline.stage` - Pipeline stage (with `{stage}`)

### 5. Messages
- `pageTitle.messages.inbox` - Message inbox
- `pageTitle.messages.conversation` - Conversation (with `{name}`)
- `pageTitle.messages.compose` - Compose message
- `pageTitle.messages.templates` - Message templates

### 6. Interviews
- `pageTitle.interviews.list` - Interview schedule
- `pageTitle.interviews.detail` - Interview details (with `{name}`)
- `pageTitle.interviews.schedule` - Schedule interview
- `pageTitle.interviews.calendar` - Interview calendar
- `pageTitle.interviews.feedback` - Interview feedback (with `{name}`)

### 7. Interviewers
- `pageTitle.interviewers.list` - Interviewer listing
- `pageTitle.interviewers.profile` - Interviewer profile (with `{name}`)
- `pageTitle.interviewers.create` - Add interviewer
- `pageTitle.interviewers.edit` - Edit interviewer (with `{name}`)

### 8. Settings
- `pageTitle.settings.general` - General settings
- `pageTitle.settings.account` - Account settings
- `pageTitle.settings.team` - Team management
- `pageTitle.settings.integrations` - Integrations
- `pageTitle.settings.notifications` - Notifications

### 9. Authentication
- `pageTitle.auth.login` - Sign in
- `pageTitle.auth.register` - Create account
- `pageTitle.auth.resetPassword` - Reset password
- `pageTitle.auth.verifyEmail` - Verify email

### 10. Error Pages
- `pageTitle.error.notFound` - Page not found
- `pageTitle.error.serverError` - Server error
- `pageTitle.error.unauthorized` - Access denied

## Implementation Examples

### Page Components

Each major page component should include the appropriate page title hook:

```typescript
// Dashboard
export default function Dashboard() {
  useSimplePageTitle("pageTitle.dashboard");
  // Component logic...
}

// Candidate Detail
export default function CandidateDetail() {
  const { id } = useParams();
  const { data: candidate } = useCandidate(id);
  
  useContextPageTitle(
    "pageTitle.candidates.detail", 
    "name", 
    candidate?.name || "Loading..."
  );
  // Component logic...
}

// Job Detail with Additional Context
export default function JobDetail() {
  const { id } = useParams();
  const { data: job } = useJob(id);
  
  usePageTitle({
    titleKey: "pageTitle.jobs.detail",
    context: { title: job?.title || "Loading..." },
    additionalContext: job?.status === 'active' ? 'Active Position' : undefined
  });
  // Component logic...
}
```

## API Reference

### `usePageTitle(options: PageTitleOptions)`

The main hook for managing page titles with full customization.

**Parameters:**
- `titleKey` (string): Translation key for the title
- `context` (object, optional): Context variables for interpolation
- `additionalContext` (string, optional): Additional context to append
- `includeAppName` (boolean, optional): Whether to include "HireFlow ATS" suffix (default: true)

### `useSimplePageTitle(titleKey: string, additionalContext?: string)`

Simplified hook for static page titles.

**Parameters:**
- `titleKey` (string): Translation key for the title
- `additionalContext` (string, optional): Additional context to append

### `useContextPageTitle(titleKey: string, contextKey: string, contextValue: string, additionalContext?: string)`

Hook for page titles with a single context variable.

**Parameters:**
- `titleKey` (string): Translation key for the title
- `contextKey` (string): The context variable key (e.g., "name", "title")
- `contextValue` (string): The context variable value
- `additionalContext` (string, optional): Additional context to append

## Best Practices

### 1. Use Descriptive Context
```typescript
// ✅ Good
useContextPageTitle("pageTitle.candidates.detail", "name", "John Doe");

// ❌ Avoid
useContextPageTitle("pageTitle.candidates.detail", "name", "");
```

### 2. Handle Loading States
```typescript
// ✅ Good
useContextPageTitle(
  "pageTitle.candidates.detail", 
  "name", 
  candidate?.name || "Loading..."
);

// ❌ Avoid
useContextPageTitle("pageTitle.candidates.detail", "name", candidate?.name);
```

### 3. Use Additional Context Sparingly
```typescript
// ✅ Good - Adds meaningful context
usePageTitle({
  titleKey: "pageTitle.jobs.detail",
  context: { title: "Frontend Developer" },
  additionalContext: "Remote Position"
});

// ❌ Avoid - Too verbose
usePageTitle({
  titleKey: "pageTitle.jobs.detail",
  context: { title: "Frontend Developer" },
  additionalContext: "Remote Position - Full Time - Senior Level"
});
```

### 4. Follow Naming Conventions
- Use clear, hierarchical translation keys
- Keep context variable names consistent (`name`, `title`, `stage`)
- Use kebab-case for translation keys

## SEO Benefits

The dynamic page title system provides several SEO advantages:

1. **Descriptive Titles**: Each page has a unique, descriptive title
2. **Meta Descriptions**: Automatically generated based on page content
3. **Language Support**: Proper localization for international SEO
4. **Context Awareness**: Titles include relevant entity names and details

## Testing

To test the page title system:

1. Navigate to different pages and observe the browser title bar
2. Switch languages and verify titles update
3. Use the `PageTitleDemo` component for interactive testing
4. Check meta descriptions in browser dev tools

## Troubleshooting

### Title Not Updating
- Ensure the hook is called in the component body
- Check that the translation key exists in both language files
- Verify the component is properly mounted

### Context Variables Not Working
- Check that variable names match between translation and hook usage
- Ensure context values are not undefined
- Verify the translation includes the correct placeholder syntax `{variableName}`

### Build Errors
- Ensure all translation keys are defined in both `en.ts` and `vi.ts`
- Check TypeScript types in `client/lib/types.ts`
- Verify import paths are correct

## Contributing

When adding new pages or modifying existing ones:

1. Add appropriate translation keys to both language files
2. Update the TypeScript interface in `client/lib/types.ts`
3. Use the appropriate page title hook in your component
4. Test with both languages
5. Update this documentation if adding new categories
