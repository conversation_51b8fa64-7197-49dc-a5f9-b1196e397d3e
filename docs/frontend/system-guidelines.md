# 📋 HireFlow ATS - Frontend System Guideline

> **B<PERSON>n hướng dẫn phát triển frontend đồng bộ cho đội ngũ HireFlow ATS**  
> Version: 1.0 | Cập nhật: 2024

---

## 🎯 **Tổng quan Kiến trúc**

### **Tech Stack Chính**
```typescript
// Core Technologies
React 18 + TypeScript + Vite
TailwindCSS + shadcn/ui + Radix UI
React Query + React Hook Form
React Router + i18next
```

### **Cấu trúc Project**
```
client/
├── components/         # UI Components (domain-based organization)
│   ├── ui/            # Base UI components (shadcn/ui)
│   ├── candidates/    # Candidate domain components
│   ├── jobs/          # Job domain components
│   ├���─ calendar/      # Calendar domain components
│   └── layout/        # Layout components
├── pages/             # Page components (React Router)
├── hooks/             # Custom hooks
├── lib/               # Utilities and core logic
│   ├── adapters/      # Data transformation
│   ├── services/      # API services
│   ├── types/         # TypeScript definitions
│   └── utils/         # Helper functions
├── data/              # Mock data and constants
└── shared/            # Shared utilities
```

---

## 🎨 **Design System & UI Components**

### **Component Library**
- **Base**: shadcn/ui + Radix UI primitives
- **Icons**: Lucide React
- **Styling**: TailwindCSS + CSS Variables
- **Variants**: Class Variance Authority (CVA)

### **Color System**
```css
/* Primary Green Theme */
--primary: 142.1 76.2% 36.3%;        /* #10b981 */
--success: 142.1 76.2% 36.3%;
--destructive: 0 84.2% 60.2%;        /* #ef4444 */
--warning: 32.1 94.6% 43.7%;         /* #f59e0b */

/* Semantic Variables */
--background, --foreground
--card, --card-foreground
--muted, --muted-foreground
```

### **Typography System**
```css
font-family: Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif;

/* Hierarchy */
H1-H6: font-semibold
text-xs → text-2xl (Tailwind scale)
```

### **Component Architecture**
```typescript
// CVA Pattern untuk variants
const buttonVariants = cva(
  "base-classes",
  {
    variants: {
      variant: { default, destructive, outline },
      size: { default, sm, lg, icon }
    }
  }
)

// Forward Refs Pattern
const Component = React.forwardRef<ElementRef, Props>(
  ({ className, ...props }, ref) => (
    <Primitive
      ref={ref}
      className={cn(baseClasses, className)}
      {...props}
    />
  )
)
```

---

## 📝 **Naming Conventions**

### **File & Folder Naming**

| Type | Convention | Example |
|------|------------|---------|
| **Components** | PascalCase.tsx | `CandidateDetailModal.tsx` |
| **Hooks** | kebab-case | `use-toast.ts` |
| **Services** | camelCase | `candidateAnalysisService.ts` |
| **Types** | camelCase | `candidateAnalysis.ts` |
| **Utils** | camelCase | `utils.ts`, `config.ts` |
| **Folders** | kebab-case | `components/ui/`, `lib/services/` |

### **Code Naming**

```typescript
// ✅ Component Names - PascalCase
const CandidateDetailModal = () => {}
const AIAnalysisSummary = () => {}

// ✅ Variables & Functions - camelCase
const candidateId = 123;
const handleStatusChange = () => {}
const generateAnalysisReport = async () => {}

// ✅ Constants - UPPER_SNAKE_CASE
const API_BASE_URL = "https://api.example.com";
const MAX_FILE_SIZE = 10 * 1024 * 1024;

// ✅ Types & Interfaces - PascalCase
interface CandidateDetailModalProps {}
type ApiResponse<T> = {}

// ✅ Custom Hooks - camelCase with 'use' prefix
const useAIAnalysis = () => {}
const useCandidateFilters = () => {}

// ✅ CSS Classes - kebab-case
.ai-gradient-bg {}
.metric-card {}
.sidebar-nav-item {}

// ✅ API Properties - snake_case (backend convention)
candidate_id, job_posting_id, created_at

// ✅ UI Properties - camelCase (frontend convention)
candidateId, jobPostingId, createdAt
```

---

## 🔄 **State Management Patterns**

### **State Layer Separation**

```typescript
// 1. Global State (Context API)
- Authentication & User data
- Theme preferences (dark/light)
- Language/Internationalization
- Application configuration

// 2. Server State (React Query)
- CRUD operations (Candidates, Jobs, Interviews)
- Dashboard analytics
- API caching and synchronization

// 3. Local State (useState/useReducer)
- Form inputs & validation
- UI toggles (modals, filters)
- Component-specific selections
```

### **React Query Implementation**

```typescript
// Query Keys Structure
export const queryKeys = {
  candidates: (params?: any) => ["candidates", params],
  candidate: (id: string, include?: string) => ["candidate", id, include],
  jobs: (params?: any) => ["jobs", params],
}

// Custom Hook Pattern
export const useCandidates = (params?: FilterParams) => {
  return useQuery({
    queryKey: queryKeys.candidates(params),
    queryFn: () => apiService.getCandidates(params || {}),
    enabled: !!localStorage.getItem("auth_token"),
    staleTime: 60000, // 1 minute
  });
};

// Mutation với Error Handling
export const useCreateCandidate = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: (data: CandidateData) => apiService.createCandidate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["candidates"] });
      notifications.success("Tạo ứng viên thành công!");
    },
    onError: (error: ApiError) => {
      notifications.showApiError(error);
    }
  });
};
```

### **Data Transformation Pattern**

```typescript
// Adapter Pattern cho API ↔ UI transformation
export const candidateAdapters = {
  fromApi: (apiCandidate: ApiCandidate): UiCandidate => ({
    id: apiCandidate.id.toString(),
    name: safeString(apiCandidate.name),
    skills: safeArray(apiCandidate.skills),
    appliedDate: safeDate(apiCandidate.applied_date),
  }),

  toApi: (uiCandidate: Partial<UiCandidate>): Partial<ApiCandidate> => ({
    name: safeString(uiCandidate.name),
    skills: safeArray(uiCandidate.skills),
    applied_date: formatDateForApi(uiCandidate.appliedDate),
  })
}
```

---

## 🔄 **Data Flow & API Integration**

### **API Service Layer**

```typescript
// Centralized API Service
class ApiService {
  private baseURL: string;
  private token: string | null = null;

  // Standardized request method
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    // Authentication, error handling, response transformation
  }

  // Domain-specific methods
  async getCandidates(params: CandidateFilters): Promise<PaginatedResponse<Candidate[]>> {}
  async createCandidate(data: CandidateFormData): Promise<Candidate> {}
}

export const apiService = new ApiService();
```

### **Error Handling Strategy**

```typescript
// Custom Error Classes
export class ValidationError extends Error {
  constructor(
    message: string,
    public readonly validationErrors: Record<string, string[]>,
    public readonly statusCode: number = 422
  ) {}
}

export class ApiError extends Error {
  constructor(
    message: string,
    public readonly statusCode: number,
    public readonly response?: any
  ) {}
}

// Centralized Error Management
const useNotifications = () => {
  const showApiError = (error: ApiError | ValidationError | Error) => {
    if (error instanceof ValidationError) {
      // Handle field-specific validation errors
      showValidationToast(error.validationErrors);
    } else if (error instanceof ApiError) {
      // Handle HTTP/API errors
      showErrorToast(error.message);
    }
  };
}
```

---

## 📋 **Form Management**

### **React Hook Form Pattern**

```typescript
// Standard Form Setup
const { 
  register, 
  handleSubmit, 
  setValue, 
  watch, 
  formState: { errors, isSubmitting } 
} = useForm<FormData>({
  resolver: zodResolver(validationSchema),
  defaultValues: defaultFormData
});

// Form Submission với Error Handling
const onSubmit = async (data: FormData) => {
  try {
    await mutation.mutateAsync(data);
    onSuccess?.();
  } catch (error) {
    // Errors handled by mutation onError
  }
};

// Controlled Component Pattern
<Input
  {...register("name")}
  error={errors.name?.message}
  placeholder="Nhập họ tên"
  disabled={isSubmitting}
/>
```

### **Validation Strategy**

```typescript
// Zod Schema Validation
const candidateSchema = z.object({
  name: z.string().min(1, "Tên là bắt buộc"),
  email: z.string().email("Email không hợp lệ"),
  phone: z.string().optional(),
  skills: z.array(z.string()).min(1, "Ít nhất 1 kỹ năng"),
});

type CandidateFormData = z.infer<typeof candidateSchema>;
```

---

## 🌐 **Internationalization (i18n)**

### **Translation Structure**

```typescript
// Translation Interface
interface Translations {
  common: { save: string; cancel: string; delete: string; }
  candidates: { title: string; addCandidate: string; }
  jobs: { title: string; addJob: string; }
  // ... other domains
}

// Usage trong Component
const CandidateList = () => {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t.candidates.title}</h1>
      <Button>{t.candidates.addCandidate}</Button>
    </div>
  );
};
```

### **Translation Keys Convention**

```typescript
// Hierarchical Structure
t.common.save              // "Lưu" / "Save"
t.candidates.title         // "Ứng viên" / "Candidates"
t.status.pending          // "Đang chờ" / "Pending"
t.toast.success.created   // "Tạo thành công!" / "Created successfully!"
```

---

## 📦 **Component Development Guidelines**

### **Component Structure**

```typescript
// Standard Component Template
import React from 'react';
import { cn } from '@/lib/utils';

interface ComponentProps {
  className?: string;
  children?: React.ReactNode;
  variant?: 'default' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
}

export const Component = React.forwardRef<
  HTMLDivElement,
  ComponentProps
>(({ className, children, variant = 'default', size = 'md', ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        'base-styles',
        variantStyles[variant],
        sizeStyles[size],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
});

Component.displayName = 'Component';
```

### **Custom Hook Pattern**

```typescript
// Custom Hook Template
export const useCustomHook = (params: HookParams) => {
  const [state, setState] = useState(initialState);
  
  // Effects và logic
  useEffect(() => {
    // Side effects
  }, [dependencies]);
  
  // Helper functions
  const helperFunction = useCallback(() => {
    // Logic
  }, [dependencies]);
  
  // Return interface
  return {
    data: state,
    isLoading,
    error,
    actions: {
      helperFunction,
      setState
    }
  };
};
```

---

## 🧪 **Testing Guidelines**

### **Testing Strategy**

```typescript
// Component Testing với React Testing Library
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } }
  });
  
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('CandidateList', () => {
  it('should display candidates correctly', async () => {
    render(<CandidateList />, { wrapper: TestWrapper });
    
    await waitFor(() => {
      expect(screen.getByText('Ứng viên')).toBeInTheDocument();
    });
  });
});
```

### **Hook Testing**

```typescript
// Custom Hook Testing
import { renderHook, act } from '@testing-library/react';

describe('useCustomHook', () => {
  it('should handle state updates correctly', () => {
    const { result } = renderHook(() => useCustomHook(mockParams));
    
    act(() => {
      result.current.actions.setState(newState);
    });
    
    expect(result.current.data).toEqual(expectedState);
  });
});
```

---

## 🚀 **Performance Best Practices**

### **Code Splitting**

```typescript
// Lazy Loading Components
const CandidateDetail = lazy(() => import('@/components/candidates/CandidateDetail'));
const JobDetail = lazy(() => import('@/components/jobs/JobDetail'));

// Route-based Code Splitting
const Routes = () => (
  <Router>
    <Routes>
      <Route path="/candidates" element={
        <Suspense fallback={<Loading />}>
          <CandidateDetail />
        </Suspense>
      } />
    </Routes>
  </Router>
);
```

### **Memoization Strategy**

```typescript
// Component Memoization
const ExpensiveComponent = React.memo(({ data, onUpdate }) => {
  // Expensive rendering logic
}, (prevProps, nextProps) => {
  // Custom comparison for re-render optimization
  return prevProps.data.id === nextProps.data.id;
});

// Hook Memoization
const useExpensiveCalculation = (data: ComplexData[]) => {
  return useMemo(() => {
    return data.reduce((acc, item) => {
      // Expensive calculation
    }, initialValue);
  }, [data]);
};
```

### **Bundle Optimization**

```typescript
// Vite Config Optimization
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-select'],
          utils: ['date-fns', 'clsx', 'tailwind-merge']
        }
      }
    }
  }
});
```

---

## 🔧 **Development Tools**

### **VS Code Extensions**
- ES7+ React/Redux/React-Native snippets
- TypeScript Importer
- Tailwind CSS IntelliSense
- Auto Rename Tag
- Thunder Client (API testing)

### **Code Quality Tools**

```json
// package.json scripts
{
  "scripts": {
    "dev": "vite",
    "build": "npm run build:client && npm run build:server",
    "test": "vitest --run",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "format": "prettier --write .",
    "typecheck": "tsc"
  }
}
```

### **Git Workflow**

```bash
# Feature Branch Convention
git checkout -b feature/candidate-ai-analysis
git checkout -b fix/pointer-events-cleanup
git checkout -b refactor/api-service-optimization

# Commit Message Convention
feat: add AI candidate analysis component
fix: resolve pointer-events issue in dialogs
refactor: optimize API service caching strategy
docs: update component documentation
test: add unit tests for custom hooks
```

---

## 📚 **Documentation Standards**

### **Component Documentation**

```typescript
/**
 * CandidateDetailModal - Displays detailed candidate information in a modal
 * 
 * @param candidate - The candidate object to display
 * @param isOpen - Controls modal visibility
 * @param onClose - Callback when modal is closed
 * @param onEdit - Optional callback for edit action
 * 
 * @example
 * ```tsx
 * <CandidateDetailModal
 *   candidate={selectedCandidate}
 *   isOpen={isModalOpen}
 *   onClose={() => setIsModalOpen(false)}
 *   onEdit={handleEditCandidate}
 * />
 * ```
 */
export const CandidateDetailModal = ({ candidate, isOpen, onClose, onEdit }: Props) => {
  // Component implementation
};
```

### **API Documentation**

```typescript
/**
 * Retrieves paginated list of candidates with optional filtering
 * 
 * @param params - Filter and pagination parameters
 * @param params.page - Page number (1-based)
 * @param params.per_page - Items per page (default: 20)
 * @param params.filter - Filter criteria object
 * @param params.sort - Sort field and direction
 * 
 * @returns Promise resolving to paginated candidate response
 * 
 * @throws {ApiError} When API request fails
 * @throws {ValidationError} When parameters are invalid
 */
async getCandidates(params: CandidateFilterParams): Promise<PaginatedResponse<Candidate[]>>
```

---

## ⚠️ **Common Pitfalls & Solutions**

### **1. State Management**
```typescript
// ❌ Avoid: Mixing server state with local state
const [candidates, setCandidates] = useState([]);
const [isLoading, setIsLoading] = useState(false);

// ✅ Use: React Query for server state
const { data: candidates, isLoading } = useCandidates();
```

### **2. Component Re-renders**
```typescript
// ❌ Avoid: Inline object/function creation
<Component
  style={{ margin: 10 }}
  onClick={() => handleClick(id)}
/>

// ✅ Use: Memoized values
const style = useMemo(() => ({ margin: 10 }), []);
const handleClick = useCallback((id) => { /* logic */ }, []);
```

### **3. Type Safety**
```typescript
// ❌ Avoid: Using 'any' type
const handleUpdate = (data: any) => { /* logic */ };

// ✅ Use: Specific interfaces
interface UpdateData {
  id: string;
  name: string;
  status: CandidateStatus;
}
const handleUpdate = (data: UpdateData) => { /* logic */ };
```

### **4. Error Boundaries**
```typescript
// ✅ Implement: Error boundary for chunk load failures
export const ChunkErrorBoundary = ({ children }: { children: React.ReactNode }) => (
  <ErrorBoundary
    fallback={<ChunkLoadErrorFallback />}
    onError={(error) => {
      if (error.message.includes('Loading chunk')) {
        window.location.reload();
      }
    }}
  >
    {children}
  </ErrorBoundary>
);
```

---

## 🔄 **Migration & Refactoring Guidelines**

### **Incremental Migration Strategy**

1. **Phase 1**: Establish new patterns (completed)
2. **Phase 2**: Migrate critical components
3. **Phase 3**: Update supporting utilities
4. **Phase 4**: Full codebase alignment

### **Refactoring Checklist**

- [ ] Component follows naming conventions
- [ ] Uses proper TypeScript interfaces
- [ ] Implements error handling
- [ ] Includes loading states
- [ ] Has proper accessibility attributes
- [ ] Follows design system patterns
- [ ] Includes unit tests
- [ ] Documents complex logic

---

## 📞 **Support & Resources**

### **Internal Resources**
- **Design System**: `/client/components/ui/` folder
- **API Documentation**: `/API.md` and `/apiv2.md`
- **Type Definitions**: `/client/lib/types/`
- **Examples**: Existing components in domain folders

### **External Resources**
- [shadcn/ui Documentation](https://ui.shadcn.com/)
- [React Query Documentation](https://tanstack.com/query/latest)
- [TailwindCSS Documentation](https://tailwindcss.com/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)

---

## 📋 **Checklist cho Pull Request**

### **Before Submitting**
- [ ] Code follows naming conventions
- [ ] TypeScript types are properly defined
- [ ] Components are responsive and accessible
- [ ] Error handling is implemented
- [ ] Loading states are handled
- [ ] Tests are written and passing
- [ ] No console errors in development
- [ ] Performance impact considered
- [ ] Documentation updated if needed

### **Code Review Focus**
- [ ] Business logic correctness
- [ ] Type safety compliance
- [ ] Performance implications
- [ ] Accessibility standards
- [ ] Error handling coverage
- [ ] Code maintainability
- [ ] Design system adherence

---

*Tài liệu này được duy trì bởi đội Frontend HireFlow ATS. Vui lòng cập nhật khi có thay đổi trong kiến trúc hoặc quy chuẩn.*
