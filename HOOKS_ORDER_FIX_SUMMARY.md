# React Hooks Order Fix Summary

## Problem
The EditCandidateModal component was violating the Rules of Hooks by conditionally calling `useCandidateNotesOperations`:

```javascript
// WRONG - Conditional hook call
const notesOperations = mode === "edit" && candidate ? useCandidateNotesOperations(candidate.id) : null;
```

This caused <PERSON><PERSON> to detect a change in the order of hooks between renders, leading to:
- Warning: "<PERSON><PERSON> has detected a change in the order of Hooks"
- Error: "Should have a queue. This is likely a bug in React"

## Root Cause
When `mode` was "add" or `candidate` was null, the hook wasn't called, changing the order of hooks between renders. This violates React's fundamental rule that hooks must be called in the same order every time.

## Solution

### 1. Always Call the Hook
Changed to always call the hook but pass conditional parameters:

```javascript
// CORRECT - Always call hook
const candidateIdForNotes = mode === "edit" && candidate ? candidate.id : "";
const notesOperations = useCandidateNotesOperations(candidateIdForNotes);
```

### 2. Handle Empty/Invalid IDs in the Hook
Updated `useCandidateNotesOperations` to handle empty candidate IDs gracefully:

```javascript
export const useCandidateNotesOperations = (candidateId: string) => {
  // Always call all hooks first
  const addNote = useAddCandidateNote();
  const updateNote = useUpdateCandidateNote();
  const deleteNote = useDeleteCandidateNote();
  const updateAllNotes = useUpdateCandidateNotes();

  // Then handle conditional logic
  const isDisabled = !candidateId || candidateId.trim() === "";

  return {
    addNote: isDisabled 
      ? async (content: string) => Promise.reject(new Error("No candidate ID provided"))
      : (content: string) => addNote.mutateAsync({ candidateId, content }),
    // ... other operations with same pattern
    isDisabled,
  };
};
```

### 3. Update Component Logic
Updated the component to use the `isDisabled` property for conditional rendering:

```javascript
{mode === "edit" && candidate && !notesOperations.isDisabled ? (
  <EnhancedCandidateNotes {...props} />
) : (
  // Alternative UI
)}
```

## Benefits

1. **Hooks Consistency**: All hooks are now called in the same order every render
2. **Error Handling**: Graceful handling of invalid candidate IDs
3. **Type Safety**: Proper TypeScript support throughout
4. **User Experience**: Clear feedback when operations are disabled
5. **Maintainability**: Easier to debug and test

## Testing

To verify the fix:
1. ✅ Open EditCandidateModal in "add" mode - no hook errors
2. ✅ Open EditCandidateModal in "edit" mode - notes operations work
3. ✅ Switch between modes - consistent hook order maintained
4. ✅ No React warnings or errors in console

## Key Takeaway

Always follow the Rules of Hooks:
- Don't call hooks inside loops, conditions, or nested functions
- Only call hooks from React function components or custom hooks
- Call hooks in the same order every time

This fix ensures the component is robust and follows React best practices.
