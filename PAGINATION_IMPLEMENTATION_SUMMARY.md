# Tóm tắt Phân tích và Triển khai Phân trang

## 1. <PERSON><PERSON> tích Phân trang trong /candidates

### C<PERSON>u trúc hiện tại:
- **Component**: `CandidatesPagination` (`client/components/ui/candidates-pagination.tsx`)
- **Hook API**: `useCandidates` với params `page` và `per_page`
- **Xử lý dữ liệu**: Sử dụng `candidateAdapters.fromPaginatedApi()` để transform dữ liệu từ API
- **Reset trang**: Khi thay đổi filter hoặc search, reset về trang 1
- **UI**: Hiển thị số lượng item, nút Previous/Next, các số trang với ellipsis

### Đặc điểm:
- Server-side pagination (không filter client-side)
- Responsive design với text ẩn/hiện trên mobile
- Error handling khi chuyển trang thất bại
- Loading state trong khi chuyển trang

## 2. <PERSON><PERSON><PERSON> khai cho /jobs

### Thay đổi đã thực hiện:

#### A. Tạo Component Phân trang Chung
- **File mới**: `client/components/ui/common-pagination.tsx`
- **Props**: 
  - `pagination`: Dữ liệu phân trang
  - `onPageChange`: Callback khi chuyển trang
  - `isLoading`: Trạng thái loading
  - `itemName`: Tên loại item (có thể tùy chỉnh)
  - `className`: CSS class tùy chỉnh

#### B. Cập nhật Jobs.tsx
- **Import**: Thêm `CommonPagination`
- **API call**: Thêm `per_page: 10` vào `useJobs`
- **Loại bỏ**: Client-side filtering/sorting (chuyển sang server-side)
- **Reset page**: Tất cả filter, search, sort đều reset về trang 1
- **Hiển thị**: Sử dụng `pagination.totalItems` thay vì `jobs.length`
- **UI**: Thêm `CommonPagination` component sau danh sách job

## 3. Triển khai cho /interviewers

### Thay đổi đã thực hiện:

#### A. Cập nhật Interviewers.tsx
- **Import**: Thêm `CommonPagination`
- **State**: Thêm `currentPage` state
- **API call**: Thêm `page` và `per_page` vào `useInterviewers`
- **Pagination data**: Extract t��� `interviewersData.meta`
- **Reset page**: Filter search, department, status đều reset về trang 1
- **UI**: Thêm `CommonPagination` trong CardContent

#### B. Cấu trúc Pagination Data
```typescript
const pagination = interviewersData?.meta
  ? {
      currentPage: interviewersData.meta.current_page,
      totalPages: interviewersData.meta.last_page,
      totalItems: interviewersData.meta.total,
      itemsPerPage: interviewersData.meta.per_page,
      hasNext: interviewersData.meta.current_page < interviewersData.meta.last_page,
      hasPrev: interviewersData.meta.current_page > 1,
    }
  : null;
```

## 4. Phân tích /messages

### Trạng thái hiện tại:
- **MessageList**: Đã có phân trang built-in với state management đầy đủ
- **MessageTemplateList**: Đã có phân trang với pagination state
- **Không cần thay đổi**: Cả hai component đã implement phân trang server-side

### Chi tiết Implementation:
- MessageList: Sử dụng `currentPage`, `totalPages`, `totalMessages` states
- MessageTemplateList: Sử dụng pagination object với `current_page`, `last_page`, `total`
- Cả hai đều có filter và search với reset page

## 5. Lợi ích của Giải pháp

### A. Component Tái sử dụng
- `CommonPagination` có thể dùng cho tất cả các trang
- Customizable `itemName` cho từng context
- Consistent UI/UX across application

### B. Performance
- Server-side pagination giảm tải client
- Lazy loading chỉ fetch data cần thiết
- Efficient memory usage

### C. User Experience
- Reset page khi filter/search để tránh confusion
- Loading states và error handling
- Responsive design cho mobile/desktop

### D. Maintainability
- Centralized pagination logic
- Type-safe với TypeScript
- Consistent error handling pattern

## 6. Cấu trúc Files

```
client/
├── components/
│   └── ui/
│       ├── candidates-pagination.tsx (cũ - cho backward compatibility)
│       └── common-pagination.tsx (mới - chung cho tất cả)
├── pages/
│   ├── Candidates.tsx (đã có phân trang)
│   ├── Jobs.tsx (đã thêm phân trang)
│   ├── Interviewers.tsx (đã thêm phân trang)
│   └── MessagesWithTemplates.tsx (đã có phân trang)
└── lib/
    └── adapters/
        └── utils.ts (PaginationData interface)
```

## 7. API Requirements

Tất cả API endpoints cần support:
- `page`: Số trang (bắt đầu từ 1)
- `per_page`: Số item per page
- Response format:
  ```json
  {
    "data": [...],
    "meta": {
      "current_page": 1,
      "last_page": 10,
      "per_page": 10,
      "total": 100
    }
  }
  ```

## 8. Testing Notes

Cần test:
- Pagination navigation (prev/next/specific page)
- Filter/search reset page behavior
- Loading states
- Error handling
- Responsive design
- Empty states
