import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FormActions } from "@/components/ui/form-actions";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Building2,
  X,
  Plus,
  Sparkles,
  FileText,
  Target,
  Award,
} from "lucide-react";
import { Job } from "@/data/mockData";
import { toast } from "sonner";
import { useNotifications } from "@/hooks/use-notifications";
import { ValidationError, ApiError } from "@/lib/api";
import { UserSelector } from "./UserSelector"; // v2.0.1: User selection component
import { safeNumber } from "@/lib/adapters/utils";

interface AddEditJobModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (
    job: Omit<Job, "id" | "applicantCount" | "postedDate">,
  ) => Promise<void> | void;
  job?: Job | null;
  mode: "add" | "edit";
}

interface JobFormData {
  title: string;
  department: string;
  location: string;
  type: "full-time" | "part-time" | "contract" | "internship"; // v2.0.1: updated options
  workLocation: "onsite" | "remote" | "hybrid";
  salaryMin: number;
  salaryMax: number;
  currency: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
  skills: string[];
  status: "draft" | "active" | "paused" | "closed";
  priority: "low" | "medium" | "high" | "urgent"; // v2.0.1: added urgent
  closingDate: string;
  hiringManager: string;
  hiringManagerId?: number; // v2.0.1: hiring manager ID
  recruiter: string;
  recruiterId?: number; // v2.0.1: recruiter ID
  experienceLevel: "entry" | "mid" | "senior" | "lead"; // v2.0.1: specific options
  educationRequired: string;
  companyCulture: string;
  positions: number; // v2.0.1: number of positions
}

const departmentOptions = [
  "Phòng Phần mềm",
  "Phòng Kinh doanh",
  "Phòng Hành chính nhân sự",
  "Phòng Tài chính kế toán",
  "Phòng Marketing",
  "Phòng Vận hành",
  "Phòng Giải pháp kỹ thuật",
  "VP Hà Nội",
];

const skillSuggestions = [
  "React",
  "TypeScript",
  "JavaScript",
  "Node.js",
  "Python",
  "Java",
  "AWS",
  "Docker",
  "Kubernetes",
  "Product Strategy",
  "Data Analysis",
  "Figma",
  "UI/UX",
  "Machine Learning",
  "DevOps",
  "Leadership",
  "Communication",
  "Problem Solving",
];

export const AddEditJobModal = ({
  isOpen,
  onClose,
  onSubmit,
  job,
  mode,
}: AddEditJobModalProps) => {
  const [currentTab, setCurrentTab] = useState("basic");
  const [requirements, setRequirements] = useState<string[]>([]);
  const [responsibilities, setResponsibilities] = useState<string[]>([]);
  const [benefits, setBenefits] = useState<string[]>([]);
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [newRequirement, setNewRequirement] = useState("");
  const [newResponsibility, setNewResponsibility] = useState("");
  const [newBenefit, setNewBenefit] = useState("");
  const [customSkill, setCustomSkill] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const notifications = useNotifications();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<JobFormData>({
    defaultValues: {
      type: "full-time",
      workLocation: "hybrid",
      currency: "VND", // v2.0.1: default to VND
      status: "draft",
      priority: "medium",
      experienceLevel: "mid",
      positions: 1, // v2.0.1: default 1 position
      educationRequired: "",
      companyCulture: "",
      hiringManager: "",
      hiringManagerId: undefined,
      recruiter: "",
      recruiterId: undefined,
    },
  });

  useEffect(() => {
    if (job && mode === "edit") {
      // Populate form with existing job data
      setValue("title", job.title);
      setValue("department", job.department);
      setValue("location", job.location);
      setValue("type", job.type);
      setValue("workLocation", job.workLocation || "hybrid"); // v2.0.1: work location
      setValue("salaryMin", safeNumber(job.salary.min));
      setValue("salaryMax", safeNumber(job.salary.max));
      setValue("currency", job.salary.currency);
      setValue("description", job.description);
      setValue("status", job.status);
      setValue("priority", job.priority);
      setValue("closingDate", job.closingDate);
      setValue("hiringManager", job.hiringManager);
      setValue("hiringManagerId", job.hiringManagerId); // v2.0.1: hiring manager ID
      setValue("recruiter", job.recruiter || ""); // v2.0.1: recruiter
      setValue("recruiterId", job.recruiterId); // v2.0.1: recruiter ID
      setValue("experienceLevel", job.experienceLevel || "mid"); // v2.0.1: experience level
      setValue("educationRequired", job.educationRequired || ""); // v2.0.1: education
      setValue("companyCulture", job.companyCulture || ""); // v2.0.1: company culture
      setValue("positions", job.positions || 1); // v2.0.1: positions

      setRequirements(job.requirements || []);
      setResponsibilities(job.responsibilities || []); // v2.0.1: responsibilities
      setSelectedSkills(job.skills || []);
      setBenefits(job.benefits || []);
    }
  }, [job, mode, setValue]);

  const handleClose = () => {
    reset();
    setRequirements([]);
    setResponsibilities([]);
    setBenefits([]);
    setSelectedSkills([]);
    setCurrentTab("basic");
    setIsSubmitting(false);
    onClose();
  };

  const addItem = (
    item: string,
    setItem: React.Dispatch<React.SetStateAction<string>>,
    items: string[],
    setItems: React.Dispatch<React.SetStateAction<string[]>>,
  ) => {
    if (item.trim() && !items.includes(item.trim())) {
      setItems([...items, item.trim()]);
      setItem("");
    }
  };

  const removeItem = (
    index: number,
    items: string[],
    setItems: React.Dispatch<React.SetStateAction<string[]>>,
  ) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const handleSkillAdd = (skill: string) => {
    if (skill && !selectedSkills.includes(skill)) {
      setSelectedSkills([...selectedSkills, skill]);
    }
    setCustomSkill("");
  };

  const handleSkillRemove = (skill: string) => {
    setSelectedSkills(selectedSkills.filter((s) => s !== skill));
  };

  const onFormSubmit = async (data: JobFormData) => {
    if (isSubmitting) return; // Prevent double submission

    setIsSubmitting(true);
    try {
      // v2.0.1: Validate that either hiring manager ID or name is provided
      if (!data.hiringManagerId && !data.hiringManager?.trim()) {
        toast.error("Validation Error", {
          description: "Please select a hiring manager or enter one manually.",
        });
        return;
      }

      // Validate that required arrays have at least one item
      if (!requirements.length) {
        toast.error("Validation Error", {
          description: "Please add at least one job requirement.",
        });
        return;
      }

      if (!responsibilities.length) {
        toast.error("Validation Error", {
          description: "Please add at least one responsibility.",
        });
        return;
      }
      const jobData: Omit<Job, "id" | "applicantCount" | "postedDate"> = {
        title: data.title,
        department: data.department,
        location: data.location,
        type: data.type,
        workLocation: data.workLocation, // v2.0.1: work location
        salary: {
          min: data.salaryMin.toString(),
          max: data.salaryMax.toString(),
          currency: data.currency,
        },
        description: data.description,
        requirements,
        responsibilities, // v2.0.1: responsibilities
        benefits,
        skills: selectedSkills, // v2.0.1: skills array
        status: data.status,
        priority: data.priority,
        closingDate: data.closingDate,
        hiringManager: data.hiringManager,
        hiringManagerId: data.hiringManagerId, // v2.0.1: hiring manager ID
        recruiter: data.recruiter, // v2.0.1: recruiter
        recruiterId: data.recruiterId, // v2.0.1: recruiter ID
        experienceLevel: data.experienceLevel, // v2.0.1: experience level
        educationRequired: data.educationRequired, // v2.0.1: education requirement
        companyCulture: data.companyCulture, // v2.0.1: company culture
        positions: data.positions || 1, // v2.0.1: number of positions
        viewCount: 0,
      };

      await onSubmit(jobData);
      // Only close modal if no error occurred
      // The parent component will handle closing the modal on success
    } catch (error) {
      console.error("Form submission error:", error);

      // Show appropriate error message to user based on error type
      if (error instanceof ValidationError) {
        // Show validation errors with Vietnamese messages
        const formattedErrors = error.getFormattedErrors();
        toast.error(`Lỗi xác thực: ${error.message}`, {
          description: formattedErrors,
          duration: 5000,
        });
      } else if (error instanceof ApiError) {
        // Show API errors with status code
        toast.error(`Lỗi API (${error.statusCode}): ${error.message}`, {
          duration: 5000,
        });
      } else if (error instanceof Error) {
        // Handle generic errors
        const message = error.message || "Đ�� xảy ra lỗi không xác định";
        toast.error(`Lỗi: ${message}`, {
          duration: 5000,
        });
      } else {
        // Handle any other error types
        toast.error("Đã xảy ra lỗi không xác định. Vui lòng thử lại.", {
          duration: 5000,
        });
      }

      // Modal stays open so user can fix the issue and retry
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Building2 className="w-5 h-5 text-primary" />
            </div>
            {mode === "edit" ? "Chỉnh sửa công việc" : "Tạo công việc mới"}
          </DialogTitle>
          <DialogDescription>
            {mode === "edit"
              ? "Cập nhật thông tin chi tiết và yêu cầu công việc."
              : "Tạo bài đăng tuyển dụng toàn diện để thu hút các ứng viên tốt nhất."}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
          <Tabs value={currentTab} onValueChange={setCurrentTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">Thông tin công việc</TabsTrigger>
              <TabsTrigger value="requirements">Yêu cầu</TabsTrigger>
              <TabsTrigger value="additional">Thông tin thêm</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="title">Job Title *</Label>
                  <Input
                    id="title"
                    {...register("title", {
                      required: "Job title is required",
                      minLength: {
                        value: 3,
                        message: "Job title must be at least 3 characters",
                      },
                      maxLength: {
                        value: 255,
                        message: "Job title cannot exceed 255 characters",
                      },
                    })}
                    placeholder="e.g., Senior Frontend Developer"
                    className="rounded-xl"
                  />
                  {errors.title && (
                    <p className="text-sm text-destructive">
                      {errors.title.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="department">Phòng *</Label>
                  <Select
                    value={watch("department")}
                    onValueChange={(value) => {
                      setValue("department", value, { shouldValidate: true });
                    }}
                    required
                  >
                    <SelectTrigger className="rounded-xl">
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      {departmentOptions.map((dept) => (
                        <SelectItem key={dept} value={dept}>
                          {dept}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.department && (
                    <p className="text-sm text-destructive">
                      {errors.department.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location">Chi nhánh</Label>
                  <Input
                    id="location"
                    {...register("location")}
                    placeholder="e.g., Trụ sở TP HCM"
                    className="rounded-xl"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Trạng thái</Label>
                  <Select
                    value={watch("status")}
                    onValueChange={(value) => setValue("status", value as any)}
                  >
                    <SelectTrigger className="rounded-xl">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="paused">Paused</SelectItem>
                      <SelectItem value="closed">Closed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type">Loại công việc</Label>
                  <Select
                    value={watch("type")}
                    onValueChange={(value) => setValue("type", value as any)}
                  >
                    <SelectTrigger className="rounded-xl">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="full-time">Full-time</SelectItem>
                      <SelectItem value="part-time">Part-time</SelectItem>
                      <SelectItem value="contract">Contract</SelectItem>
                      <SelectItem value="internship">Internship</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">Ưu tiên</Label>
                  <Select
                    value={watch("priority")}
                    onValueChange={(value) =>
                      setValue("priority", value as any)
                    }
                  >
                    <SelectTrigger className="rounded-xl">
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="positions">Số lượng</Label>
                    <Input
                      id="positions"
                      type="number"
                      min="1"
                      max="100"
                      {...register("positions", {
                        valueAsNumber: true,
                        min: { value: 1, message: "Must be at least 1" },
                        max: { value: 100, message: "Cannot exceed 100" },
                      })}
                      placeholder="1"
                      className="rounded-xl"
                    />
                    {errors.positions && (
                      <p className="text-sm text-destructive">
                        {errors.positions.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="workLocation">Work Location</Label>
                    <Select
                      value={watch("workLocation")}
                      onValueChange={(value) =>
                        setValue("workLocation", value as any)
                      }
                    >
                      <SelectTrigger className="rounded-xl">
                        <SelectValue placeholder="Select work location" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="onsite">On-site</SelectItem>
                        <SelectItem value="remote">Remote</SelectItem>
                        <SelectItem value="hybrid">Hybrid</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="experienceLevel">Cấp độ kinh nghiệm</Label>
                    <Select
                      value={watch("experienceLevel")}
                      onValueChange={(value) =>
                        setValue("experienceLevel", value as any)
                      }
                    >
                      <SelectTrigger className="rounded-xl">
                        <SelectValue placeholder="Chọn cấp độ kinh nghiệm" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="entry">Mới ra trường</SelectItem>
                        <SelectItem value="junior">Nhân viên</SelectItem>
                        <SelectItem value="mid">Trung cấp</SelectItem>
                        <SelectItem value="senior">Cao cấp</SelectItem>
                        <SelectItem value="lead">Trưởng nhóm</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <Label>Mức lương</Label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="salaryMin">Tối thiểu</Label>
                    <Input
                      id="salaryMin"
                      type="number"
                      {...register("salaryMin", {
                        valueAsNumber: true,
                        required: "Minimum salary is required",
                        min: {
                          value: 0,
                          message: "Salary must be positive",
                        },
                        validate: (value) =>
                          !watch("salaryMax") ||
                          value <= watch("salaryMax") ||
                          "Minimum salary must be less than maximum",
                      })}
                      placeholder="120000"
                      className={`rounded-xl ${errors.salaryMin ? "border-destructive" : ""}`}
                    />
                    {errors.salaryMin && (
                      <p className="text-sm text-destructive">
                        {errors.salaryMin.message}
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="salaryMax">Tối đa</Label>
                    <Input
                      id="salaryMax"
                      type="number"
                      {...register("salaryMax", {
                        valueAsNumber: true,
                        required: "Maximum salary is required",
                        min: {
                          value: 0,
                          message: "Salary must be positive",
                        },
                        validate: (value) =>
                          !watch("salaryMin") ||
                          value >= watch("salaryMin") ||
                          "Maximum salary must be greater than minimum",
                      })}
                      placeholder="140000"
                      className={`rounded-xl ${errors.salaryMax ? "border-destructive" : ""}`}
                    />
                    {errors.salaryMax && (
                      <p className="text-sm text-destructive">
                        {errors.salaryMax.message}
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="currency">Tiền tệ</Label>
                    <Select
                      value={watch("currency")}
                      onValueChange={(value) => setValue("currency", value)}
                    >
                      <SelectTrigger className="rounded-xl">
                        <SelectValue placeholder="Chọn tiền tệ" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="VND">VND</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="description">Mô tả công việc</Label>
                  <Textarea
                    id="description"
                    {...register("description", {
                      required: "Job description is required",
                      minLength: {
                        value: 50,
                        message: "Description must be at least 50 characters",
                      },
                      maxLength: {
                        value: 5000,
                        message: "Description cannot exceed 5000 characters",
                      },
                    })}
                    placeholder="Mô tả vị trí, trách nhiệm và những điều hấp dẫn của công việc..."
                    rows={6}
                    className={`rounded-xl ${errors.description ? "border-destructive" : ""}`}
                  />
                  {errors.description && (
                    <p className="text-sm text-destructive">
                      {errors.description.message}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {
                    <UserSelector
                      label="Hiring Manager *"
                      placeholder="Select hiring manager"
                      value={watch("hiringManagerId")}
                      onValueChange={(value) => {
                        setValue("hiringManagerId", value);
                        if (value) setValue("hiringManager", "");
                      }}
                      userType="hiring-managers"
                      error={errors.hiringManagerId?.message}
                    />
                  }

                  {/* v2.0.1: Recruiter Selector */}
                  {
                    <UserSelector
                      label="Recruiter"
                      placeholder="Select recruiter (optional)"
                      value={watch("recruiterId")}
                      onValueChange={(value) => {
                        setValue("recruiterId", value);
                        if (value) setValue("recruiter", "");
                      }}
                      userType="recruiters"
                      error={errors.recruiterId?.message}
                    />
                  }
                </div>

                {/* Fallback text inputs for manual entry if needed */}

                <div className="space-y-2">
                  <Label htmlFor="educationRequired">Yêu cầu về học vấn</Label>
                  <Textarea
                    id="educationRequired"
                    {...register("educationRequired")}
                    placeholder="Tốt nghiệp Đại học chuyên ngành Tin học hoặc liên quan"
                    className="rounded-xl"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="requirements" className="space-y-6">
              {/* Requirements */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Yêu cầu công việc
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2 mb-3">
                    {requirements.map((req, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {req}
                        <button
                          type="button"
                          onClick={() =>
                            removeItem(index, requirements, setRequirements)
                          }
                          className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Input
                      value={newRequirement}
                      onChange={(e) => setNewRequirement(e.target.value)}
                      placeholder="Thêm yêu cầu"
                      className="rounded-xl"
                      onKeyPress={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          addItem(
                            newRequirement,
                            setNewRequirement,
                            requirements,
                            setRequirements,
                          );
                        }
                      }}
                    />
                    <Button
                      type="button"
                      onClick={() =>
                        addItem(
                          newRequirement,
                          setNewRequirement,
                          requirements,
                          setRequirements,
                        )
                      }
                      className="rounded-xl"
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Responsibilities */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    Trách nhiệm
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2 mb-3">
                    {responsibilities.map((resp, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {resp}
                        <button
                          type="button"
                          onClick={() =>
                            removeItem(
                              index,
                              responsibilities,
                              setResponsibilities,
                            )
                          }
                          className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Input
                      value={newResponsibility}
                      onChange={(e) => setNewResponsibility(e.target.value)}
                      placeholder="Add a responsibility"
                      className="rounded-xl"
                      onKeyPress={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          addItem(
                            newResponsibility,
                            setNewResponsibility,
                            responsibilities,
                            setResponsibilities,
                          );
                        }
                      }}
                    />
                    <Button
                      type="button"
                      onClick={() =>
                        addItem(
                          newResponsibility,
                          setNewResponsibility,
                          responsibilities,
                          setResponsibilities,
                        )
                      }
                      className="rounded-xl"
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Skills */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Sparkles className="w-5 h-5" />
                    Kỹ năng yêu cầu
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2 mb-3">
                    {selectedSkills.map((skill) => (
                      <Badge
                        key={skill}
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {skill}
                        <button
                          type="button"
                          onClick={() => handleSkillRemove(skill)}
                          className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Input
                      value={customSkill}
                      onChange={(e) => setCustomSkill(e.target.value)}
                      placeholder="Thêm kỹ năng"
                      className="rounded-xl"
                      onKeyPress={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          handleSkillAdd(customSkill);
                        }
                      }}
                    />
                    <Button
                      type="button"
                      onClick={() => handleSkillAdd(customSkill)}
                      className="rounded-xl"
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {skillSuggestions
                      .filter((skill) => !selectedSkills.includes(skill))
                      .slice(0, 10)
                      .map((skill) => (
                        <Button
                          key={skill}
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => handleSkillAdd(skill)}
                          className="text-xs rounded-lg"
                        >
                          {skill}
                        </Button>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="additional" className="space-y-6">
              {/* Benefits */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Award className="w-5 h-5" />
                    Phúc lợi & Ưu đãi
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2 mb-3">
                    {benefits.map((benefit, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {benefit}
                        <button
                          type="button"
                          onClick={() =>
                            removeItem(index, benefits, setBenefits)
                          }
                          className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Input
                      value={newBenefit}
                      onChange={(e) => setNewBenefit(e.target.value)}
                      placeholder="Thêm phúc lợi"
                      className="rounded-xl"
                      onKeyPress={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          addItem(
                            newBenefit,
                            setNewBenefit,
                            benefits,
                            setBenefits,
                          );
                        }
                      }}
                    />
                    <Button
                      type="button"
                      onClick={() =>
                        addItem(
                          newBenefit,
                          setNewBenefit,
                          benefits,
                          setBenefits,
                        )
                      }
                      className="rounded-xl"
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Job Settings */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="closingDate">Closing Date</Label>
                  <Input
                    id="closingDate"
                    type="date"
                    {...register("closingDate")}
                    className="rounded-xl"
                  />
                </div>
              </div>

              {/* v2.0.1: Additional Job Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="educationRequired">Bằng cấp yêu cầu</Label>
                  <Textarea
                    id="educationRequired"
                    {...register("educationRequired")}
                    placeholder="e.g., Bachelor's degree in Computer Science or related field"
                    className="rounded-xl"
                    rows={2}
                  />
                </div>

                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="companyCulture">
                    Văn hóa & Môi trường Công ty
                  </Label>
                  <Textarea
                    id="companyCulture"
                    {...register("companyCulture")}
                    placeholder="Describe the company culture, work environment, and team dynamics"
                    className="rounded-xl"
                    rows={3}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <FormActions
            onCancel={handleClose}
            cancelText="Hủy"
            submitText={
              mode === "edit" ? "Cập nhật công việc" : "Tạo công việc"
            }
            submitLoadingText={
              mode === "edit" ? "Đang cập nhật..." : "Đang tạo..."
            }
            isSubmitting={isSubmitting}
            disableCancelWhileSubmitting={true}
          />
        </form>
      </DialogContent>
    </Dialog>
  );
};
