import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  MessageSquare,
  Trash2,
  Edit,
  Plus,
  User,
  AlertCircle,
} from "lucide-react";
import { safeFormatDistanceToNow } from "@/lib/utils";
import {
  CandidateNote,
  CandidateNotes as NotesArray,
  validateNoteContent,
} from "@/lib/types/candidateNotes";
import { useCurrentUser } from "@/hooks/useCurrentUser";
import { toast } from "sonner";

// Editable CandidateNotes props interface for modals
export interface CandidateNotesEditorProps {
  candidateId: string;
  notes: NotesArray;
  onAddNote: (content: string) => Promise<void>;
  onUpdateNote: (noteIndex: number, content: string) => Promise<void>;
  onDeleteNote: (noteIndex: number) => Promise<void>;
  disabled?: boolean;
  maxNoteLength?: number;
}

// Editable CandidateNotes component for modals
export const CandidateNotesEditor: React.FC<CandidateNotesEditorProps> = ({
  candidateId,
  notes = [],
  onAddNote,
  onUpdateNote,
  onDeleteNote,
  disabled = false,
  maxNoteLength = 2000,
}) => {
  const [newNote, setNewNote] = useState("");
  const [editingNoteIndex, setEditingNoteIndex] = useState<number | null>(null);
  const [editContent, setEditContent] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { profile: currentUser } = useCurrentUser();

  // Character count for new note
  const remainingChars = maxNoteLength - newNote.length;

  const handleSaveNote = async () => {
    const validation = validateNoteContent(newNote.trim(), maxNoteLength);

    if (!validation.isValid) {
      validation.errors.forEach((error) => toast.error(error));
      return;
    }

    if (!currentUser) {
      toast.error("Không thể xác định người dùng hiện tại");
      return;
    }

    setIsSubmitting(true);
    try {
      await onAddNote(newNote.trim());
      setNewNote("");
      toast.success("Đã thêm ghi chú thành công");
    } catch (error) {
      toast.error("Không thể thêm ghi chú. Vui lòng thử lại.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditNote = (note: CandidateNote, index: number) => {
    setEditingNoteIndex(index);
    setEditContent(note.content);
  };

  const handleSaveEdit = async (noteIndex: number) => {
    const validation = validateNoteContent(editContent.trim(), maxNoteLength);

    if (!validation.isValid) {
      validation.errors.forEach((error) => toast.error(error));
      return;
    }

    setIsSubmitting(true);
    try {
      await onUpdateNote(noteIndex, editContent.trim());
      setEditingNoteIndex(null);
      setEditContent("");
      toast.success("Đã cập nhật ghi chú thành công");
    } catch (error) {
      toast.error("Không thể cập nhật ghi chú. Vui lòng thử lại.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingNoteIndex(null);
    setEditContent("");
  };

  const handleDeleteNote = async (noteIndex: number) => {
    if (window.confirm("Bạn có chắc chắn muốn xóa ghi chú này?")) {
      setIsSubmitting(true);
      try {
        await onDeleteNote(noteIndex);
        toast.success("Đã xóa ghi chú thành công");
      } catch (error) {
        toast.error("Không thể xóa ghi chú. Vui lòng thử lại.");
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const getNoteAuthorInitials = (note: CandidateNote): string => {
    return note.created_by
      .split(" ")
      .map((name) => name[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const isNoteUpdated = (note: CandidateNote): boolean => {
    return note.updated_at && note.updated_at !== note.created_at;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="w-5 h-5" />
          Ghi chú & Phản hồi
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Add new note */}
        {!disabled && (
          <div className="space-y-3 p-4 border rounded-lg bg-muted/20">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Thêm ghi chú</label>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span
                  className={
                    remainingChars < 100
                      ? "text-orange-600"
                      : remainingChars < 0
                        ? "text-red-600"
                        : ""
                  }
                >
                  {remainingChars} ký tự còn lại
                </span>
                {remainingChars < 0 && (
                  <AlertCircle className="w-3 h-3 text-red-600" />
                )}
              </div>
            </div>
            <Textarea
              value={newNote}
              onChange={(e) => setNewNote(e.target.value)}
              placeholder="Thêm ghi chú của bạn về ứng viên này..."
              rows={3}
              maxLength={maxNoteLength}
              className={
                remainingChars < 0 ? "border-red-300 focus:border-red-500" : ""
              }
            />
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                {currentUser && (
                  <>
                    <User className="w-3 h-3" />
                    <span>Đăng bởi: {currentUser.fullName}</span>
                  </>
                )}
              </div>
              <Button
                size="sm"
                onClick={handleSaveNote}
                disabled={!newNote.trim() || remainingChars < 0 || isSubmitting}
              >
                <Plus className="w-4 h-4 mr-2" />
                {isSubmitting ? "Đang lưu..." : "Lưu ghi chú"}
              </Button>
            </div>
          </div>
        )}

        {/* Notes list */}
        {notes.length > 0 ? (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Lịch sử ghi chú ({notes.length})</h4>
            </div>
            {notes.map((note, index) => (
              <div
                key={`note-${index}`}
                className="p-4 border rounded-lg bg-card hover:bg-muted/20 transition-colors"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Avatar className="w-6 h-6">
                      <AvatarFallback className="text-xs">
                        {getNoteAuthorInitials(note)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col">
                      <span className="text-sm font-medium">
                        {note.created_by}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {safeFormatDistanceToNow(note.created_at, {
                          addSuffix: true,
                        })}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {isNoteUpdated(note) && (
                      <Badge variant="outline" className="text-xs">
                        Đã chỉnh sửa
                      </Badge>
                    )}
                    {!disabled && (
                      <>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditNote(note, index)}
                          disabled={isSubmitting}
                        >
                          <Edit className="w-3 h-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteNote(index)}
                          className="text-destructive hover:text-destructive"
                          disabled={isSubmitting}
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>

                {editingNoteIndex === index ? (
                  <div className="space-y-2">
                    <Textarea
                      value={editContent}
                      onChange={(e) => setEditContent(e.target.value)}
                      rows={3}
                      maxLength={maxNoteLength}
                    />
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-muted-foreground">
                        {maxNoteLength - editContent.length} ký tự còn lại
                      </span>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleSaveEdit(index)}
                          disabled={
                            isSubmitting || editContent.length > maxNoteLength
                          }
                        >
                          {isSubmitting ? "Đang lưu..." : "Lưu"}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleCancelEdit}
                          disabled={isSubmitting}
                        >
                          Hủy
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <>
                    <p className="text-sm whitespace-pre-line mb-2">
                      {note.content}
                    </p>
                    {isNoteUpdated(note) && (
                      <div className="text-xs text-muted-foreground mt-2 p-2 bg-muted/30 rounded">
                        <div className="flex items-center gap-1">
                          <Edit className="w-3 h-3" />
                          <span>
                            Cập nhật bởi {note.updated_by} -{" "}
                            {safeFormatDistanceToNow(note.updated_at!, {
                              addSuffix: true,
                            })}
                          </span>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <MessageSquare className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p className="text-sm">Chưa có ghi chú nào</p>
            <p className="text-xs">
              Ghi chú và phản hồi sẽ xuất hiện ở đây khi được thêm vào
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CandidateNotesEditor;
