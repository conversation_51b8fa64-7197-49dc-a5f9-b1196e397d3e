import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { MessageSquare, Edit } from "lucide-react";
import { safeFormatDistanceToNow } from "@/lib/utils";
import {
  CandidateNote,
  CandidateNotes as NotesArray,
} from "@/lib/types/candidateNotes";

// Read-only CandidateNotes props interface
export interface CandidateNotesProps {
  candidateId: string;
  notes: NotesArray;
  readOnly?: boolean;
}

// Read-only CandidateNotes component for detail view
export const CandidateNotes: React.FC<CandidateNotesProps> = ({
  candidateId,
  notes = [],
  readOnly = true,
}) => {
  const getNoteAuthorInitials = (note: CandidateNote): string => {
    return note.created_by
      .split(" ")
      .map((name) => name[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const isNoteUpdated = (note: CandidateNote): boolean => {
    return note.updated_at && note.updated_at !== note.created_at;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="w-5 h-5" />
          Ghi chú & Phản hồi
          {readOnly && (
            <Badge variant="secondary" className="text-xs ml-auto">
              Chỉ xem
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Notes list - Read only */}
        {notes.length > 0 ? (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Lịch sử ghi chú ({notes.length})</h4>
            </div>
            {notes.map((note, index) => (
              <div
                key={`note-${index}`}
                className="p-4 border rounded-lg bg-card hover:bg-muted/20 transition-colors"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Avatar className="w-6 h-6">
                      <AvatarFallback className="text-xs">
                        {getNoteAuthorInitials(note)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col">
                      <span className="text-sm font-medium">
                        {note.created_by}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {safeFormatDistanceToNow(note.created_at, {
                          addSuffix: true,
                        })}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {isNoteUpdated(note) && (
                      <Badge variant="outline" className="text-xs">
                        Đã chỉnh sửa
                      </Badge>
                    )}
                  </div>
                </div>

                <p className="text-sm whitespace-pre-line mb-2">
                  {note.content}
                </p>
                {isNoteUpdated(note) && (
                  <div className="text-xs text-muted-foreground mt-2 p-2 bg-muted/30 rounded">
                    <div className="flex items-center gap-1">
                      <Edit className="w-3 h-3" />
                      <span>
                        Cập nhật bởi {note.updated_by} -{" "}
                        {safeFormatDistanceToNow(note.updated_at!, {
                          addSuffix: true,
                        })}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <MessageSquare className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p className="text-sm">Chưa có ghi chú nào</p>
            <p className="text-xs">
              Ghi chú và phản hồi sẽ xuất hiện ở đây khi được thêm vào
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CandidateNotes;
