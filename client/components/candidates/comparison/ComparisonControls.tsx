import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { BarChart3, FileText, Trash2, Filter, Building2 } from "lucide-react";
import { useJobs } from "@/hooks/useApi";

interface ComparisonControlsProps {
  selectedCount: number;
  comparisonMode: "detailed" | "summary";
  onModeChange: (mode: "detailed" | "summary") => void;
  jobFilterId: string;
  onJobFilterChange: (jobId: string) => void;
  onClearAll: () => void;
}

export const ComparisonControls: React.FC<ComparisonControlsProps> = ({
  selectedCount,
  comparisonMode,
  onModeChange,
  jobFilterId,
  onJobFilterChange,
  onClearAll,
}) => {
  // Fetch jobs for filtering
  const { data: jobsResponse } = useJobs({
    per_page: 100,
    status: "active",
    sort: "title",
  });

  const jobs = jobsResponse?.data || [];

  if (selectedCount === 0) {
    return null;
  }

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-4">
            {/* Selection Count */}
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-sm">
                {selectedCount} ứng viên được chọn
              </Badge>
              {selectedCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClearAll}
                  className="h-6 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="w-3 h-3 mr-1" />
                  Xóa
                </Button>
              )}
            </div>
          </div>

          {/* View Mode Toggle */}
          <Tabs value={comparisonMode} onValueChange={onModeChange}>
            <TabsList className="grid w-full grid-cols-2 h-8">
              <TabsTrigger
                value="detailed"
                className="text-xs flex items-center gap-1"
              >
                <FileText className="w-3 h-3" />
                Chi tiết
              </TabsTrigger>
              <TabsTrigger
                value="summary"
                className="text-xs flex items-center gap-1"
              >
                <BarChart3 className="w-3 h-3" />
                Tóm tắt
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {/* Quick Stats */}
        {selectedCount > 1 && (
          <div className="mt-4 pt-4 border-t">
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-sm text-muted-foreground">Chức năng</div>
                <div className="font-medium capitalize">{comparisonMode}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Ứng viên</div>
                <div className="font-medium">{selectedCount}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Tối đa</div>
                <div className="font-medium">4</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Còn lại</div>
                <div className="font-medium">{4 - selectedCount}</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
