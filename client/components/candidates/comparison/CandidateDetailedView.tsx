import React, { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  X,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Star,
  Briefcase,
  GraduationCap,
  Award,
  TrendingUp,
  Brain,
  Target,
  Users,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Loader2,
  Zap,
  FileText,
  DollarSign,
  Clock,
  ShieldCheck,
  Eye,
} from "lucide-react";
import { Candidate } from "@/data/mockData";
import { useAIAnalysis } from "@/hooks/useAIAnalysis";
import { useNavigate } from "react-router-dom";

interface CandidateDetailedViewProps {
  candidate: Candidate;
  onRemove: (candidateId: string) => void;
  onUpdate: (candidateId: string, updates: any) => void;
  jobFilterId?: string;
}

export const CandidateDetailedView: React.FC<CandidateDetailedViewProps> = ({
  candidate,
  onRemove,
  onUpdate,
  jobFilterId,
}) => {
  const [isLoadingAnalysis, setIsLoadingAnalysis] = useState(false);
  const [showFullAnalysis, setShowFullAnalysis] = useState(false);
  const navigate = useNavigate();

  // Load AI analysis
  const {
    analysis,
    isLoading: analysisLoading,
    error: analysisError,
    generateAnalysis,
  } = useAIAnalysis({
    candidateId: candidate.id,
    jobPostingId:
      jobFilterId !== "all" ? parseInt(jobFilterId || "0") : undefined,
    autoLoad: true,
  });

  useEffect(() => {
    if (analysis && !candidate.analysisData) {
      onUpdate(candidate.id, {
        analysisData: analysis,
        isLoading: false,
      });
    } else if (!analysisLoading && !analysis && candidate.isLoading) {
      onUpdate(candidate.id, { isLoading: false });
    }
  }, [
    analysis,
    analysisLoading,
    candidate.analysisData,
    candidate.isLoading,
    candidate.id,
    onUpdate,
  ]);

  const handleGenerateAnalysis = async () => {
    setIsLoadingAnalysis(true);
    try {
      await generateAnalysis();
    } catch (error) {
      console.error("Failed to generate analysis:", error);
    } finally {
      setIsLoadingAnalysis(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "hired":
        return "bg-green-100 text-green-800";
      case "offer":
        return "bg-orange-100 text-orange-800";
      case "interview":
        return "bg-purple-100 text-purple-800";
      case "screening":
        return "bg-yellow-100 text-yellow-800";
      case "applied":
        return "bg-blue-100 text-blue-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };
  return (
    <Card className="h-fit">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={candidate.avatar} />
              <AvatarFallback className="bg-primary/10 text-primary">
                {candidate.initials}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg">{candidate.name}</CardTitle>
              <p className="text-sm text-muted-foreground">
                {candidate.position}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onRemove(candidate.id)}
            className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Basic Info */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm">
            <Mail className="w-4 h-4 text-muted-foreground" />
            <span className="truncate">{candidate.email}</span>
          </div>
          {candidate.phone && (
            <div className="flex items-center gap-2 text-sm">
              <Phone className="w-4 h-4 text-muted-foreground" />
              <span>{candidate.phone}</span>
            </div>
          )}
          <div className="flex items-center gap-2 text-sm">
            <MapPin className="w-4 h-4 text-muted-foreground" />
            <span>{candidate.location}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Clock className="w-4 h-4 text-muted-foreground" />
            <span>{candidate.experience}</span>
          </div>
        </div>

        <Separator />

        {/* Status and Rating */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Status</span>
            <Badge className={getStatusColor(candidate.status)}>
              {candidate.status.charAt(0).toUpperCase() +
                candidate.status.slice(1)}
            </Badge>
          </div>

          {candidate.rating && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Rating</span>
              <div className="flex items-center gap-1">
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span className="font-medium">{candidate.rating}/5</span>
              </div>
            </div>
          )}

          {candidate.aiScore !== undefined && candidate.aiScore > 0 && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">AI Score</span>
              <div className="flex items-center gap-2">
                <Progress value={candidate.aiScore} className="w-16 h-2" />
                <span
                  className={`font-medium ${getScoreColor(candidate.aiScore)}`}
                >
                  {candidate.aiScore}%
                </span>
              </div>
            </div>
          )}

          {candidate.salary && (
            <div className="flex items-center justify-between  hidden">
              <span className="text-sm font-medium">Salary</span>
              <div className="flex items-center gap-1">
                <DollarSign className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium">{candidate.salary}</span>
              </div>
            </div>
          )}

          {/* Salary Information */}
          <div className="space-y-2 hidden">
            {candidate.salary && (
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Current Salary</span>
                <div className="flex items-center gap-1">
                  <DollarSign className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium">
                    {candidate.salary}
                  </span>
                </div>
              </div>
            )}

            {(candidate.salaryExpectationMin ||
              candidate.salaryExpectationMax) && (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg mt-2">
                <div className="text-xs text-yellow-700 dark:text-yellow-300 font-medium mb-1 flex items-center gap-1">
                  <DollarSign className="w-3 h-3" />
                  Salary Expectation
                </div>
                <div className="space-y-1">
                  <div className="text-sm font-semibold text-yellow-800 dark:text-yellow-200">
                    {candidate.salaryExpectationMin &&
                    candidate.salaryExpectationMax
                      ? `${(candidate.salaryExpectationMin / 1000000).toFixed(1)}M - ${(candidate.salaryExpectationMax / 1000000).toFixed(1)}M`
                      : candidate.salaryExpectationMin
                        ? `From ${(candidate.salaryExpectationMin / 1000000).toFixed(1)}M`
                        : candidate.salaryExpectationMax
                          ? `Up to ${(candidate.salaryExpectationMax / 1000000).toFixed(1)}M`
                          : "Negotiable"}{" "}
                    {candidate.salaryCurrency || "VND"}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {candidate.salaryExpectationMin &&
                    candidate.salaryExpectationMax
                      ? `Range: ${candidate.salaryExpectationMin.toLocaleString()} - ${candidate.salaryExpectationMax.toLocaleString()}`
                      : candidate.salaryExpectationMin
                        ? `Minimum: ${candidate.salaryExpectationMin.toLocaleString()}`
                        : candidate.salaryExpectationMax
                          ? `Maximum: ${candidate.salaryExpectationMax.toLocaleString()}`
                          : "Open to negotiation"}
                  </div>
                </div>
              </div>
            )}

            {!candidate.salary &&
              !candidate.salaryExpectationMin &&
              !candidate.salaryExpectationMax && (
                <div className="text-xs text-muted-foreground italic">
                  No salary information provided
                </div>
              )}
          </div>
        </div>

        <Separator />

        {/* Skills */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium flex items-center gap-2">
            <Award className="w-4 h-4" />
            Skills ({candidate.skills?.length || 0})
          </h4>
          <div className="flex flex-wrap gap-1">
            {candidate.skills?.slice(0, 6).map((skill) => (
              <Badge key={skill} variant="secondary" className="text-xs">
                {skill}
              </Badge>
            ))}
            {candidate.skills && candidate.skills.length > 6 && (
              <Badge variant="secondary" className="text-xs">
                +{candidate.skills.length - 6} more
              </Badge>
            )}
          </div>
        </div>

        {/* Education */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium flex items-center gap-2">
            <GraduationCap className="w-4 h-4 text-blue-600" />
            Education & Qualifications
          </h4>

          {candidate.education ? (
            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
              <div className="text-xs text-blue-700 dark:text-blue-300 font-medium mb-1">
                Academic Background
              </div>
              <p className="text-sm text-muted-foreground">
                {candidate.education}
              </p>
            </div>
          ) : (
            <div className="text-xs text-muted-foreground italic">
              No education information provided
            </div>
          )}
        </div>

        {/* Career & Experience */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium flex items-center gap-2">
            <Briefcase className="w-4 h-4 text-purple-600" />
            Professional Experience
          </h4>

          {/* Experience Level */}
          <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg">
            <div className="text-xs text-purple-700 dark:text-purple-300 font-medium mb-1">
              Experience Level
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-3 h-3 text-purple-600" />
              <span className="text-sm font-medium">
                {candidate.experience}
              </span>
            </div>
          </div>

          {/* Work History */}
          {candidate.workHistory ? (
            <div className="bg-slate-50 dark:bg-slate-900/20 p-3 rounded-lg">
              <div className="text-xs text-slate-700 dark:text-slate-300 font-medium mb-1">
                Career Timeline
              </div>
              <p className="text-xs text-muted-foreground whitespace-pre-line">
                {candidate.workHistory}
              </p>
            </div>
          ) : (
            <div className="text-xs text-muted-foreground italic">
              No detailed work history provided
            </div>
          )}
        </div>

        <Separator />

        {/* AI Analysis */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium flex items-center gap-2">
            <Brain className="w-4 h-4 text-primary" />
            AI Analysis
          </h4>

          {analysisLoading || isLoadingAnalysis ? (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="w-5 h-5 animate-spin text-primary" />
              <span className="ml-2 text-sm text-muted-foreground">
                Loading analysis...
              </span>
            </div>
          ) : analysis ? (
            <div className="space-y-3">
              {/* Scores */}
              {analysis.scores && (
                <div className="space-y-2">
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex justify-between">
                      <span>Overall:</span>
                      <span className={getScoreColor(analysis.scores.overall)}>
                        {analysis.scores.overall}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Skills:</span>
                      <span className={getScoreColor(analysis.scores.skills)}>
                        {analysis.scores.skills}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Experience:</span>
                      <span
                        className={getScoreColor(analysis.scores.experience)}
                      >
                        {analysis.scores.experience}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Cultural Fit:</span>
                      <span
                        className={getScoreColor(analysis.scores.cultural_fit)}
                      >
                        {analysis.scores.cultural_fit}%
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Summary */}
              {analysis.summary && (
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <h5 className="text-xs font-medium">Summary</h5>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowFullAnalysis(!showFullAnalysis)}
                      className="h-auto p-0 text-xs text-primary"
                    >
                      {showFullAnalysis ? "Show Less" : "Show More"}
                    </Button>
                  </div>
                  <p
                    className={`text-xs text-muted-foreground ${showFullAnalysis ? "" : "line-clamp-3"}`}
                  >
                    {analysis.summary}
                  </p>
                </div>
              )}

              {/* Strengths */}
              {analysis.strengths && analysis.strengths.length > 0 && (
                <div>
                  <h5 className="text-xs font-medium mb-1 flex items-center gap-1">
                    <CheckCircle className="w-3 h-3 text-green-600" />
                    Strengths ({analysis.strengths.length})
                  </h5>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    {(showFullAnalysis
                      ? analysis.strengths
                      : analysis.strengths.slice(0, 3)
                    ).map((strength, index) => (
                      <li key={index}>• {strength}</li>
                    ))}
                    {!showFullAnalysis && analysis.strengths.length > 3 && (
                      <li
                        className="text-primary cursor-pointer"
                        onClick={() => setShowFullAnalysis(true)}
                      >
                        • +{analysis.strengths.length - 3} more...
                      </li>
                    )}
                  </ul>
                </div>
              )}

              {/* Weaknesses */}
              {analysis.weaknesses && analysis.weaknesses.length > 0 && (
                <div>
                  <h5 className="text-xs font-medium mb-1 flex items-center gap-1">
                    <AlertCircle className="w-3 h-3 text-orange-600" />
                    Areas for Improvement ({analysis.weaknesses.length})
                  </h5>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    {(showFullAnalysis
                      ? analysis.weaknesses
                      : analysis.weaknesses.slice(0, 2)
                    ).map((weakness, index) => (
                      <li key={index}>• {weakness}</li>
                    ))}
                    {!showFullAnalysis && analysis.weaknesses.length > 2 && (
                      <li
                        className="text-primary cursor-pointer"
                        onClick={() => setShowFullAnalysis(true)}
                      >
                        • +{analysis.weaknesses.length - 2} more...
                      </li>
                    )}
                  </ul>
                </div>
              )}

              {/* Job Matching */}
              {analysis.jobMatching && (
                <div>
                  <h5 className="text-xs font-medium mb-1 flex items-center gap-1">
                    <Target className="w-3 h-3 text-blue-600" />
                    Job Match: {analysis.scores.overall}%
                  </h5>
                  <Progress value={analysis.scores.overall} className="h-1" />

                  {/* Job Matching Details */}
                  <div className="mt-2 space-y-2">
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                      <div className="text-xs text-blue-700 dark:text-blue-300 font-medium mb-1">
                        Job Match Analysis
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {typeof analysis.jobMatching === "string"
                          ? analysis.jobMatching
                          : `Match score: ${analysis.scores.overall}% based on skills, experience, and requirements alignment.`}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Matching Criteria */}
              {analysis.jobMatching &&
                analysis.jobMatching.matchingCriteria &&
                analysis.jobMatching.matchingCriteria.length > 0 && (
                  <div>
                    <h5 className="text-xs font-medium mb-1 flex items-center gap-1">
                      <CheckCircle className="w-3 h-3 text-green-600" />
                      Matching Criteria (
                      {analysis.jobMatching.matchingCriteria.length})
                    </h5>
                    <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                      <ul className="text-xs text-muted-foreground space-y-1">
                        {(showFullAnalysis
                          ? analysis.jobMatching.matchingCriteria
                          : analysis.jobMatching.matchingCriteria.slice(0, 3)
                        ).map((criteria, index) => (
                          <li key={index} className="flex items-start gap-1">
                            <CheckCircle className="w-3 h-3 text-green-600 mt-0.5 flex-shrink-0" />
                            <span>{criteria}</span>
                          </li>
                        ))}
                        {!showFullAnalysis &&
                          analysis.jobMatching.matchingCriteria.length > 3 && (
                            <li
                              className="text-primary cursor-pointer flex items-center gap-1"
                              onClick={() => setShowFullAnalysis(true)}
                            >
                              <span>
                                +
                                {analysis.jobMatching.matchingCriteria.length -
                                  3}{" "}
                                more criteria...
                              </span>
                            </li>
                          )}
                      </ul>
                    </div>
                  </div>
                )}

              {/* Missing Requirements */}
              {analysis.jobMatching &&
                analysis.jobMatching.missingRequirements &&
                analysis.jobMatching.missingRequirements.length > 0 && (
                  <div>
                    <h5 className="text-xs font-medium mb-1 flex items-center gap-1">
                      <AlertCircle className="w-3 h-3 text-red-600" />
                      Missing Requirements (
                      {analysis.jobMatching.missingRequirements.length})
                    </h5>
                    <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg">
                      <ul className="text-xs text-muted-foreground space-y-1">
                        {(showFullAnalysis
                          ? analysis.jobMatching.missingRequirements
                          : analysis.jobMatching.missingRequirements.slice(0, 3)
                        ).map((requirement, index) => (
                          <li key={index} className="flex items-start gap-1">
                            <AlertCircle className="w-3 h-3 text-red-600 mt-0.5 flex-shrink-0" />
                            <span>{requirement}</span>
                          </li>
                        ))}
                        {!showFullAnalysis &&
                          analysis.jobMatching.missingRequirements.length >
                            3 && (
                            <li
                              className="text-primary cursor-pointer flex items-center gap-1"
                              onClick={() => setShowFullAnalysis(true)}
                            >
                              <span>
                                +
                                {analysis.jobMatching.missingRequirements
                                  .length - 3}{" "}
                                more requirements...
                              </span>
                            </li>
                          )}
                      </ul>
                    </div>
                  </div>
                )}
            </div>
          ) : (
            <div className="text-center py-3">
              <Brain className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-xs text-muted-foreground mb-2">
                No AI analysis available
              </p>
              <Button
                size="sm"
                onClick={handleGenerateAnalysis}
                disabled={isLoadingAnalysis}
                className="h-7 px-3 text-xs"
              >
                {isLoadingAnalysis ? (
                  <Loader2 className="w-3 h-3 animate-spin mr-1" />
                ) : (
                  <Zap className="w-3 h-3 mr-1" />
                )}
                Generate Analysis
              </Button>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="space-y-2 pt-2">
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              className="flex-1 h-8 text-xs"
              onClick={() =>
                window.open(`/candidates/detail/${candidate.id}`, "_blank")
              }
            >
              <FileText className="w-3 h-3 mr-1" />
              Xem chi tiết
            </Button>

            {candidate.resumeUrl && (
              <Button
                size="sm"
                variant="outline"
                className="w-full h-8 text-xs"
                onClick={() => window.open(candidate.resumeUrl, "_blank")}
              >
                <Eye className="w-3 h-3 mr-1" />
                CV
              </Button>
            )}
          </div>

          {/* CV Quick View */}
        </div>
      </CardContent>
    </Card>
  );
};
