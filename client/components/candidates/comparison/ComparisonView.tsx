import React, { useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

import {
  X,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Star,
  Briefcase,
  GraduationCap,
  Award,
  TrendingUp,
  Brain,
  Target,
  Users,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Loader2,
} from "lucide-react";
import { Candidate } from "@/data/mockData";
import { useAIAnalysis } from "@/hooks/useAIAnalysis";
import { CandidateDetailedView } from "./CandidateDetailedView";
import { CandidateSummaryView } from "./CandidateSummaryView";

interface ComparisonViewProps {
  candidates: Candidate[];
  mode: "detailed" | "summary";
  jobFilterId?: string;
  onRemoveCandidate: (candidateId: string) => void;
  onUpdateCandidate: (candidateId: string, updates: any) => void;
}

export const ComparisonView: React.FC<ComparisonViewProps> = ({
  candidates,
  mode,
  jobFilterId,
  onRemoveCandidate,
  onUpdateCandidate,
}) => {
  // Load AI analysis for each candidate
  useEffect(() => {
    candidates.forEach((candidate) => {
      if (!candidate.analysisData && !candidate.isLoading) {
        onUpdateCandidate(candidate.id, { isLoading: true });
      }
    });
  }, [candidates, onUpdateCandidate]);

  if (candidates.length === 0) {
    return null;
  }

  return (
    <div className="space-y-6">
      {mode === "detailed" ? (
        <DetailedComparisonView
          candidates={candidates}
          onRemoveCandidate={onRemoveCandidate}
          onUpdateCandidate={onUpdateCandidate}
          jobFilterId={jobFilterId}
        />
      ) : (
        <SummaryComparisonView
          candidates={candidates}
          onRemoveCandidate={onRemoveCandidate}
          onUpdateCandidate={onUpdateCandidate}
          jobFilterId={jobFilterId}
        />
      )}
    </div>
  );
};

interface ComparisonViewInternalProps {
  candidates: Candidate[];
  onRemoveCandidate: (candidateId: string) => void;
  onUpdateCandidate: (candidateId: string, updates: any) => void;
  jobFilterId?: string;
}

const DetailedComparisonView: React.FC<ComparisonViewInternalProps> = ({
  candidates,
  onRemoveCandidate,
  onUpdateCandidate,
  jobFilterId,
}) => {
  return (
    <div className="overflow-x-auto">
      <div className="min-w-fit flex gap-6 pb-4">
        {candidates.map((candidate) => (
          <div key={candidate.id} className="w-80 flex-shrink-0">
            <CandidateDetailedView
              candidate={candidate}
              onRemove={onRemoveCandidate}
              onUpdate={onUpdateCandidate}
              jobFilterId={jobFilterId}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

const SummaryComparisonView: React.FC<ComparisonViewInternalProps> = ({
  candidates,
  onRemoveCandidate,
  onUpdateCandidate,
  jobFilterId,
}) => {
  return (
    <div className="space-y-4">
      {/* Header Row */}
      <Card>
        <CardContent className="p-4">
          <div
            className="grid gap-4"
            style={{
              gridTemplateColumns: `200px repeat(${candidates.length}, 1fr)`,
            }}
          >
            <div className="font-semibold text-muted-foreground">Criteria</div>
            {candidates.map((candidate) => (
              <div key={candidate.id} className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={candidate.avatar} />
                    <AvatarFallback className="bg-primary/10 text-primary text-xs">
                      {candidate.initials}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm truncate">
                      {candidate.name}
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onRemoveCandidate(candidate.id)}
                    className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Comparison Rows */}
      <div className="space-y-2">
        {candidates.map((candidate) => (
          <CandidateSummaryView
            key={candidate.id}
            candidate={candidate}
            candidates={candidates}
            onRemove={onRemoveCandidate}
            onUpdate={onUpdateCandidate}
            jobFilterId={jobFilterId}
          />
        ))}
      </div>
    </div>
  );
};
