import React from "react";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import {
  X,
  Star,
  Award,
  Brain,
  Target,
  CheckCircle,
  AlertCircle,
  Zap,
  Clock,
  MapPin,
  DollarSign,
  GraduationCap,
  ShieldCheck,
  FileText,
  Eye,
  Briefcase,
} from "lucide-react";
import { Candidate } from "@/data/mockData";
import { useNavigate } from "react-router-dom";

interface CandidateSummaryViewProps {
  candidate: Candidate;
  candidates: Candidate[];
  onRemove: (candidateId: string) => void;
  onUpdate: (candidateId: string, updates: any) => void;
  jobFilterId?: string;
}

export const CandidateSummaryView: React.FC<CandidateSummaryViewProps> = ({
  candidate,
  candidates,
  onRemove,
  onUpdate,
  jobFilterId,
}) => {
  const navigate = useNavigate();
  const getStatusColor = (status: string) => {
    switch (status) {
      case "hired":
        return "bg-green-100 text-green-800";
      case "offer":
        return "bg-orange-100 text-orange-800";
      case "interview":
        return "bg-purple-100 text-purple-800";
      case "screening":
        return "bg-yellow-100 text-yellow-800";
      case "applied":
        return "bg-blue-100 text-blue-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getComparativeRank = (value: number | undefined, field: keyof Candidate) => {
    if (value === undefined) return null;
    
    const values = candidates
      .map(c => c[field] as number)
      .filter(v => v !== undefined && v > 0)
      .sort((a, b) => b - a);
    
    const rank = values.indexOf(value) + 1;
    const total = values.length;
    
    if (rank === 1) return "🥇 Best";
    if (rank === 2 && total > 2) return "🥈 2nd";
    if (rank === 3 && total > 3) return "🥉 3rd";
    return `#${rank}`;
  };

  const getSkillsOverlap = () => {
    const allOtherSkills = candidates
      .filter(c => c.id !== candidate.id)
      .flatMap(c => c.skills || []);
    
    const candidateSkills = candidate.skills || [];
    const commonSkills = candidateSkills.filter(skill => 
      allOtherSkills.includes(skill)
    );
    
    const uniqueSkills = candidateSkills.filter(skill => 
      !allOtherSkills.includes(skill)
    );

    return {
      common: commonSkills.length,
      unique: uniqueSkills.length,
      total: candidateSkills.length,
    };
  };

  const skillsOverlap = getSkillsOverlap();

  return (
    <Card className="relative">
      <CardContent className="p-4">
        <div className="flex items-start gap-4">
          {/* Candidate Header */}
          <div className="flex items-center gap-3 min-w-[200px]">
            <Avatar className="h-10 w-10">
              <AvatarImage src={candidate.avatar} />
              <AvatarFallback className="bg-primary/10 text-primary">
                {candidate.initials}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <h4 className="font-semibold text-sm truncate">{candidate.name}</h4>
              <p className="text-xs text-muted-foreground truncate">{candidate.position}</p>
              <Badge className={`${getStatusColor(candidate.status)} text-xs mt-1`}>
                {candidate.status}
              </Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onRemove(candidate.id)}
              className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>

          {/* Comparison Data */}
          <div className="flex-1 grid grid-cols-7 gap-4">
            {/* Rating */}
            <div className="text-center">
              <div className="text-xs text-muted-foreground mb-1">Rating</div>
              {candidate.rating ? (
                <div className="space-y-1">
                  <div className="flex items-center justify-center gap-1">
                    <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm font-medium">{candidate.rating}</span>
                  </div>
                  <div className="text-xs text-primary font-medium">
                    {getComparativeRank(candidate.rating, 'rating')}
                  </div>
                </div>
              ) : (
                <span className="text-xs text-muted-foreground">N/A</span>
              )}
            </div>

            {/* AI Score */}
            <div className="text-center">
              <div className="text-xs text-muted-foreground mb-1">AI Score</div>
              {candidate.aiScore !== undefined && candidate.aiScore > 0 ? (
                <div className="space-y-1">
                  <div className={`text-sm font-medium ${getScoreColor(candidate.aiScore)}`}>
                    {candidate.aiScore}%
                  </div>
                  <div className="text-xs text-primary font-medium">
                    {getComparativeRank(candidate.aiScore, 'aiScore')}
                  </div>
                </div>
              ) : (
                <span className="text-xs text-muted-foreground">N/A</span>
              )}
            </div>

            {/* Experience */}
            <div className="text-center">
              <div className="text-xs text-muted-foreground mb-1">Experience</div>
              <div className="text-sm font-medium flex items-center justify-center gap-1">
                <Clock className="w-3 h-3" />
                <span className="text-xs">{candidate.experience}</span>
              </div>
            </div>

            {/* Skills */}
            <div className="text-center">
              <div className="text-xs text-muted-foreground mb-1">Skills</div>
              <div className="space-y-1">
                <div className="text-sm font-medium">{skillsOverlap.total}</div>
                <div className="text-xs text-green-600">
                  {skillsOverlap.common} shared
                </div>
                <div className="text-xs text-blue-600">
                  {skillsOverlap.unique} unique
                </div>
              </div>
            </div>

            {/* Location */}
            <div className="text-center">
              <div className="text-xs text-muted-foreground mb-1">Location</div>
              <div className="text-xs flex items-center justify-center gap-1">
                <MapPin className="w-3 h-3" />
                <span className="truncate">{candidate.location}</span>
              </div>
            </div>

            {/* Salary */}
            <div className="text-center">
              <div className="text-xs text-muted-foreground mb-1">Compensation</div>
              <div className="space-y-1">
                {/* Current Salary */}
                {candidate.salary && (
                  <div className="bg-green-50 p-1 rounded">
                    <div className="text-xs text-green-700 font-medium">Current</div>
                    <div className="text-xs text-green-600">{candidate.salary}</div>
                  </div>
                )}

                {/* Expected Salary */}
                {(candidate.salaryExpectationMin || candidate.salaryExpectationMax) ? (
                  <div className="bg-yellow-50 p-1 rounded">
                    <div className="text-xs text-yellow-700 font-medium flex items-center justify-center gap-1">
                      <DollarSign className="w-2 h-2" />
                      Expected
                    </div>
                    <div className="text-xs text-yellow-600 font-medium">
                      {candidate.salaryExpectationMin && candidate.salaryExpectationMax
                        ? `${(candidate.salaryExpectationMin / 1000000).toFixed(0)}-${(candidate.salaryExpectationMax / 1000000).toFixed(0)}M`
                        : candidate.salaryExpectationMin
                        ? `${(candidate.salaryExpectationMin / 1000000).toFixed(0)}M+`
                        : `≤${(candidate.salaryExpectationMax! / 1000000).toFixed(0)}M`
                      }
                    </div>
                  </div>
                ) : (
                  !candidate.salary && <div className="text-xs text-muted-foreground">No data</div>
                )}
              </div>
            </div>

            {/* Education/Certs */}
            <div className="text-center">
              <div className="text-xs text-muted-foreground mb-1">Qualifications</div>
              <div className="space-y-1">
                {/* Education Level */}
                {candidate.education ? (
                  <div className="bg-blue-50 p-1 rounded text-xs">
                    <div className="flex items-center justify-center gap-1 text-blue-700">
                      <GraduationCap className="w-3 h-3" />
                      <span className="font-medium">EDU</span>
                    </div>
                    <div className="text-xs text-blue-600 truncate">
                      {candidate.education.length > 15
                        ? `${candidate.education.substring(0, 15)}...`
                        : candidate.education
                      }
                    </div>
                  </div>
                ) : (
                  <div className="text-xs text-muted-foreground">No edu</div>
                )}

                {/* Certifications */}
                {candidate.tags && candidate.tags.some(tag => tag.toLowerCase().includes('cert')) ? (
                  <div className="bg-green-50 p-1 rounded">
                    <div className="flex items-center justify-center gap-1 text-green-700">
                      <ShieldCheck className="w-3 h-3" />
                      <span className="text-xs font-medium">CERT</span>
                    </div>
                    <div className="text-xs text-green-600">
                      {candidate.tags.filter(tag => tag.toLowerCase().includes('cert')).length} items
                    </div>
                  </div>
                ) : (
                  <div className="text-xs text-muted-foreground">No cert</div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Information Display */}
        <div className="mt-3 pt-3 border-t space-y-3">
          {/* Skills Preview */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs font-medium text-muted-foreground">Skills Overview</span>
              <span className="text-xs text-muted-foreground">
                {candidate.skills?.length || 0} total
              </span>
            </div>
            <div className="flex flex-wrap gap-1">
              {candidate.skills?.slice(0, 6).map((skill) => {
                const isCommon = candidates
                  .filter(c => c.id !== candidate.id)
                  .some(c => c.skills?.includes(skill));

                return (
                  <Badge
                    key={skill}
                    variant={isCommon ? "default" : "secondary"}
                    className="text-xs px-1 py-0"
                    title={isCommon ? "Shared skill" : "Unique skill"}
                  >
                    {skill}
                  </Badge>
                );
              })}
              {candidate.skills && candidate.skills.length > 6 && (
                <Badge variant="outline" className="text-xs px-1 py-0">
                  +{candidate.skills.length - 6}
                </Badge>
              )}
            </div>
          </div>

          {/* Professional Background */}
          <div className="grid grid-cols-2 gap-3">
            {/* Work History */}
            <div>
              <span className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                <Briefcase className="w-3 h-3" />
                Career Path
              </span>
              {candidate.workHistory ? (
                <div className="bg-slate-50 dark:bg-slate-900/20 p-2 rounded mt-1">
                  <p className="text-xs text-muted-foreground line-clamp-3">
                    {candidate.workHistory}
                  </p>
                </div>
              ) : (
                <p className="text-xs text-muted-foreground mt-1 italic">No work history details</p>
              )}
            </div>

            {/* Experience Summary */}
            <div>
              <span className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                <Clock className="w-3 h-3" />
                Experience Level
              </span>
              <div className="bg-purple-50 dark:bg-purple-900/20 p-2 rounded mt-1">
                <div className="text-xs font-medium text-purple-700 dark:text-purple-300">
                  {candidate.experience}
                </div>
                <div className="text-xs text-purple-600 dark:text-purple-400">
                  Professional level
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Comparison Indicators */}
        <div className="mt-3 pt-3 border-t">
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center gap-4">
              {candidate.rating && candidate.rating >= 4 && (
                <div className="flex items-center gap-1 text-green-600">
                  <CheckCircle className="w-3 h-3" />
                  <span>High Rating</span>
                </div>
              )}
              {candidate.aiScore && candidate.aiScore >= 80 && (
                <div className="flex items-center gap-1 text-green-600">
                  <Brain className="w-3 h-3" />
                  <span>AI Recommended</span>
                </div>
              )}
              {skillsOverlap.unique > skillsOverlap.common && (
                <div className="flex items-center gap-1 text-blue-600">
                  <Zap className="w-3 h-3" />
                  <span>Unique Skills</span>
                </div>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              {candidate.resumeUrl && (
                <Button
                  size="sm"
                  variant="outline"
                  className="h-5 px-2 text-xs"
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(candidate.resumeUrl, '_blank');
                  }}
                >
                  <Eye className="w-2 h-2 mr-1" />
                  CV
                </Button>
              )}

              <Button
                size="sm"
                variant="outline"
                className="h-5 px-2 text-xs"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/candidates/detail/${candidate.id}`);
                }}
              >
                <FileText className="w-2 h-2 mr-1" />
                Full
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
