import React, { useState, use<PERSON>emo } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Filter,
  Star,
  MapPin,
  Briefcase,
  Calendar,
  Check,
  Loader2,
  Users,
} from "lucide-react";
import { Candidate } from "@/data/mockData";
import { useJobs } from "@/hooks/useApi";

interface CandidateSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  candidates: Candidate[];
  selectedIds: string[];
  onSelect: (candidate: Candidate) => void;
  isLoading?: boolean;
  jobFilterId?: string;
}

export const CandidateSelector: React.FC<CandidateSelectorProps> = ({
  isOpen,
  onClose,
  candidates,
  selectedIds,
  onSelect,
  isLoading = false,
  jobFilterId = "all",
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [experienceFilter, setExperienceFilter] = useState<string>("all");
  const [localJobFilter, setLocalJobFilter] = useState<string>(jobFilterId);

  // Fetch jobs for filtering
  const { data: jobsResponse } = useJobs({
    per_page: 100,
    status: "active",
    sort: "title",
  });

  const jobs = useMemo(() => {
    return jobsResponse?.data || [];
  }, [jobsResponse]);

  // Filter candidates based on search and filters
  const filteredCandidates = useMemo(() => {
    let filtered = candidates;

    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (candidate) =>
          candidate.name.toLowerCase().includes(searchLower) ||
          candidate.email.toLowerCase().includes(searchLower) ||
          candidate.position.toLowerCase().includes(searchLower) ||
          candidate.skills.some((skill) =>
            skill.toLowerCase().includes(searchLower),
          ),
      );
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(
        (candidate) => candidate.status === statusFilter,
      );
    }

    // Experience filter
    if (experienceFilter !== "all") {
      filtered = filtered.filter(
        (candidate) => candidate.experience === experienceFilter,
      );
    }

    // Job filter
    if (localJobFilter !== "all") {
      filtered = filtered.filter(
        (candidate) =>
          candidate.jobId === localJobFilter ||
          candidate.jobPosting?.id?.toString() === localJobFilter,
      );
    }

    return filtered;
  }, [candidates, searchTerm, statusFilter, experienceFilter, localJobFilter]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "hired":
        return "bg-green-100 text-green-800";
      case "offer":
        return "bg-orange-100 text-orange-800";
      case "interview":
        return "bg-purple-100 text-purple-800";
      case "screening":
        return "bg-yellow-100 text-yellow-800";
      case "applied":
        return "bg-blue-100 text-blue-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusLabel = (status: string) => {
    const statusLabels: Record<string, string> = {
      sourced: "Sourced",
      applied: "Applied",
      screening: "Screening",
      interview: "Interview",
      offer: "Offer",
      hired: "Hired",
      rejected: "Rejected",
    };
    return statusLabels[status] || status;
  };

  const handleSelect = (candidate: Candidate) => {
    onSelect(candidate);
  };

  if (isLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>Select Candidates to Compare</DialogTitle>
          </DialogHeader>
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-2">
              <Loader2 className="w-6 h-6 animate-spin" />
              <span>Loading candidates...</span>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Select Candidates to Compare
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search candidates by name, email, position, or skills..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 rounded-xl"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[130px] rounded-xl">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="sourced">Sourced</SelectItem>
                  <SelectItem value="applied">Applied</SelectItem>
                  <SelectItem value="screening">Screening</SelectItem>
                  <SelectItem value="interview">Interview</SelectItem>
                  <SelectItem value="offer">Offer</SelectItem>
                  <SelectItem value="hired">Hired</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={experienceFilter}
                onValueChange={setExperienceFilter}
              >
                <SelectTrigger className="w-[130px] rounded-xl">
                  <SelectValue placeholder="Experience" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Experience</SelectItem>
                  <SelectItem value="0-1 years">0-1 years</SelectItem>
                  <SelectItem value="1-3 years">1-3 years</SelectItem>
                  <SelectItem value="3-5 years">3-5 years</SelectItem>
                  <SelectItem value="5+ years">5+ years</SelectItem>
                </SelectContent>
              </Select>

              <Select value={localJobFilter} onValueChange={setLocalJobFilter}>
                <SelectTrigger className="w-[150px] rounded-xl">
                  <SelectValue placeholder="Job Position" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Positions</SelectItem>
                  {jobs.map((job) => (
                    <SelectItem key={job.id} value={job.id.toString()}>
                      {job.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Results Count */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>
              {filteredCandidates.length} candidate
              {filteredCandidates.length !== 1 ? "s" : ""} found
            </span>
            {(searchTerm ||
              statusFilter !== "all" ||
              experienceFilter !== "all" ||
              localJobFilter !== "all") && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSearchTerm("");
                  setStatusFilter("all");
                  setExperienceFilter("all");
                  setLocalJobFilter("all");
                }}
                className="h-auto p-1 text-xs"
              >
                Xóa bộ lọc
              </Button>
            )}
          </div>

          {/* Candidates List */}
          <div className="max-h-[50vh] overflow-y-auto">
            {filteredCandidates.length === 0 ? (
              <div className="text-center py-12">
                <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  No candidates found
                </h3>
                <p className="text-muted-foreground">
                  Try adjusting your search criteria or filters
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {filteredCandidates.map((candidate) => {
                  const isSelected = selectedIds.includes(candidate.id);

                  return (
                    <Card
                      key={candidate.id}
                      className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                        isSelected
                          ? "border-primary bg-primary/5 cursor-not-allowed opacity-60"
                          : "hover:border-primary/50"
                      }`}
                      onClick={() => !isSelected && handleSelect(candidate)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={candidate.avatar} />
                            <AvatarFallback className="bg-primary/10 text-primary">
                              {candidate.initials}
                            </AvatarFallback>
                          </Avatar>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <h4 className="font-semibold truncate">
                                {candidate.name}
                              </h4>
                              {isSelected ? (
                                <Badge className="bg-primary/20 text-primary">
                                  <Check className="w-3 h-3 mr-1" />
                                  Selected
                                </Badge>
                              ) : (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="h-6 px-2 text-xs"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleSelect(candidate);
                                  }}
                                >
                                  Add
                                </Button>
                              )}
                            </div>

                            <p className="text-sm text-muted-foreground flex items-center gap-1 mt-1">
                              <Briefcase className="w-3 h-3" />
                              {candidate.position}
                            </p>

                            <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                {candidate.location}
                              </div>
                              <div className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                {candidate.experience}
                              </div>
                              {candidate.rating && (
                                <div className="flex items-center gap-1">
                                  <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                                  {candidate.rating}
                                </div>
                              )}
                            </div>

                            <div className="flex items-center justify-between mt-3">
                              <Badge
                                className={getStatusColor(candidate.status)}
                              >
                                {getStatusLabel(candidate.status)}
                              </Badge>

                              <div className="flex flex-wrap gap-1">
                                {candidate.skills.slice(0, 2).map((skill) => (
                                  <Badge
                                    key={skill}
                                    variant="secondary"
                                    className="text-xs px-1 py-0"
                                  >
                                    {skill}
                                  </Badge>
                                ))}
                                {candidate.skills.length > 2 && (
                                  <Badge
                                    variant="secondary"
                                    className="text-xs px-1 py-0"
                                  >
                                    +{candidate.skills.length - 2}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-muted-foreground">
            {selectedIds.length} trong 4 ứng viên được chọn
          </div>
          <Button onClick={onClose} variant="outline">
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
