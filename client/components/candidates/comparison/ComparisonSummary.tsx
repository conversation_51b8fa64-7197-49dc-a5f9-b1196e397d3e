import React, { useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  BarChart3,
  Trophy,
  TrendingUp,
  Users,
  Award,
  Target,
  Zap,
  Star,
  DollarSign,
  GraduationCap,
  ShieldCheck,
  Clock,
} from "lucide-react";
import { Candidate } from "@/data/mockData";

interface ComparisonSummaryProps {
  candidates: Candidate[];
  jobFilterId?: string;
}

export const ComparisonSummary: React.FC<ComparisonSummaryProps> = ({
  candidates,
  jobFilterId,
}) => {
  const summaryStats = useMemo(() => {
    if (candidates.length === 0) return null;

    // Calculate average scores
    const validRatings = candidates.filter((c) => c.rating && c.rating > 0);
    const validAiScores = candidates.filter((c) => c.aiScore && c.aiScore > 0);

    const avgRating =
      validRatings.length > 0
        ? validRatings.reduce((sum, c) => sum + (c.rating || 0), 0) /
          validRatings.length
        : 0;

    const avgAiScore =
      validAiScores.length > 0
        ? validAiScores.reduce((sum, c) => sum + (c.aiScore || 0), 0) /
          validAiScores.length
        : 0;

    // Find top candidate by rating
    const topByRating = candidates.reduce((top, current) => {
      const currentRating = current.rating || 0;
      const topRating = top.rating || 0;
      return currentRating > topRating ? current : top;
    });

    // Find top candidate by AI score
    const topByAiScore = candidates.reduce((top, current) => {
      const currentScore = current.aiScore || 0;
      const topScore = top.aiScore || 0;
      return currentScore > topScore ? current : top;
    });

    // Calculate skill overlap
    const allSkills = candidates.flatMap((c) => c.skills || []);
    const uniqueSkills = [...new Set(allSkills)];
    const commonSkills = uniqueSkills.filter((skill) =>
      candidates.every((c) => c.skills?.includes(skill)),
    );

    // Experience levels
    const experienceLevels = candidates
      .map((c) => c.experience)
      .filter(Boolean);
    const uniqueExperience = [...new Set(experienceLevels)];

    // Status distribution
    const statusCounts = candidates.reduce(
      (acc, c) => {
        acc[c.status] = (acc[c.status] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    // Salary analysis
    const salaryData = candidates.filter(
      (c) => c.salaryExpectationMin || c.salaryExpectationMax,
    );
    const avgSalaryExpectation =
      salaryData.length > 0
        ? salaryData.reduce((sum, c) => {
            const salary =
              c.salaryExpectationMin || c.salaryExpectationMax || 0;
            return sum + salary;
          }, 0) / salaryData.length
        : 0;

    // Education analysis
    const educationData = candidates.filter((c) => c.education).length;
    const certificationData = candidates.filter((c) =>
      c.tags?.some((tag) => tag.toLowerCase().includes("cert")),
    ).length;

    return {
      avgRating: Math.round(avgRating * 10) / 10,
      avgAiScore: Math.round(avgAiScore * 10) / 10,
      topByRating,
      topByAiScore,
      totalSkills: uniqueSkills.length,
      commonSkills: commonSkills.length,
      experienceRange: uniqueExperience,
      statusCounts,
      hasRatings: validRatings.length > 0,
      hasAiScores: validAiScores.length > 0,
      avgSalaryExpectation:
        Math.round((avgSalaryExpectation / 1000000) * 10) / 10, // Convert to millions
      candidatesWithEducation: educationData,
      candidatesWithCertifications: certificationData,
    };
  }, [candidates]);

  if (!summaryStats || candidates.length < 2) {
    return null;
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "hired":
        return "bg-green-100 text-green-800";
      case "offer":
        return "bg-orange-100 text-orange-800";
      case "interview":
        return "bg-purple-100 text-purple-800";
      case "screening":
        return "bg-yellow-100 text-yellow-800";
      case "applied":
        return "bg-blue-100 text-blue-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-emerald-500/5 hidden">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="w-5 h-5 text-primary" />
          Comparison Summary
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Top Performers */}
          <div className="space-y-4">
            <h4 className="font-semibold flex items-center gap-2">
              <Trophy className="w-4 h-4 text-yellow-600" />
              Top Performers
            </h4>

            {summaryStats.hasRatings && (
              <div className="bg-white/50 rounded-lg p-3">
                <div className="text-sm text-muted-foreground">
                  Highest Rating
                </div>
                <div className="font-medium truncate">
                  {summaryStats.topByRating.name}
                </div>
                <div className="flex items-center gap-1 mt-1">
                  <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm font-medium">
                    {summaryStats.topByRating.rating}
                  </span>
                </div>
              </div>
            )}

            {summaryStats.hasAiScores && (
              <div className="bg-white/50 rounded-lg p-3">
                <div className="text-sm text-muted-foreground">
                  Highest AI Score
                </div>
                <div className="font-medium truncate">
                  {summaryStats.topByAiScore.name}
                </div>
                <div className="flex items-center gap-1 mt-1">
                  <Zap className="w-3 h-3 text-primary" />
                  <span
                    className={`text-sm font-medium ${getScoreColor(summaryStats.topByAiScore.aiScore || 0)}`}
                  >
                    {summaryStats.topByAiScore.aiScore}%
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Average Scores */}
          <div className="space-y-4">
            <h4 className="font-semibold flex items-center gap-2">
              <Target className="w-4 h-4 text-blue-600" />
              Average Scores
            </h4>

            {summaryStats.hasRatings && (
              <div className="bg-white/50 rounded-lg p-3">
                <div className="text-sm text-muted-foreground">Rating</div>
                <div className="flex items-center gap-2 mt-1">
                  <Progress
                    value={(summaryStats.avgRating / 5) * 100}
                    className="flex-1 h-2"
                  />
                  <span className="text-sm font-medium">
                    {summaryStats.avgRating}/5
                  </span>
                </div>
              </div>
            )}

            {summaryStats.hasAiScores && (
              <div className="bg-white/50 rounded-lg p-3">
                <div className="text-sm text-muted-foreground">AI Score</div>
                <div className="flex items-center gap-2 mt-1">
                  <Progress
                    value={summaryStats.avgAiScore}
                    className="flex-1 h-2"
                  />
                  <span
                    className={`text-sm font-medium ${getScoreColor(summaryStats.avgAiScore)}`}
                  >
                    {summaryStats.avgAiScore}%
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Skills Analysis */}
          <div className="space-y-4">
            <h4 className="font-semibold flex items-center gap-2">
              <Award className="w-4 h-4 text-green-600" />
              Skills Analysis
            </h4>

            <div className="bg-white/50 rounded-lg p-3">
              <div className="text-sm text-muted-foreground">
                Total Unique Skills
              </div>
              <div className="text-xl font-bold text-primary">
                {summaryStats.totalSkills}
              </div>
            </div>

            <div className="bg-white/50 rounded-lg p-3">
              <div className="text-sm text-muted-foreground">Common Skills</div>
              <div className="text-xl font-bold text-green-600">
                {summaryStats.commonSkills}
              </div>
              <div className="text-xs text-muted-foreground">
                Shared by all candidates
              </div>
            </div>

            <div className="grid grid-cols-2 gap-2">
              <div className="bg-blue-50 rounded-lg p-3">
                <div className="flex items-center gap-1 text-blue-700 mb-1">
                  <GraduationCap className="w-3 h-3" />
                  <span className="text-xs font-medium">Education</span>
                </div>
                <div className="text-lg font-bold text-blue-600">
                  {summaryStats.candidatesWithEducation}/{candidates.length}
                </div>
                <div className="text-xs text-blue-600">have education data</div>
              </div>

              <div className="bg-green-50 rounded-lg p-3">
                <div className="flex items-center gap-1 text-green-700 mb-1">
                  <ShieldCheck className="w-3 h-3" />
                  <span className="text-xs font-medium">Certified</span>
                </div>
                <div className="text-lg font-bold text-green-600">
                  {summaryStats.candidatesWithCertifications}/
                  {candidates.length}
                </div>
                <div className="text-xs text-green-600">
                  have certifications
                </div>
              </div>
            </div>
          </div>

          {/* Status & Experience */}
          <div className="space-y-4">
            <h4 className="font-semibold flex items-center gap-2">
              <Users className="w-4 h-4 text-purple-600" />
              Overview
            </h4>

            <div className="bg-white/50 rounded-lg p-3">
              <div className="text-sm text-muted-foreground mb-2">
                Status Distribution
              </div>
              <div className="space-y-1">
                {Object.entries(summaryStats.statusCounts).map(
                  ([status, count]) => (
                    <div
                      key={status}
                      className="flex items-center justify-between"
                    >
                      <Badge className={`${getStatusColor(status)} text-xs`}>
                        {status}
                      </Badge>
                      <span className="text-xs font-medium">{count}</span>
                    </div>
                  ),
                )}
              </div>
            </div>

            <div className="bg-white/50 rounded-lg p-3">
              <div className="text-sm text-muted-foreground">
                Experience Range
              </div>
              <div className="text-sm font-medium">
                {summaryStats.experienceRange.length > 0
                  ? summaryStats.experienceRange.join(", ")
                  : "Not specified"}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Insights */}
        <div className="mt-6 pt-4 border-t">
          <h4 className="font-semibold mb-3 flex items-center gap-2">
            <TrendingUp className="w-4 h-4 text-orange-600" />
            Quick Insights
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="bg-white/50 rounded-lg p-3">
              <div className="font-medium text-green-700 mb-1">Strengths</div>
              <ul className="space-y-1 text-muted-foreground">
                {summaryStats.commonSkills > 0 && (
                  <li>
                    • {summaryStats.commonSkills} shared skills across all
                    candidates
                  </li>
                )}
                {summaryStats.hasRatings && summaryStats.avgRating >= 4 && (
                  <li>• High average rating ({summaryStats.avgRating}/5)</li>
                )}
                {summaryStats.hasAiScores && summaryStats.avgAiScore >= 70 && (
                  <li>• Strong AI compatibility scores</li>
                )}
              </ul>
            </div>
            <div className="bg-white/50 rounded-lg p-3">
              <div className="font-medium text-orange-700 mb-1">
                Considerations
              </div>
              <ul className="space-y-1 text-muted-foreground">
                {summaryStats.experienceRange.length > 2 && (
                  <li>
                    Diverse experience levels may require different
                    approaches
                  </li>
                )}
                {Object.keys(summaryStats.statusCounts).length > 3 && (
                  <li>• Candidates at different pipeline stages</li>
                )}
                {summaryStats.commonSkills === 0 && (
                  <li>• No shared skills - consider role requirements</li>
                )}
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
