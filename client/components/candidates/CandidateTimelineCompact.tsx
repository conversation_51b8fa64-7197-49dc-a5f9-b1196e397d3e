import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Calendar,
  Mail,
  MessageSquare,
  UserCheck,
  FileText,
  Edit,
  Brain,
  UserPlus,
  RefreshCw,
  ChevronDown,
  ChevronUp,
  AlertCircle,
  Activity,
} from "lucide-react";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { useCandidateTimeline, useRefreshTimeline } from "@/hooks/useCandidateTimeline";
import {
  TimelineActivity,
  getStatusColor,
  getScoreColor,
} from "@/lib/types/candidateTimeline";
import { timelineConfig } from "@/lib/config/timeline";
import { cn } from "@/lib/utils";

interface CandidateTimelineCompactProps {
  candidateId: string | number;
  className?: string;
  maxItems?: number;
}

// Icon mapping
const ACTIVITY_ICON_MAP = {
  status_change: UserCheck,
  interview_scheduled: Calendar,
  interview_feedback: MessageSquare,
  communication: Mail,
  profile_analysis: Brain,
  profile_updated: Edit,
  notes_updated: FileText,
  candidate_created: UserPlus,
};

// Color mapping for Tailwind classes (compact version)
const COLOR_CLASSES = {
  blue: "text-blue-600 bg-blue-100",
  indigo: "text-indigo-600 bg-indigo-100", 
  yellow: "text-yellow-600 bg-yellow-100",
  purple: "text-purple-600 bg-purple-100",
  orange: "text-orange-600 bg-orange-100",
  green: "text-green-600 bg-green-100",
  red: "text-red-600 bg-red-100",
  gray: "text-gray-600 bg-gray-100",
};

const CompactTimelineItem: React.FC<{ activity: TimelineActivity; isLast: boolean }> = ({ 
  activity, 
  isLast 
}) => {
  const IconComponent = ACTIVITY_ICON_MAP[activity.type] || Activity;
  const colorClass = COLOR_CLASSES[activity.color as keyof typeof COLOR_CLASSES] || COLOR_CLASSES.gray;
  
  const formatCompactTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return format(date, "dd/MM HH:mm", { locale: vi });
  };

  return (
    <div className="flex gap-3 relative">
      {/* Timeline line */}
      {!isLast && (
        <div className="absolute left-4 top-8 w-px h-full bg-border -z-10" />
      )}
      
      {/* Icon */}
      <div className={cn(
        "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
        colorClass
      )}>
        <IconComponent className="w-4 h-4" />
      </div>
      
      {/* Content */}
      <div className="flex-1 min-w-0 pb-4">
        <div className="flex items-center justify-between gap-2">
          <h4 className="font-medium text-sm truncate">{activity.title}</h4>
          <time className="text-xs text-muted-foreground flex-shrink-0">
            {formatCompactTime(activity.timestamp)}
          </time>
        </div>
        
        <p className="text-xs text-muted-foreground mt-1" style={{
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden'
        }}>
          {activity.description}
        </p>
        
        {/* Metadata (compact) */}
        {activity.type === "status_change" && activity.metadata.new_status && (
          <Badge variant="outline" className="text-xs mt-1">
            {activity.metadata.new_status}
          </Badge>
        )}
        
        {activity.type === "interview_feedback" && activity.metadata.score && (
          <Badge 
            variant="outline" 
            className={cn("text-xs mt-1", COLOR_CLASSES[getScoreColor(activity.metadata.score)])}
          >
            {activity.metadata.score}%
          </Badge>
        )}
      </div>
    </div>
  );
};

const CompactSkeleton: React.FC = () => (
  <div className="space-y-3">
    {[...Array(3)].map((_, i) => (
      <div key={i} className="flex gap-3">
        <Skeleton className="w-8 h-8 rounded-full" />
        <div className="flex-1 space-y-2">
          <div className="flex justify-between">
            <Skeleton className="h-3 w-2/3" />
            <Skeleton className="h-3 w-12" />
          </div>
          <Skeleton className="h-3 w-full" />
        </div>
      </div>
    ))}
  </div>
);

export const CandidateTimelineCompact: React.FC<CandidateTimelineCompactProps> = ({
  candidateId,
  className,
  maxItems = timelineConfig.compactMaxItems,
}) => {
  const [showAll, setShowAll] = useState(false);

  const filters = {
    page: 1,
    per_page: showAll ? 20 : maxItems,
  };

  const { data, isLoading, error, refetch } = useCandidateTimeline(candidateId, filters);
  const refreshTimeline = useRefreshTimeline();

  const handleRefresh = () => {
    refreshTimeline(candidateId);
  };

  if (error) {
    return (
      <div className={cn("space-y-3", className)}>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-sm">
            Không thể tải timeline. Vui lòng thử lại.
          </AlertDescription>
        </Alert>
        <Button onClick={() => refetch()} variant="outline" size="sm" className="w-full">
          <RefreshCw className="w-4 h-4 mr-2" />
          Thử lại
        </Button>
      </div>
    );
  }

  const activities = data?.data?.activities || [];
  const displayActivities = showAll ? activities : activities.slice(0, maxItems);
  const hasMore = activities.length > maxItems;

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h4 className="font-medium flex items-center gap-2">
          <Activity className="w-4 h-4" />
          Hoạt động gần đây
        </h4>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRefresh}
          disabled={isLoading}
        >
          <RefreshCw className={cn("w-4 h-4", isLoading && "animate-spin")} />
        </Button>
      </div>

      {/* Timeline Content */}
      {isLoading ? (
        <CompactSkeleton />
      ) : activities.length === 0 ? (
        <div className="text-center py-6 text-muted-foreground">
          <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">Chưa có hoạt động nào</p>
        </div>
      ) : (
        <>
          <div className="space-y-1">
            {displayActivities.map((activity, index) => (
              <CompactTimelineItem
                key={activity.id}
                activity={activity}
                isLast={index === displayActivities.length - 1}
              />
            ))}
          </div>
          
          {/* Show More/Less Button */}
          {hasMore && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAll(!showAll)}
              className="w-full text-xs"
            >
              {showAll ? (
                <>
                  <ChevronUp className="w-4 h-4 mr-1" />
                  Thu gọn
                </>
              ) : (
                <>
                  <ChevronDown className="w-4 h-4 mr-1" />
                  Xem thêm ({activities.length - maxItems})
                </>
              )}
            </Button>
          )}
        </>
      )}
    </div>
  );
};

export default CandidateTimelineCompact;
