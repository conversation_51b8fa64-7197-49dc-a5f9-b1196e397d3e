import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Calendar,
  Mail,
  MessageSquare,
  UserCheck,
  FileText,
  Edit,
  Brain,
  UserPlus,
  Clock,
  RefreshCw,
  Filter,
  ChevronLeft,
  ChevronRight,
  AlertCircle,
  Activity,
} from "lucide-react";
import { formatDistanceToNow, format } from "date-fns";
import { vi } from "date-fns/locale";
import { useCandidateTimeline, useRefreshTimeline } from "@/hooks/useCandidateTimeline";
import {
  TimelineActivity,
  TimelineFilters,
  ActivityType,
  ActivityCategory,
  getStatusColor,
  getMessageStatusColor,
  getScoreColor,
} from "@/lib/types/candidateTimeline";
import { timelineConfig } from "@/lib/config/timeline";
import { cn } from "@/lib/utils";

interface CandidateTimelineTabProps {
  candidateId: string | number;
  className?: string;
}

// Icon mapping
const ACTIVITY_ICON_MAP = {
  status_change: UserCheck,
  interview_scheduled: Calendar,
  interview_feedback: MessageSquare,
  communication: Mail,
  profile_analysis: Brain,
  profile_updated: Edit,
  notes_updated: FileText,
  candidate_created: UserPlus,
};

// Color mapping for Tailwind classes
const COLOR_CLASSES = {
  blue: "text-blue-600 bg-blue-50 border-blue-200",
  indigo: "text-indigo-600 bg-indigo-50 border-indigo-200", 
  yellow: "text-yellow-600 bg-yellow-50 border-yellow-200",
  purple: "text-purple-600 bg-purple-50 border-purple-200",
  orange: "text-orange-600 bg-orange-50 border-orange-200",
  green: "text-green-600 bg-green-50 border-green-200",
  red: "text-red-600 bg-red-50 border-red-200",
  gray: "text-gray-600 bg-gray-50 border-gray-200",
};

const TimelineActivityItem: React.FC<{ activity: TimelineActivity }> = ({ activity }) => {
  const IconComponent = ACTIVITY_ICON_MAP[activity.type] || Activity;
  const colorClass = COLOR_CLASSES[activity.color as keyof typeof COLOR_CLASSES] || COLOR_CLASSES.gray;
  
  const getUserInitials = (name: string) => {
    return name
      .split(" ")
      .map(n => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = diff / (1000 * 60 * 60 * 24);
    
    if (days < 1) {
      return formatDistanceToNow(date, { addSuffix: true, locale: vi });
    } else if (days < 7) {
      return format(date, "EEEE 'lúc' HH:mm", { locale: vi });
    } else {
      return format(date, "dd/MM/yyyy 'lúc' HH:mm", { locale: vi });
    }
  };

  const renderMetadataDetails = () => {
    const { metadata, type } = activity;
    
    switch (type) {
      case "status_change":
        return (
          <div className="flex items-center gap-2 text-sm">
            <Badge variant="outline" className="text-xs">
              {metadata.old_status}
            </Badge>
            <span>→</span>
            <Badge variant="outline" className="text-xs">
              {metadata.new_status}
            </Badge>
            {metadata.notes && (
              <span className="text-muted-foreground ml-2">
                • {metadata.notes}
              </span>
            )}
          </div>
        );
      
      case "interview_feedback":
        return (
          <div className="text-sm space-y-1">
            {metadata.score && (
              <div className="flex items-center gap-2">
                <span>Điểm số:</span>
                <Badge 
                  variant="outline" 
                  className={cn("text-xs", COLOR_CLASSES[getScoreColor(metadata.score)])}
                >
                  {metadata.score}%
                </Badge>
              </div>
            )}
            {metadata.recommendation && (
              <div className="text-muted-foreground">
                Khuyến nghị: {metadata.recommendation}
              </div>
            )}
          </div>
        );
      
      case "communication":
        return (
          <div className="text-sm space-y-1">
            {metadata.subject && (
              <div className="font-medium">{metadata.subject}</div>
            )}
            {metadata.message_type && (
              <Badge variant="outline" className="text-xs capitalize">
                {metadata.message_type}
              </Badge>
            )}
          </div>
        );
      
      case "profile_analysis":
        return (
          <div className="text-sm">
            {metadata.score && (
              <div className="flex items-center gap-2">
                <span>Điểm phân tích:</span>
                <Badge 
                  variant="outline" 
                  className={cn("text-xs", COLOR_CLASSES[getScoreColor(metadata.score)])}
                >
                  {metadata.score}%
                </Badge>
              </div>
            )}
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div
      className="flex gap-4 p-4 hover:bg-muted/50 transition-colors focus-within:bg-muted/50"
      role="article"
      aria-label={`${activity.title} - ${formatTimestamp(activity.timestamp)}`}
    >
      <div className={cn(
        "flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center border-2",
        colorClass
      )}
      aria-hidden="true"
      >
        <IconComponent className="w-5 h-5" />
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1">
            <h4 className="font-medium text-sm">{activity.title}</h4>
            <p className="text-sm text-muted-foreground mt-1">
              {activity.description}
            </p>
            {renderMetadataDetails()}
          </div>
          
          <div className="flex-shrink-0 text-right">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <time className="text-xs text-muted-foreground">
                    {formatTimestamp(activity.timestamp)}
                  </time>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{format(new Date(activity.timestamp), "dd/MM/yyyy 'lúc' HH:mm:ss", { locale: vi })}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <div className="flex items-center gap-1 mt-1">
              <Avatar className="w-5 h-5">
                <AvatarFallback className="text-xs bg-muted">
                  {getUserInitials(activity.user.name)}
                </AvatarFallback>
              </Avatar>
              <span className="text-xs text-muted-foreground">
                {activity.user.name}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const TimelineSkeleton: React.FC = () => (
  <div className="space-y-4">
    {[...Array(5)].map((_, i) => (
      <div key={i} className="flex gap-4 p-4">
        <Skeleton className="w-10 h-10 rounded-full" />
        <div className="flex-1 space-y-2">
          <Skeleton className="h-4 w-2/3" />
          <Skeleton className="h-3 w-full" />
          <Skeleton className="h-3 w-1/2" />
        </div>
        <div className="space-y-1">
          <Skeleton className="h-3 w-16" />
          <Skeleton className="h-3 w-20" />
        </div>
      </div>
    ))}
  </div>
);

export const CandidateTimelineTab: React.FC<CandidateTimelineTabProps> = ({
  candidateId,
  className,
}) => {
  const [filters, setFilters] = useState<TimelineFilters>({
    page: 1,
    per_page: timelineConfig.defaultPageSize,
  });

  const { data, isLoading, error, refetch } = useCandidateTimeline(candidateId, filters);
  const refreshTimeline = useRefreshTimeline();

  const handleFilterChange = (key: keyof TimelineFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  const handleRefresh = () => {
    refreshTimeline(candidateId);
  };

  if (error) {
    return (
      <div className={cn("space-y-4", className)}>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Không thể tải timeline hoạt động. Vui lòng thử lại.
          </AlertDescription>
        </Alert>
        <Button onClick={() => refetch()} variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Thử lại
        </Button>
      </div>
    );
  }

  const activities = data?.data?.activities || [];
  const pagination = data?.data?.pagination;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header and Filters */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Activity className="w-5 h-5" />
          Lịch sử hoạt động
        </h3>
        
        <div className="flex items-center gap-2">
          <Select
            value={filters.per_page?.toString() || "20"}
            onValueChange={(value) => handleFilterChange("per_page", parseInt(value))}
          >
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value={timelineConfig.defaultPageSize.toString()}>
                {timelineConfig.defaultPageSize}
              </SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value={timelineConfig.maxPageSize.toString()}>
                {timelineConfig.maxPageSize}
              </SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={cn("w-4 h-4", isLoading && "animate-spin")} />
          </Button>
        </div>
      </div>

      {/* Timeline Content */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="text-base">
            {pagination && `${pagination.total} hoạt động`}
          </CardTitle>
        </CardHeader>
        
        <CardContent className="p-0">
          {isLoading ? (
            <TimelineSkeleton />
          ) : activities.length === 0 ? (
            <div className="p-8 text-center text-muted-foreground">
              <Activity className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Chưa có hoạt động nào ��ược ghi nhận</p>
            </div>
          ) : (
            <div className="divide-y">
              {activities.map((activity) => (
                <TimelineActivityItem
                  key={activity.id}
                  activity={activity}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination && pagination.last_page > 1 && (
        <nav role="navigation" aria-label="Phân trang timeline hoạt động">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground" aria-live="polite">
              Hiển thị {pagination.from} - {pagination.to} trong {pagination.total} hoạt động
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.current_page - 1)}
                disabled={pagination.current_page <= 1}
                aria-label={`Chuyển đến trang ${pagination.current_page - 1}`}
              >
                <ChevronLeft className="w-4 h-4" />
                Trước
              </Button>

              <span className="text-sm" aria-current="page">
                Trang {pagination.current_page} / {pagination.last_page}
              </span>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.current_page + 1)}
                disabled={pagination.current_page >= pagination.last_page}
                aria-label={`Chuyển đến trang ${pagination.current_page + 1}`}
              >
                Sau
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </nav>
      )}
    </div>
  );
};

export default CandidateTimelineTab;
