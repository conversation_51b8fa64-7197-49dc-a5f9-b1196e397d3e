import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { FormActions } from "@/components/ui/form-actions";
import { FormContainer } from "@/components/ui/form-container";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  GraduationCap,
  Link,
  Upload,
  X,
  Plus,
  Sparkles,
  Edit,
  Loader2,
  FileText,
  MessageSquare,
} from "lucide-react";
import { Candidate } from "@/data/mockData";
import { UiCandidate } from "@/lib/adapters/types";
import { toast } from "sonner";
import { useTranslation } from "@/lib/i18n";
import { useJobs } from "@/hooks/useApi";
import { jobAdapters } from "@/lib/adapters";
import { ResumeExtractionModal } from "./ResumeExtractionModal";
import { ResumeExtractionService } from "@/lib/services/resumeExtractionService";
import {
  ExtractedInformation,
  ExtractedFieldSelection,
} from "@/lib/types/resumeExtraction";
import {
  CandidateNotes as NotesArray,
  CandidateNote,
  validateNoteContent,
} from "@/lib/types/candidateNotes";
import { useCreateCandidateWithNotes, useCandidateNotesOperations } from "@/hooks/useCandidateNotes";
import { useCurrentUser } from "@/hooks/useCurrentUser";
import { CandidateNotesEditor } from "./detail/CandidateNotesEditor";

interface EditCandidateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdate?: (candidate: UiCandidate) => Promise<void>;
  onCreate?: (candidate: Omit<UiCandidate, "id">) => Promise<void>;
  candidate?: UiCandidate | null;
  mode?: "edit" | "add";
}

interface CandidateFormData {
  name: string;
  email: string;
  phone: string;
  location: string;
  position: string;
  experience: string;
  skills: string[];
  source: string;
  jobId: string;
  salaryExpectationMin: number;
  salaryExpectationMax: number;
  salaryCurrency: string;
  linkedinUrl: string;
  githubUrl: string;
  portfolioUrl: string;
  notes: string;
  education: string;
  workHistory: string;
  tags: string[];
  resumeUrl?: string;
  // Enhanced notes structure
  structuredNotes?: string[]; // Array of note contents for creation
}

const skillSuggestions = [
  "React",
  "TypeScript",
  "JavaScript",
  "Node.js",
  "Python",
  "Java",
  "AWS",
  "Docker",
  "Kubernetes",
  "GraphQL",
  "SQL",
  "MongoDB",
  "UI/UX Design",
  "Product Management",
  "Agile",
  "Scrum",
  "Data Analysis",
  "Machine Learning",
  "DevOps",
  "Leadership",
];

const sourceSuggestions = [
  "LinkedIn",
  "Company Website",
  "Referral",
  "Indeed",
  "AngelList",
  "Glassdoor",
  "GitHub",
  "Stack Overflow",
  "Conference",
  "Cold Outreach",
];

export const CandidateModal = ({
  isOpen,
  onClose,
  onUpdate,
  onCreate,
  candidate,
  mode = "edit",
}: EditCandidateModalProps) => {
  const { t } = useTranslation();
  const [currentTab, setCurrentTab] = useState("general");
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [customSkill, setCustomSkill] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]); // v2.0.1: tags state
  const [customTag, setCustomTag] = useState("");
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [isExtracting, setIsExtracting] = useState(false);
  const [extractedData, setExtractedData] =
    useState<ExtractedInformation | null>(null);
  const [showExtractionModal, setShowExtractionModal] = useState(false);
  const [isAiMode, setIsAiMode] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [candidateNotes, setCandidateNotes] = useState<NotesArray>([]);
  const [originalNotes, setOriginalNotes] = useState<NotesArray>([]);
  const [hasUnsavedNotes, setHasUnsavedNotes] = useState(false);

  // Get current user for note attribution
  const { profile: currentUser } = useCurrentUser();

  // Use enhanced candidate creation hook
  const createCandidateWithNotes = useCreateCandidateWithNotes();

  // Notes operations for edit mode - always call hook but pass conditional candidate ID
  const candidateIdForNotes = mode === "edit" && candidate ? candidate.id : "";
  const notesOperations = useCandidateNotesOperations(candidateIdForNotes);

  // Fetch active jobs from API
  const { data: jobsData } = useJobs({
    filter: { status: "active" },
    per_page: 100, // Get enough jobs for selection
  });

  // Transform API data to get active jobs
  const activeJobs = jobsData
    ? jobAdapters.fromPaginatedApi(jobsData).jobs
    : [];

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<CandidateFormData>({
    defaultValues: {
      source: "LinkedIn",
      experience: "3-5 years",
      salaryCurrency: "VND", // v2.0.1: default currency
    },
  });

  // Load candidate data when modal opens or reset for add mode
  useEffect(() => {
    if (isOpen) {
      if (mode === "edit" && candidate) {
        // Load existing candidate data for editing
        setValue("name", candidate.name || "");
        setValue("email", candidate.email || "");
        setValue("phone", candidate.phone || "");
        setValue("location", candidate.location || "");
        setValue("position", candidate.position || "");
        setValue("experience", candidate.experience || "3-5 years");
        setValue("source", candidate.source || "LinkedIn");
        setValue("jobId", candidate.jobId || "none");

        // v2.0.1: Load salary fields with fallback logic
        const minSalary = candidate.salaryExpectationMin || 0;
        const maxSalary = candidate.salaryExpectationMax || 0;
        const currency = candidate.salaryCurrency || "VND";

        setValue("salaryExpectationMin", minSalary);
        setValue("salaryExpectationMax", maxSalary);
        setValue("salaryCurrency", currency);
        setValue("linkedinUrl", candidate.linkedinUrl || "");
        setValue("resumeUrl", candidate.resumeUrl || "");
        setValue("githubUrl", candidate.githubUrl || "");
        setValue("portfolioUrl", candidate.portfolioUrl || "");
        // Handle both legacy string notes and new structured notes
        if (typeof candidate.notes === 'string') {
          setValue("notes", candidate.notes);
          setCandidateNotes([]);
          setOriginalNotes([]);
        } else if (Array.isArray(candidate.notes)) {
          // Use structured notes directly
          const notes = candidate.notes as NotesArray;
          setCandidateNotes(notes);
          setOriginalNotes(JSON.parse(JSON.stringify(notes))); // Deep copy for comparison
          setValue("notes", ""); // Clear legacy notes field
        } else {
          setValue("notes", "");
          setCandidateNotes([]);
          setOriginalNotes([]);
        }
        setHasUnsavedNotes(false);
        setValue("education", candidate.education || "");
        setValue("workHistory", candidate.workHistory || "");
        setSelectedSkills(candidate.skills || []);
        setSelectedTags(candidate.tags || []);
      } else if (mode === "add") {
        // Reset form for add mode
        reset({
          source: "LinkedIn",
          experience: "3-5 years",
          salaryCurrency: "VND",
          jobId: "none",
          name: "",
          email: "",
          phone: "",
          location: "",
          position: "",
          salaryExpectationMin: 0,
          salaryExpectationMax: 0,
          linkedinUrl: "",
          resumeUrl: "",
          githubUrl: "",
          portfolioUrl: "",
          notes: "",
          education: "",
          workHistory: "",
        });
        setSelectedSkills([]);
        setSelectedTags([]);
        setResumeFile(null);
        setIsAiMode(false);
        setExtractedData(null);
        setShowExtractionModal(false);
        setIsExtracting(false);
        setCandidateNotes([]);
      }
    }
  }, [candidate, isOpen, mode, setValue, reset]);

  const handleClose = () => {
    reset();
    setSelectedSkills([]);
    setSelectedTags([]); // v2.0.1: reset tags
    setResumeFile(null);
    setCurrentTab("general");
    setIsExtracting(false);
    setExtractedData(null);
    setShowExtractionModal(false);
    setIsAiMode(false);
    setIsSubmitting(false);
    setCandidateNotes([]);
    setOriginalNotes([]);
    setHasUnsavedNotes(false);
    onClose();
  };

  const handleSkillAdd = (skill: string) => {
    if (skill && !selectedSkills.includes(skill)) {
      setSelectedSkills([...selectedSkills, skill]);
    }
    setCustomSkill("");
  };

  const handleSkillRemove = (skill: string) => {
    setSelectedSkills(selectedSkills.filter((s) => s !== skill));
  };

  // v2.0.1: Tag handlers
  const handleTagAdd = (tag: string) => {
    if (tag && !selectedTags.includes(tag)) {
      setSelectedTags([...selectedTags, tag]);
    }
    setCustomTag("");
  };

  const handleTagRemove = (tag: string) => {
    setSelectedTags(selectedTags.filter((t) => t !== tag));
  };

  // Enhanced note management functions for add mode
  const handleAddNoteForCreation = async (content: string) => {
    if (!currentUser) {
      toast.error("Không thể xác định người dùng hiện tại");
      return;
    }

    const validation = validateNoteContent(content);
    if (!validation.isValid) {
      validation.errors.forEach(error => toast.error(error));
      return;
    }

    const newNote: CandidateNote = {
      content: content.trim(),
      created_at: new Date().toISOString(),
      created_by: currentUser.fullName,
      created_id: parseInt(currentUser.id) || 1, // Fallback to ID 1 if current user ID is invalid
      updated_at: null,
      updated_by: null,
      updated_id: null,
    };

    const updatedNotes = [...candidateNotes, newNote];
    setCandidateNotes(updatedNotes);
    setHasUnsavedNotes(JSON.stringify(updatedNotes) !== JSON.stringify(originalNotes));
  };

  const handleUpdateNoteForCreation = async (noteIndex: number, content: string) => {
    if (!currentUser) {
      toast.error("Không thể xác định người dùng hiện tại");
      return;
    }

    const validation = validateNoteContent(content);
    if (!validation.isValid) {
      validation.errors.forEach(error => toast.error(error));
      return;
    }

    const updatedNotes = [...candidateNotes];
    updatedNotes[noteIndex] = {
      ...updatedNotes[noteIndex],
      content: content.trim(),
      updated_at: new Date().toISOString(),
      updated_by: currentUser.fullName,
      updated_id: parseInt(currentUser.id) || 1,
    };

    setCandidateNotes(updatedNotes);
    setHasUnsavedNotes(JSON.stringify(updatedNotes) !== JSON.stringify(originalNotes));
  };

  const handleDeleteNoteForCreation = async (noteIndex: number) => {
    const updatedNotes = candidateNotes.filter((_, index) => index !== noteIndex);
    setCandidateNotes(updatedNotes);
    setHasUnsavedNotes(JSON.stringify(updatedNotes) !== JSON.stringify(originalNotes));
  };

  const handleResumeUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setResumeFile(file);
      toast.info(t.toast?.success?.uploaded || "Resume uploaded successfully");
    }
  };

  const handleExtractFromResume = async () => {
    const resumeUrl = watch("resumeUrl");
    if (!resumeUrl && !resumeFile) {
      toast.error("Please provide a resume URL or upload a resume file first");
      return;
    }

    setIsExtracting(true);
    try {
      if (mode === "edit" && candidate) {
        // In edit mode, use real API extraction
        if (!ResumeExtractionService.canExtractResume({ resumeUrl })) {
          toast.error("No resume found to extract from");
          return;
        }

        const candidateId =
          typeof candidate.id === "string"
            ? parseInt(candidate.id)
            : candidate.id;

        if (!candidateId || isNaN(candidateId)) {
          throw new Error("Invalid candidate ID. Cannot extract resume.");
        }

        const response =
          await ResumeExtractionService.extractResume(candidateId);
        const extractedInfo = response.data.extracted_information;

        if (!extractedInfo) {
          throw new Error("No information could be extracted from the resume.");
        }

        setExtractedData(extractedInfo);
        setShowExtractionModal(true);
        toast.success(
          response.message || "Resume information extracted successfully!",
        );
      } else {
        // In add mode, use demo data
        await new Promise((resolve) => setTimeout(resolve, 2000)); // Simulate API delay
        setShowExtractionModal(true);
        toast.success("Resume information extracted successfully!");
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to extract resume information");
    } finally {
      setIsExtracting(false);
    }
  };

  const handleApplyExtractedFields = (
    selectedFields: ExtractedFieldSelection,
  ) => {
    if (!extractedData) return;

    // Apply selected fields to the form
    if (selectedFields.name && extractedData.name) {
      setValue("name", extractedData.name);
    }
    if (selectedFields.email && extractedData.email) {
      setValue("email", extractedData.email);
    }
    if (selectedFields.phone && extractedData.phone) {
      setValue("phone", extractedData.phone);
    }
    if (selectedFields.address && extractedData.address) {
      setValue("location", extractedData.address);
    }
    if (selectedFields.skills && extractedData.skills) {
      const formattedSkills = ResumeExtractionService.formatSkills(
        extractedData.skills,
      );
      setSelectedSkills((prev) => {
        const combined = [...prev, ...formattedSkills];
        return Array.from(new Set(combined)); // Remove duplicates
      });
    }
    if (selectedFields.experience && extractedData.experience) {
      const formattedExperience = ResumeExtractionService.formatExperience(
        extractedData.experience,
      );
      setValue("workHistory", formattedExperience);
    }
    if (selectedFields.education && extractedData.education) {
      const formattedEducation = ResumeExtractionService.formatEducation(
        extractedData.education,
      );
      setValue("education", formattedEducation);
    }

    const appliedFields = Object.entries(selectedFields)
      .filter(([_, selected]) => selected)
      .map(([field, _]) => field);

    toast.success(
      `Applied ${appliedFields.length} fields from resume extraction`,
    );
  };

  const onSubmit = async (data: CandidateFormData) => {
    if (isSubmitting) return; // Prevent double submission

    setIsSubmitting(true);
    try {
      if (mode === "edit") {
        if (!candidate || !onUpdate) return;

        const updatedCandidate: UiCandidate = {
          ...candidate,
          name: data.name,
          email: data.email,
          phone: data.phone,
          location: data.location,
          position: data.position,
          experience: data.experience,
          skills: selectedSkills,
          source: data.source,
          jobId: data.jobId === "none" ? undefined : data.jobId || undefined,
          salaryExpectationMin: data.salaryExpectationMin,
          salaryExpectationMax: data.salaryExpectationMax,
          salaryCurrency: data.salaryCurrency,
          linkedinUrl: data.linkedinUrl,
          resumeUrl: data.resumeUrl,
          githubUrl: data.githubUrl,
          portfolioUrl: data.portfolioUrl,
          notes: candidateNotes, // Use structured notes
          education: data.education,
          workHistory: data.workHistory,
          tags: selectedTags,
          initials: data.name
            .split(" ")
            .map((n) => n[0])
            .join("")
            .toUpperCase(),
        };

        await onUpdate(updatedCandidate);
        // Reset unsaved changes state after successful update
        setOriginalNotes(JSON.parse(JSON.stringify(candidateNotes)));
        setHasUnsavedNotes(false);
      } else {
        if (!onCreate) return;

        const newCandidate: Omit<UiCandidate, "id"> = {
          name: data.name,
          email: data.email,
          phone: data.phone,
          location: data.location,
          position: data.position,
          experience: data.experience,
          skills: selectedSkills,
          source: data.source,
          jobId: data.jobId === "none" ? undefined : data.jobId || undefined,
          salaryExpectationMin: data.salaryExpectationMin,
          salaryExpectationMax: data.salaryExpectationMax,
          salaryCurrency: data.salaryCurrency,
          linkedinUrl: data.linkedinUrl,
          resumeUrl: data.resumeUrl,
          githubUrl: data.githubUrl,
          portfolioUrl: data.portfolioUrl,
          notes: data.notes,
          education: data.education || "",
          workHistory: data.workHistory || "",
          tags: selectedTags,
          initials: data.name
            .split(" ")
            .map((n) => n[0])
            .join("")
            .toUpperCase(),
          status: "sourced",
          appliedDate: new Date().toISOString().split("T")[0],
          // Required fields for UiCandidate
          salary: `${data.salaryExpectationMin || 0} - ${data.salaryExpectationMax || 0} ${data.salaryCurrency || "VND"}`,
          aiScore: 0,
        };

        // Create candidate with structured notes
        const candidateWithNotes = {
          ...newCandidate,
          notes: candidateNotes, // Use structured notes directly
        };

        await onCreate(candidateWithNotes);
      }
    } catch (error) {
      console.error("Form submission error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (mode === "edit" && !candidate) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              {mode === "edit" ? (
                <Edit className="w-5 h-5 text-primary" />
              ) : (
                <User className="w-5 h-5 text-primary" />
              )}
            </div>
            {mode === "edit"
              ? `${t.candidates?.editCandidate || "Edit Candidate"}: ${candidate?.name}`
              : t.candidates?.addCandidate || "Add New Candidate"}
          </DialogTitle>
          <DialogDescription>
            {mode === "edit"
              ? t.candidates?.updateCandidateProfile ||
                "Update candidate profile information and preferences."
              : "Create a new candidate profile manually or use AI to parse resume information automatically."}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* AI Toggle - only show in add mode */}
          {mode === "add" && (
            <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-emerald-500/5 hidden">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Sparkles className="w-5 h-5 text-primary" />
                    <div>
                      <h4 className="font-semibold">
                        AI-Powered Resume Parsing
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        Automatically extract candidate information from resume
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Checkbox
                      checked={isAiMode}
                      onCheckedChange={(checked) =>
                        setIsAiMode(checked === true)
                      }
                      className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                    />
                    <Label htmlFor="ai-mode" className="text-sm">
                      Enable AI Mode
                    </Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
          <Tabs value={currentTab} onValueChange={setCurrentTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="general">
                {mode === "add"
                  ? "Basic Info"
                  : t.candidates?.generalInformation || "General Information"}
              </TabsTrigger>
              <TabsTrigger value="documents">
                {t.candidates?.documents || "Documents"}
              </TabsTrigger>
              <TabsTrigger value="additional">
                {mode === "add"
                  ? "Additional"
                  : t.candidates?.notes || "Additional"}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-6">
              {/* Basic Information Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="w-5 h-5" />
                    {t.candidates?.basicInformation || "Basic Information"}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="name">
                        {t.candidates?.fullName || "Full Name"} *
                      </Label>
                      <Input
                        id="name"
                        {...register("name", {
                          required:
                            t.candidates?.nameRequired || "Name is required",
                        })}
                        placeholder={
                          t.candidates?.enterFullName || "Enter full name"
                        }
                        className="rounded-xl"
                      />
                      {errors.name && (
                        <p className="text-sm text-destructive">
                          {errors.name.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">
                        {t.candidates?.emailAddress || "Email Address"} *
                      </Label>
                      <Input
                        id="email"
                        type="email"
                        {...register("email", {
                          required:
                            t.candidates?.emailRequired || "Email is required",
                          pattern: {
                            value: /^\S+@\S+$/,
                            message:
                              t.candidates?.invalidEmailFormat ||
                              "Invalid email format",
                          },
                        })}
                        placeholder={
                          t.candidates?.enterEmailAddress ||
                          "Enter email address"
                        }
                        className="rounded-xl"
                      />
                      {errors.email && (
                        <p className="text-sm text-destructive">
                          {errors.email.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="phone">
                        {t.candidates?.phoneNumber || "Phone Number"}
                        {mode === "add" ? " *" : ""}
                      </Label>
                      <Input
                        id="phone"
                        {...register(
                          "phone",
                          mode === "add"
                            ? { required: "Phone number is required" }
                            : {},
                        )}
                        placeholder="0909123123"
                        className="rounded-xl"
                      />
                      {errors.phone && (
                        <p className="text-sm text-destructive">
                          {errors.phone.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="location">
                        {t.candidates?.location || "Location"}
                      </Label>
                      <Input
                        id="location"
                        {...register("location")}
                        placeholder={
                          t.candidates?.cityStateCountry ||
                          "City, State/Country"
                        }
                        className="rounded-xl"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Professional Information Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Briefcase className="w-5 h-5" />
                    {t.candidates?.professionalInformation ||
                      "Professional Information"}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="position">
                        {t.candidates?.positionRole || "Position/Role"}
                      </Label>
                      <Input
                        id="position"
                        {...register("position", {})}
                        placeholder={
                          t.candidates?.positionPlaceholder ||
                          "e.g., Senior Frontend Developer"
                        }
                        className="rounded-xl"
                      />
                      {errors.position && (
                        <p className="text-sm text-destructive">
                          {errors.position.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="experience">
                        {t.candidates?.experienceLevel || "Experience Level"}
                      </Label>
                      <Select
                        value={watch("experience")}
                        onValueChange={(value) => setValue("experience", value)}
                      >
                        <SelectTrigger className="rounded-xl">
                          <SelectValue
                            placeholder={
                              t.candidates?.selectExperienceLevel ||
                              "Select experience level"
                            }
                          />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="0-1 years">
                            {t.candidates?.entryLevel ||
                              "Entry Level (0-1 years)"}
                          </SelectItem>
                          <SelectItem value="2-3 years">
                            {t.candidates?.junior || "Junior (2-3 years)"}
                          </SelectItem>
                          <SelectItem value="3-5 years">
                            {t.candidates?.midLevel || "Mid-level (3-5 years)"}
                          </SelectItem>
                          <SelectItem value="5+ years">
                            {t.candidates?.senior || "Senior (5+ years)"}
                          </SelectItem>
                          <SelectItem value="8+ years">
                            {t.candidates?.leadPrincipal ||
                              "Lead/Principal (8+ years)"}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* v2.0.1: Separate salary expectation fields */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="salaryExpectationMin">
                          Min Salary (VND)
                        </Label>
                        <Input
                          id="salaryExpectationMin"
                          type="number"
                          {...register("salaryExpectationMin", {
                            valueAsNumber: true,
                            min: {
                              value: 0,
                              message: "Salary must be positive",
                            },
                          })}
                          placeholder="e.g., 18000000"
                          className="rounded-xl"
                        />
                        {errors.salaryExpectationMin && (
                          <span className="text-sm text-red-500">
                            {errors.salaryExpectationMin.message}
                          </span>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="salaryExpectationMax">
                          Max Salary (VND)
                        </Label>
                        <Input
                          id="salaryExpectationMax"
                          type="number"
                          {...register("salaryExpectationMax", {
                            valueAsNumber: true,
                            min: {
                              value: 0,
                              message: "Salary must be positive",
                            },
                            validate: (value) => {
                              const minSalary = watch("salaryExpectationMin");
                              if (minSalary && value && value < minSalary) {
                                return "Max salary must be greater than min salary";
                              }
                              return true;
                            },
                          })}
                          placeholder="e.g., 28000000"
                          className="rounded-xl"
                        />
                        {errors.salaryExpectationMax && (
                          <span className="text-sm text-red-500">
                            {errors.salaryExpectationMax.message}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="salaryCurrency">Currency</Label>
                      <Select
                        value={watch("salaryCurrency")}
                        onValueChange={(value) =>
                          setValue("salaryCurrency", value)
                        }
                      >
                        <SelectTrigger className="rounded-xl">
                          <SelectValue placeholder="Select currency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="VND">
                            VND (Vietnamese Dong)
                          </SelectItem>
                          <SelectItem value="USD">USD (US Dollar)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="source">
                        {t.candidates?.source || "Source"}
                      </Label>
                      <Select
                        value={watch("source")}
                        onValueChange={(value) => setValue("source", value)}
                      >
                        <SelectTrigger className="rounded-xl">
                          <SelectValue
                            placeholder={
                              t.candidates?.selectSource || "Select source"
                            }
                          />
                        </SelectTrigger>
                        <SelectContent>
                          {sourceSuggestions.map((source) => (
                            <SelectItem key={source} value={source}>
                              {source}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="jobId">
                      {t.candidates?.applyingForPosition ||
                        "Applying for Position"}
                    </Label>
                    <Select
                      value={watch("jobId")}
                      onValueChange={(value) => setValue("jobId", value)}
                    >
                      <SelectTrigger className="rounded-xl">
                        <SelectValue
                          placeholder={
                            t.candidates?.selectJobPosition ||
                            "Select job position"
                          }
                        />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">
                          No specific position
                        </SelectItem>
                        {activeJobs.map((job) => (
                          <SelectItem key={job.id} value={job.id}>
                            {job.title} - {job.department}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-4">
                    <Label>{t.candidates?.skills || "Skills"}</Label>
                    <div className="flex flex-wrap gap-2 mb-3">
                      {selectedSkills.map((skill) => (
                        <Badge
                          key={skill}
                          variant="secondary"
                          className="flex items-center gap-1"
                        >
                          {skill}
                          <button
                            type="button"
                            onClick={() => handleSkillRemove(skill)}
                            className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        value={customSkill}
                        onChange={(e) => setCustomSkill(e.target.value)}
                        placeholder={t.candidates?.addSkill || "Add a skill"}
                        className="rounded-xl"
                        onKeyPress={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            handleSkillAdd(customSkill);
                          }
                        }}
                      />
                      <Button
                        type="button"
                        onClick={() => handleSkillAdd(customSkill)}
                        className="rounded-xl"
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {skillSuggestions
                        .filter((skill) => !selectedSkills.includes(skill))
                        .slice(0, 10)
                        .map((skill) => (
                          <Button
                            key={skill}
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => handleSkillAdd(skill)}
                            className="text-xs rounded-lg"
                          >
                            {skill}
                          </Button>
                        ))}
                    </div>
                    <div className="flex flex-wrap gap-2">
                      <Label htmlFor="education">Bằng cấp / chứng chỉ</Label>
                      <Textarea
                        id="education"
                        {...register("education")}
                        placeholder={
                          "Bằng cấp, Cao đẳng, Đại học, Sau đại học..."
                        }
                        rows={4}
                        className="rounded-xl"
                      />
                    </div>
                    <div className="flex flex-wrap gap-2">
                      <Label htmlFor="notes">Quá trình làm việc</Label>
                      <Textarea
                        id="workHistory"
                        {...register("workHistory")}
                        placeholder={"Thêm quá trình làm việc của ứng viên..."}
                        rows={4}
                        className="rounded-xl"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="documents" className="space-y-6">
              <Card className="border-dashed border-2 border-muted-foreground/25 hover:border-primary/50 transition-colors">
                <CardContent className="p-8">
                  <div className="text-center space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="resumeUrl">Link Url Resume</Label>
                      <Input
                        id="resumeUrl"
                        {...register("resumeUrl")}
                        placeholder="https://drive.google.com/file/d/11-vtfXyUK6jehaLc1j84PpnU29ddnagX/preview"
                        className="rounded-xl"
                      />
                    </div>

                    <div className="hidden">
                      <Upload className="w-12 h-12 text-muted-foreground mx-auto" />
                      <div>
                        <h3 className="font-semibold text-lg">Upload Resume</h3>
                        <p className="text-muted-foreground">
                          {isAiMode
                            ? "AI will automatically extract information from the resume"
                            : "Upload PDF, DOC, or DOCX files"}
                        </p>
                      </div>
                      <div className="space-y-3">
                        <input
                          type="file"
                          accept=".pdf,.doc,.docx"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              setResumeFile(file);
                              setValue("resumeUrl", URL.createObjectURL(file));
                              toast.info("Resume uploaded successfully");
                            }
                          }}
                          className="hidden"
                          id="resume-upload"
                        />
                        <label htmlFor="resume-upload">
                          <Button
                            type="button"
                            variant="outline"
                            className="cursor-pointer rounded-xl"
                            asChild
                          >
                            <span>
                              {isAiMode && (
                                <Sparkles className="w-4 h-4 mr-2" />
                              )}
                              Choose File
                            </span>
                          </Button>
                        </label>
                        {resumeFile && (
                          <p className="text-sm text-primary">
                            ✓ {resumeFile.name} uploaded
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Extract from Resume Button */}
                    {(watch("resumeUrl") || resumeFile) && (
                      <div className="pt-4 border-t border-dashed border-muted-foreground/25">
                        <Button
                          type="button"
                          onClick={handleExtractFromResume}
                          disabled={isExtracting}
                          className="ai-button"
                        >
                          {isExtracting ? (
                            <>
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                              Extracting...
                            </>
                          ) : (
                            <>
                              <FileText className="w-4 h-4 mr-2" />
                              Extract from Resume
                            </>
                          )}
                        </Button>
                        <p className="text-xs text-muted-foreground mt-2">
                          {mode === "edit"
                            ? "AI will extract and suggest form fields from the resume"
                            : "Demo: AI will extract and suggest form fields from your resume"}
                          {mode === "add" && (
                            <>
                              <br />
                              <span className="text-primary">
                                For real extraction, save candidate first then
                                edit
                              </span>
                            </>
                          )}
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="linkedinUrl">
                    {t.candidates?.linkedinProfile || "LinkedIn Profile"}
                  </Label>
                  <Input
                    id="linkedinUrl"
                    {...register("linkedinUrl")}
                    placeholder="https://linkedin.com/in/username"
                    className="rounded-xl"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="githubUrl">
                    {t.candidates?.githubProfile || "GitHub Profile"}
                  </Label>
                  <Input
                    id="githubUrl"
                    {...register("githubUrl")}
                    placeholder="https://github.com/username"
                    className="rounded-xl"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="additional" className="space-y-6">
              {/* Enhanced notes management using local state only - changes applied on form submit */}
              {mode === "edit" && candidate && !notesOperations.isDisabled ? (
                <div className="space-y-4">
                  <div className={`p-4 rounded-lg border ${
                    hasUnsavedNotes
                      ? "bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800"
                      : "bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800"
                  }`}>
                    <div className="flex items-start gap-3">
                      <MessageSquare className={`w-5 h-5 mt-0.5 ${
                        hasUnsavedNotes
                          ? "text-orange-600 dark:text-orange-400"
                          : "text-amber-600 dark:text-amber-400"
                      }`} />
                      <div>
                        <h4 className={`text-sm font-medium ${
                          hasUnsavedNotes
                            ? "text-orange-900 dark:text-orange-100"
                            : "text-amber-900 dark:text-amber-100"
                        }`}>
                          {hasUnsavedNotes ? "Có thay đổi chưa lưu" : "Chỉnh sửa ghi chú"}
                        </h4>
                        <p className={`text-xs mt-1 ${
                          hasUnsavedNotes
                            ? "text-orange-700 dark:text-orange-300"
                            : "text-amber-700 dark:text-amber-300"
                        }`}>
                          {hasUnsavedNotes
                            ? "Bạn có thay đổi ghi chú chưa được lưu. Nhấn 'Cập nhật ứng viên' để lưu các thay đổi."
                            : "Các thay đổi ghi chú sẽ được lưu khi bạn nhấn 'Cập nhật ứng viên'."
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                  <CandidateNotesEditor
                    candidateId={candidate.id}
                    notes={candidateNotes}
                    onAddNote={handleAddNoteForCreation}
                    onUpdateNote={handleUpdateNoteForCreation}
                    onDeleteNote={handleDeleteNoteForCreation}
                    disabled={isSubmitting}
                    maxNoteLength={2000}
                  />
                </div>
              ) : mode === "add" ? (
                <div className="space-y-4">
                  <CandidateNotesEditor
                    candidateId="new-candidate"
                    notes={candidateNotes}
                    onAddNote={handleAddNoteForCreation}
                    onUpdateNote={handleUpdateNoteForCreation}
                    onDeleteNote={handleDeleteNoteForCreation}
                    disabled={isSubmitting}
                    maxNoteLength={2000}
                  />

                  {/* Info about notes for new candidates */}
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div className="flex items-start gap-3">
                      <MessageSquare className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                      <div>
                        <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                          Ghi chú cho ứng viên mới
                        </h4>
                        <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                          Các ghi chú này sẽ được lưu cùng với hồ sơ ứng viên. Mỗi ghi chú sẽ bao gồm thông tin ngư���i tạo và thời gian.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <MessageSquare className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p className="text-sm">Không thể tải ghi chú</p>
                </div>
              )}
            </TabsContent>
          </Tabs>

          <FormActions
            onCancel={handleClose}
            cancelText={t.common?.cancel || "Cancel"}
            submitText={
              mode === "edit"
                ? t.candidates?.updateCandidate || "Update Candidate"
                : t.candidates?.addCandidate || "Add Candidate"
            }
            submitLoadingText={mode === "edit" ? "Updating..." : "Adding..."}
            isSubmitting={isSubmitting}
            disableCancelWhileSubmitting={true}
          />
        </form>

        {/* Resume Extraction Modal */}
        <ResumeExtractionModal
          isOpen={showExtractionModal}
          onClose={() => setShowExtractionModal(false)}
          extractedData={extractedData}
          onApplyFields={handleApplyExtractedFields}
          isLoading={isExtracting}
        />
      </DialogContent>
    </Dialog>
  );
};

// Backwards compatibility aliases
export const EditCandidateModal = CandidateModal;
export const AddCandidateModal = CandidateModal;
