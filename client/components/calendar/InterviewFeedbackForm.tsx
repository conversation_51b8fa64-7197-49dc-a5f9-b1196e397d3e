import React, { useState, useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Button } from "../ui/button";
import { FormActions } from "../ui/form-actions";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Badge } from "../ui/badge";
import { Slider } from "../ui/slider";
import { Switch } from "../ui/switch";
import { Alert, AlertDescription } from "../ui/alert";
import { Separator } from "../ui/separator";
import {
  <PERSON>lt<PERSON>,
  Toolt<PERSON><PERSON>ontent,
  Toolt<PERSON><PERSON>rovider,
  TooltipTrigger,
} from "../ui/tooltip";
import { Link } from "react-router-dom";
import {
  Star,
  Plus,
  X,
  Save,
  Loader2,
  AlertCircle,
  CheckCircle,
  Info,
  ThumbsUp,
  ThumbsDown,
  ExternalLink,
} from "lucide-react";
import {
  InterviewFeedback,
  FeedbackFormData,
  NextRoundType,
  NEXT_ROUND_OPTIONS,
  RATING_LABELS,
  validateFeedbackData,
  getRatingLabel,
  getRatingColor,
  getScoreLabel,
  getScoreColor,
  calculateOverallScore,
} from "../../lib/types/interviewFeedback";

// Form validation schema
const feedbackSchema = z.object({
  rating: z
    .number()
    .min(1, "Đánh giá là bắt buộc")
    .max(5, "Đánh giá phải từ 1 đến 5")
    .nullable(),
  comments: z.string().min(10, "Nhận xét phải có ít nhất 10 ký tự"),
  recommend: z.boolean().nullable(),
  strengths: z.array(z.string().min(1).max(255)).optional(),
  concerns: z.array(z.string().min(1).max(255)).optional(),
  next_round_recommendation: z
    .enum([
      "screening",
      "technical",
      "case-study",
      "portfolio",
      "cultural",
      "final",
      "offer",
      "reject",
    ])
    .nullable()
    .optional(),
  technical_score: z
    .number()
    .min(0, "Điểm số phải ít nhất là 0")
    .max(100, "Điểm số tối đa là 100")
    .nullable()
    .optional(),
  communication_score: z
    .number()
    .min(0, "Điểm số phải ít nhất là 0")
    .max(100, "Điểm số tối đa là 100")
    .nullable()
    .optional(),
  cultural_fit_score: z
    .number()
    .min(0, "Điểm số phải ít nhất là 0")
    .max(100, "Điểm số tối đa là 100")
    .nullable()
    .optional(),
});

interface InterviewFeedbackFormProps {
  interview: {
    id: number;
    candidate_id?: string | number;
    candidate_name: string;
    job_id?: string | number;
    job_posting_id?: string | number;
    job_title: string;
    date: string;
    time: string;
    interviewer_id: number;
  };
  existingFeedback?: InterviewFeedback;
  onSubmit: (data: FeedbackFormData) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  isEditMode?: boolean;
}

export const InterviewFeedbackForm: React.FC<InterviewFeedbackFormProps> = ({
  interview,
  existingFeedback,
  onSubmit,
  onCancel,
  isLoading = false,
  isEditMode = false,
}) => {
  const [showDetailedScores, setShowDetailedScores] = useState(false);

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<FeedbackFormData>({
    resolver: zodResolver(feedbackSchema),
    defaultValues: {
      rating: existingFeedback?.rating || null,
      comments: existingFeedback?.comments || "",
      recommend: existingFeedback?.recommend || null,
      strengths: existingFeedback?.strengths || [],
      concerns: existingFeedback?.concerns || [],
      next_round_recommendation:
        existingFeedback?.next_round_recommendation || null,
      technical_score: existingFeedback?.technical_score || null,
      communication_score: existingFeedback?.communication_score || null,
      cultural_fit_score: existingFeedback?.cultural_fit_score || null,
    },
  });

  const {
    fields: strengthFields,
    append: appendStrength,
    remove: removeStrength,
  } = useFieldArray({
    control,
    name: "strengths",
  });

  const {
    fields: concernFields,
    append: appendConcern,
    remove: removeConcern,
  } = useFieldArray({
    control,
    name: "concerns",
  });

  const watchedValues = watch();
  const currentRating = watch("rating");
  const currentRecommend = watch("recommend");
  const currentTechnicalScore = watch("technical_score");
  const currentCommunicationScore = watch("communication_score");
  const currentCulturalFitScore = watch("cultural_fit_score");

  // Calculate overall score in real-time
  const overallScore = calculateOverallScore(
    currentTechnicalScore,
    currentCommunicationScore,
    currentCulturalFitScore,
  );

  // Auto-suggest recommendation based on scores
  useEffect(() => {
    if (currentRating && currentRating >= 4) {
      setValue("recommend", true);
    } else if (currentRating && currentRating <= 2) {
      setValue("recommend", false);
    }
  }, [currentRating, setValue]);

  const handleFormSubmit = async (data: FeedbackFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error("Error submitting feedback:", error);
    }
  };
  const StarRating: React.FC<{
    value: number | null;
    onChange: (value: number) => void;
  }> = ({ value, onChange }) => (
    <div className="flex items-center gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <button
          key={star}
          type="button"
          onClick={() => onChange(star)}
          className={`p-1 rounded transition-colors ${
            star <= (value || 0)
              ? "text-yellow-400 hover:text-yellow-500"
              : "text-gray-300 hover:text-gray-400"
          }`}
        >
          <Star className="w-6 h-6 fill-current" />
        </button>
      ))}
      {value && (
        <span className={`ml-2 text-sm font-medium ${getRatingColor(value)}`}>
          {getRatingLabel(value)}
        </span>
      )}
    </div>
  );

  const ScoreSlider: React.FC<{
    label: string;
    value: number | null;
    onChange: (value: number) => void;
    color?: string;
  }> = ({ label, value, onChange, color = "blue" }) => (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">{label}</Label>
        <div className="flex items-center gap-2">
          <span className={`text-sm font-medium ${getScoreColor(value)}`}>
            {value || 0}%
          </span>
          <Badge variant="outline" className="text-xs">
            {getScoreLabel(value)}
          </Badge>
        </div>
      </div>
      <Slider
        value={[value || 0]}
        onValueChange={(values) => onChange(values[0])}
        max={100}
        step={5}
        className="w-full"
      />
    </div>
  );

  return (
    <TooltipProvider>
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            {isEditMode
              ? "Chỉnh sửa phản hồi phỏng vấn"
              : "Gửi phản hồi phỏng vấn"}
          </CardTitle>
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">
              Cung cấp phản hồi chi tiết cho{" "}
              <Link
                to={`/candidates/detail/${interview.candidate_id || interview.candidate_name.replace(/\s+/g, "-").toLowerCase()}`}
                className="text-blue-600 hover:underline font-medium"
              >
                {interview.candidate_name}
              </Link>
              trong buổi phỏng vấn cho vị trí{" "}
              <Link
                to={`/jobs/detail/${interview.job_id || interview.job_posting_id || interview.job_title.replace(/\s+/g, "-").toLowerCase()}`}
                className="text-blue-600 hover:underline font-medium"
              >
                {interview.job_title}
              </Link>{" "}
              vào ngày {interview.date} lúc {interview.time}
            </p>
            <div className="flex gap-2 mt-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                asChild
                className="h-7 px-2 text-xs"
              >
                <Link
                  to={`/candidates/detail/${interview.candidate_id || interview.candidate_name.replace(/\s+/g, "-").toLowerCase()}`}
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  Xem ứng viên
                </Link>
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                asChild
                className="h-7 px-2 text-xs"
              >
                <Link
                  to={`/jobs/detail/${interview.job_id || interview.job_posting_id || interview.job_title.replace(/\s+/g, "-").toLowerCase()}`}
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  Xem công việc
                </Link>
              </Button>
            </div>
          </div>
        </CardHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)}>
          <CardContent className="space-y-6">
            {/* Overall Rating */}
            <div className="space-y-3">
              <Label className="text-base font-semibold">
                Đánh giá tổng thể *
              </Label>
              <StarRating
                value={currentRating}
                onChange={(value) => setValue("rating", value)}
              />
              {errors.rating && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{errors.rating.message}</AlertDescription>
                </Alert>
              )}
            </div>

            <Separator />

            {/* Recommendation */}
            <div className="space-y-3">
              <Label className="text-base font-semibold">Khuyến nghị</Label>
              <div className="flex items-center gap-4">
                <Button
                  type="button"
                  variant={currentRecommend === true ? "default" : "outline"}
                  onClick={() => setValue("recommend", true)}
                  className="flex items-center gap-2"
                >
                  <ThumbsUp className="w-4 h-4" />
                  Khuyến nghị
                </Button>
                <Button
                  type="button"
                  variant={
                    currentRecommend === false ? "destructive" : "outline"
                  }
                  onClick={() => setValue("recommend", false)}
                  className="flex items-center gap-2"
                >
                  <ThumbsDown className="w-4 h-4" />
                  Không khuyến nghị
                </Button>
                <Button
                  type="button"
                  variant={currentRecommend === null ? "secondary" : "outline"}
                  onClick={() => setValue("recommend", null)}
                >
                  Ch��a quyết định
                </Button>
              </div>
            </div>

            {/* Next Round Recommendation */}
            <div className="space-y-3">
              <Label className="text-base font-semibold">
                Khuyến nghị vòng tiếp theo
              </Label>
              <Select
                value={watchedValues.next_round_recommendation || ""}
                onValueChange={(value: NextRoundType) =>
                  setValue("next_round_recommendation", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn bước tiếp theo..." />
                </SelectTrigger>
                <SelectContent>
                  {NEXT_ROUND_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Separator />

            {/* Comments */}
            <div className="space-y-3">
              <Label htmlFor="comments" className="text-base font-semibold">
                Nhận xét chi tiết *
              </Label>
              <Textarea
                id="comments"
                {...register("comments")}
                placeholder="Cung cấp phản hồi chi tiết về hiệu suất, câu trả lời và ấn tượng tổng thể của ứng viên..."
                className="min-h-[120px]"
              />
              {errors.comments && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{errors.comments.message}</AlertDescription>
                </Alert>
              )}
            </div>

            {/* Detailed Scores Toggle */}
            <div className="flex items-center space-x-2">
              <Switch
                id="detailed-scores"
                checked={showDetailedScores}
                onCheckedChange={setShowDetailedScores}
              />
              <Label htmlFor="detailed-scores" className="text-sm">
                Cung cấp điểm số chi tiết (Kỹ thuật, Giao tiếp, Phù hợp văn hóa)
              </Label>
              <Tooltip>
                <TooltipTrigger>
                  <Info className="w-4 h-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    Chấm điểm chi tiết tùy chọn để đánh giá ứng viên tốt hơn
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>

            {/* Detailed Scores */}
            {showDetailedScores && (
              <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-between">
                  <h4 className="text-base font-semibold">Điểm số chi tiết</h4>
                  <Badge variant="outline">Tổng thể: {overallScore}%</Badge>
                </div>

                <ScoreSlider
                  label="Kỹ năng kỹ thuật"
                  value={currentTechnicalScore}
                  onChange={(value) => setValue("technical_score", value)}
                />

                <ScoreSlider
                  label="Kỹ năng giao tiếp"
                  value={currentCommunicationScore}
                  onChange={(value) => setValue("communication_score", value)}
                />

                <ScoreSlider
                  label="Phù hợp văn hóa"
                  value={currentCulturalFitScore}
                  onChange={(value) => setValue("cultural_fit_score", value)}
                />
              </div>
            )}

            <Separator />

            {/* Strengths */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-base font-semibold">Điểm mạnh</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => appendStrength("")}
                  className="flex items-center gap-1"
                >
                  <Plus className="w-4 h-4" />
                  Thêm điểm mạnh
                </Button>
              </div>
              <div className="space-y-2">
                {strengthFields.map((field, index) => (
                  <div key={field.id} className="flex items-center gap-2">
                    <Input
                      {...register(`strengths.${index}`)}
                      placeholder="Ví dụ: Kỹ năng giải quyết vấn đề tốt"
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeStrength(index)}
                      className="text-destructive hover:text-destructive"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            {/* Concerns */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-base font-semibold">
                  Khu vực cần cải thiện
                </Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => appendConcern("")}
                  className="flex items-center gap-1"
                >
                  <Plus className="w-4 h-4" />
                  Thêm mối quan tâm
                </Button>
              </div>
              <div className="space-y-2">
                {concernFields.map((field, index) => (
                  <div key={field.id} className="flex items-center gap-2">
                    <Input
                      {...register(`concerns.${index}`)}
                      placeholder="Ví dụ: Cần thêm kinh nghiệm với React"
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeConcern(index)}
                      className="text-destructive hover:text-destructive"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>

          <CardFooter className="flex flex-col gap-4">
            <div className="text-sm text-muted-foreground self-start">
              * Trường bắt buộc
            </div>
            <FormActions
              onCancel={onCancel}
              cancelText="Hủy"
              submitText={isEditMode ? "Cập nhật phản hồi" : "Gửi phản hồi"}
              submitLoadingText={isEditMode ? "Đang cập nhật..." : "Đang gửi..."}
              isSubmitting={isLoading || isSubmitting}
              disableCancelWhileSubmitting={true}
              className="w-full pt-0 border-t-0"
              submitButtonProps={{
                className: "ai-button flex items-center gap-2",
                children: (
                  <>
                    <Save className="w-4 h-4" />
                    {isEditMode ? "Cập nhật phản hồi" : "Gửi phản hồi"}
                  </>
                ),
              }}
            />
          </CardFooter>
        </form>
      </Card>
    </TooltipProvider>
  );
};

export default InterviewFeedbackForm;
