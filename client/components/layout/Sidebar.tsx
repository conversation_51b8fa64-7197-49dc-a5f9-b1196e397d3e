import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { useTranslation } from "@/lib/i18n";
import { useTheme } from "@/components/theme-provider";
import { useSidebar } from "@/contexts/SidebarContext";
import { Button } from "@/components/ui/button";
import {
  LayoutDashboard,
  Users,
  Building2,
  GitBranch,
  Calendar,
  MessageSquare,
  BarChart3,
  Settings,
  Zap,
  UserCheck,
  User,
  UsersIcon,
  Moon,
  Sun,
  Monitor,
  GitCompare,
} from "lucide-react";

export const Sidebar = () => {
  const location = useLocation();
  const { t, language } = useTranslation();
  const { theme, setTheme } = useTheme();
  const { isCollapsed } = useSidebar();

  const navigation = [
    { name: t.nav.dashboard, href: "/dashboard", icon: LayoutDashboard },
    { name: t.nav.candidates, href: "/candidates", icon: Users },
    { name: t.nav.jobs, href: "/jobs", icon: Building2 },
    { name: t.nav.pipeline, href: "/pipeline", icon: GitBranch },
    { name: t.nav.calendar, href: "/calendar", icon: Calendar },
    { name: t.nav.interviewers, href: "/interviewers", icon: UserCheck },
    { name: t.nav.messages, href: "/messages", icon: MessageSquare },
    // { name: t.nav.analytics, href: "/analytics", icon: BarChart3 },
  ];

  const secondaryNavigation = [
    { name: t.nav.profile, href: "/profile", icon: User },
    { name: t.nav.team, href: "/team", icon: UsersIcon },
    // { name: t.nav.settings, href: "/settings", icon: Settings },
  ];
  return (
    <div
      className={cn(
        "bg-sidebar border-r border-sidebar-border flex flex-col relative transition-all duration-300 ease-in-out",
        isCollapsed ? "w-16" : "w-64",
      )}
    >
      {/* AI Glow Effect */}
      <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-primary/10 to-transparent pointer-events-none" />

      {/* Logo */}
      <div
        className={cn(
          "flex items-center border-b border-sidebar-border relative z-10 transition-all duration-300",
          isCollapsed ? "justify-center p-3" : "gap-3 p-6",
        )}
      >
        <div className="w-10 h-10 bg-primary rounded-xl flex items-center justify-center shadow-lg animate-pulse-green">
          <Zap className="w-5 h-5 text-primary-foreground" />
        </div>
        {!isCollapsed && (
          <div className="transition-opacity duration-300">
            <h1 className="text-xl font-bold text-sidebar-foreground">
              HireFlow
            </h1>
            <p className="text-xs text-sidebar-foreground/60 font-medium">
              AI Recruiting
            </p>
          </div>
        )}
      </div>

      {/* Navigation */}
      <nav
        className={cn(
          "flex-1 py-6 space-y-2 relative z-10 transition-all duration-300",
          isCollapsed ? "px-2" : "px-4",
        )}
      >
        {navigation.map((item) => {
          const isActive = location.pathname === item.href;
          return (
            <Link
              key={item.name}
              to={item.href}
              className={cn(
                "sidebar-nav-item group relative",
                {
                  active: isActive,
                },
                isCollapsed ? "justify-center p-3" : "",
              )}
              title={isCollapsed ? item.name : undefined}
            >
              <div className="relative">
                <item.icon className="w-5 h-5 transition-transform group-hover:scale-110" />
                {isActive && (
                  <div className="absolute -inset-1 bg-primary/20 rounded-lg blur-sm -z-10" />
                )}
              </div>
              {!isCollapsed && (
                <>
                  <span className="font-medium transition-opacity duration-300">
                    {item.name}
                  </span>
                  {isActive && (
                    <div className="ml-auto w-2 h-2 bg-primary-foreground rounded-full opacity-80" />
                  )}
                </>
              )}
              {/* Tooltip for collapsed state */}
              {isCollapsed && (
                <div className="absolute left-full ml-2 px-2 py-1 bg-foreground text-background text-sm rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                  {item.name}
                </div>
              )}
            </Link>
          );
        })}
      </nav>

      {/* AI Assistant Banner */}
      <div className=" mx-4 mb-4 p-4 bg-gradient-to-br from-primary/20 to-emerald-500/20 rounded-xl border border-primary/30 relative hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 animate-shimmer" />
        <div className="relative">
          <div className="flex items-center gap-2 mb-2">
            <Zap className="w-4 h-4 text-primary" />
            <span className="text-sm font-semibold text-sidebar-foreground">
              AI Assistant
            </span>
          </div>
          <p className="text-xs text-sidebar-foreground/80 mb-3">
            Let AI help you find the perfect candidates faster
          </p>
          <button className="w-full py-2 px-3 bg-primary text-primary-foreground rounded-lg text-xs font-medium hover:bg-primary/90 transition-colors">
            Try AI Search
          </button>
        </div>
      </div>

      {/* Secondary Navigation */}
      <nav
        className={cn(
          "py-4 border-t border-sidebar-border space-y-1 relative z-10 transition-all duration-300",
          isCollapsed ? "px-2" : "px-4",
        )}
      >
        {secondaryNavigation.map((item) => {
          const isActive = location.pathname === item.href;
          return (
            <Link
              key={item.name}
              to={item.href}
              className={cn(
                "sidebar-nav-item group relative",
                {
                  active: isActive,
                },
                isCollapsed ? "justify-center p-3" : "",
              )}
              title={isCollapsed ? item.name : undefined}
            >
              <item.icon className="w-5 h-5" />
              {!isCollapsed && (
                <span className="font-medium transition-opacity duration-300">
                  {item.name}
                </span>
              )}
              {/* Tooltip for collapsed state */}
              {isCollapsed && (
                <div className="absolute left-full ml-2 px-2 py-1 bg-foreground text-background text-sm rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                  {item.name}
                </div>
              )}
            </Link>
          );
        })}

        {/* Theme Toggle */}
        {!isCollapsed ? (
          <div className="flex items-center gap-2 px-4 py-3 rounded-xl">
            <div className="flex items-center gap-1">
              {theme === "light" ? (
                <Sun className="w-4 h-4 text-sidebar-foreground/60" />
              ) : theme === "dark" ? (
                <Moon className="w-4 h-4 text-sidebar-foreground/60" />
              ) : (
                <Monitor className="w-4 h-4 text-sidebar-foreground/60" />
              )}
              <span className="text-sm font-medium text-sidebar-foreground/80">
                {language === "vi" ? "Giao diện" : "Theme"}
              </span>
            </div>
            <div className="ml-auto flex items-center bg-sidebar-accent rounded-lg p-1">
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "h-6 w-6 p-0 rounded transition-colors",
                  theme === "light" &&
                    "bg-sidebar-primary text-sidebar-primary-foreground",
                )}
                onClick={() => setTheme("light")}
                title={language === "vi" ? "Chế độ sáng" : "Light mode"}
              >
                <Sun className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "h-6 w-6 p-0 rounded transition-colors",
                  theme === "dark" &&
                    "bg-sidebar-primary text-sidebar-primary-foreground",
                )}
                onClick={() => setTheme("dark")}
                title={language === "vi" ? "Chế độ tối" : "Dark mode"}
              >
                <Moon className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "h-6 w-6 p-0 rounded transition-colors",
                  theme === "system" &&
                    "bg-sidebar-primary text-sidebar-primary-foreground",
                )}
                onClick={() => setTheme("system")}
                title={language === "vi" ? "Theo hệ thống" : "System theme"}
              >
                <Monitor className="h-3 w-3" />
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex justify-center">
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "h-8 w-8 p-0 rounded-lg transition-colors",
                theme === "light" && "bg-sidebar-accent",
              )}
              onClick={() => {
                const themes = ["light", "dark", "system"] as const;
                const currentIndex = themes.indexOf(theme as any);
                const nextIndex = (currentIndex + 1) % themes.length;
                setTheme(themes[nextIndex]);
              }}
              title={language === "vi" ? "Đổi giao diện" : "Toggle theme"}
            >
              {theme === "light" ? (
                <Sun className="h-4 w-4" />
              ) : theme === "dark" ? (
                <Moon className="h-4 w-4" />
              ) : (
                <Monitor className="h-4 w-4" />
              )}
            </Button>
          </div>
        )}
      </nav>
    </div>
  );
};
