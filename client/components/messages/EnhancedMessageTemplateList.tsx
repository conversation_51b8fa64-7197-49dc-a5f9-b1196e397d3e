import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import {
  FileText,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Eye,
  Copy,
  Trash2,
  RefreshCw,
  AlertCircle,
  Loader2,
  Mail,
  MessageSquare,
  Filter,
  X,
} from "lucide-react";
import {
  MessageTemplate,
  messageTemplateService,
  TemplateListParams,
} from "@/lib/services/messageTemplateService";
import { toast } from "sonner";
import { cn, safeFormatDistanceToNow } from "@/lib/utils";
import { useTranslation } from "@/lib/i18n";

interface EnhancedMessageTemplateListProps {
  onTemplateSelect?: (template: MessageTemplate) => void;
  onTemplateEdit?: (template: MessageTemplate) => void;
  onTemplateCreate?: () => void;
  onTemplatePreview?: (template: MessageTemplate) => void;
  showActions?: boolean;
  showCreateButton?: boolean;
  selectable?: boolean;
  selectedTemplateId?: number;
  className?: string;
  category?: string;
  type?: string;
}

export default function EnhancedMessageTemplateList({
  onTemplateSelect,
  onTemplateEdit,
  onTemplateCreate,
  onTemplatePreview,
  showActions = true,
  showCreateButton = true,
  selectable = false,
  selectedTemplateId,
  className,
  category,
  type,
}: EnhancedMessageTemplateListProps) {
  const { t } = useTranslation();
  const [templates, setTemplates] = useState<MessageTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  // Filters
  const [searchTerm, setSearchTerm] = useState("");
  const [filterCategory, setFilterCategory] = useState(category || "all");
  const [filterType, setFilterType] = useState(type || "all");
  const [filterStatus, setFilterStatus] = useState("all");
  const [sortBy, setSortBy] = useState("-created_at");

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalTemplates, setTotalTemplates] = useState(0);

  // Load templates
  useEffect(() => {
    loadTemplates();
  }, [
    currentPage,
    searchTerm,
    filterCategory,
    filterType,
    filterStatus,
    sortBy,
  ]);

  const loadTemplates = async (retryAttempt = 0) => {
    try {
      setLoading(true);
      setError(null);

      const params: TemplateListParams = {
        page: currentPage,
        per_page: 12,
        sort: sortBy,
        include: "createdBy",
      };

      // Add filters
      if (searchTerm) params.search = searchTerm;
      if (filterCategory !== "all") params.category = filterCategory;
      if (filterType !== "all") params.type = filterType;
      if (filterStatus === "active") params.is_active = true;
      if (filterStatus === "inactive") params.is_active = false;

      // Use authenticated API
      const response = await messageTemplateService.getTemplates(params);

      if (response.data) {
        setTemplates(response.data);
        setTotalPages(response.meta?.last_page || 1);
        setTotalTemplates(response.meta?.total || 0);
        setRetryCount(0);
      } else {
        throw new Error("No data received from API");
      }
    } catch (err) {
      console.error("Error loading templates:", err);

      const errorMessage = err instanceof Error ? err.message : "Unknown error";

      // Handle different types of errors
      if (errorMessage.includes("network") || errorMessage.includes("fetch")) {
        setError("Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet.");
      } else if (
        errorMessage.includes("401") ||
        errorMessage.includes("unauthorized")
      ) {
        setError("Bạn không có quyền truy cập. Vui lòng đăng nhập lại.");
      } else if (errorMessage.includes("404")) {
        setError(
          "Không tìm thấy dữ liệu template. Có thể API chưa được thiết lập.",
        );
      } else if (errorMessage.includes("500")) {
        setError("Lỗi máy chủ. Vui lòng thử lại sau ít phút.");
      } else {
        setError("Không thể tải danh sách template. Vui lòng thử lại.");
      }

      // Retry logic for network errors
      if (
        retryAttempt < 2 &&
        (errorMessage.includes("network") || errorMessage.includes("fetch"))
      ) {
        setTimeout(
          () => {
            loadTemplates(retryAttempt + 1);
          },
          1000 * (retryAttempt + 1),
        );
        return;
      }

      setRetryCount(retryAttempt);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    setCurrentPage(1);
    loadTemplates();
  };

  const handleTemplateAction = async (
    action: string,
    template: MessageTemplate,
  ) => {
    try {
      switch (action) {
        case "select":
          onTemplateSelect?.(template);
          break;
        case "edit":
          onTemplateEdit?.(template);
          break;
        case "preview":
          onTemplatePreview?.(template);
          break;
        case "copy":
          await navigator.clipboard.writeText(template.content);
          toast.success("Nội dung template đã được sao chép");
          break;
        case "duplicate":
          const response = await messageTemplateService.duplicateTemplate(
            template.id,
          );
          if (response.status === "success") {
            toast.success("Template đã được sao chép thành công");
            loadTemplates();
          }
          break;
        case "delete":
          if (window.confirm("Bạn có chắc chắn muốn xóa template này?")) {
            await messageTemplateService.deleteTemplate(template.id);
            toast.success("Template đã được xóa thành công");
            loadTemplates();
          }
          break;
      }
    } catch (err) {
      console.error("Error performing action:", err);
      toast.error("Không thể thực hiện hành động này");
    }
  };

  const clearFilters = () => {
    setSearchTerm("");
    setFilterCategory("all");
    setFilterType("all");
    setFilterStatus("all");
    setSortBy("-created_at");
    setCurrentPage(1);
  };

  const hasActiveFilters =
    searchTerm ||
    filterCategory !== "all" ||
    filterType !== "all" ||
    filterStatus !== "all";

  // Render loading state
  if (loading && retryCount === 0) {
    return (
      <div className={cn("space-y-6", className)}>
        {/* Filter skeleton */}
        <div className="flex flex-col sm:flex-row gap-4">
          <Skeleton className="h-10 flex-1" />
          <Skeleton className="h-10 w-40" />
          <Skeleton className="h-10 w-40" />
          <Skeleton className="h-10 w-32" />
        </div>

        {/* Grid skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                  <Skeleton className="h-6 w-16" />
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <Skeleton className="h-3 w-full mb-2" />
                <Skeleton className="h-3 w-2/3 mb-4" />
                <div className="flex gap-2">
                  <Skeleton className="h-5 w-12" />
                  <Skeleton className="h-5 w-16" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className={cn("space-y-6", className)}>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{error}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              className="ml-4"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Thử lại
            </Button>
          </AlertDescription>
        </Alert>

        {/* Show partial content if we have some cached data */}
        {templates.length > 0 && (
          <div className="opacity-50">
            <p className="text-sm text-muted-foreground mb-4">
              Hiển thị dữ liệu từ lần tải trước (có thể không cập nhật):
            </p>
            <TemplateGrid />
          </div>
        )}
      </div>
    );
  }

  // Template Grid Component
  function TemplateGrid() {
    return (
      <>
        {/* Statistics */}
        {totalTemplates > 0 && (
          <div className="flex items-center justify-between mb-4">
            <div className="text-sm text-muted-foreground">
              Hiển thị {templates.length} trong {totalTemplates} template
            </div>
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-xs"
              >
                <X className="w-3 h-3 mr-1" />
                Xóa bộ lọc
              </Button>
            )}
          </div>
        )}

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {templates.map((template) => (
            <Card
              key={template.id}
              className={cn(
                "hover:shadow-md transition-all cursor-pointer group",
                selectable &&
                  selectedTemplateId === template.id &&
                  "ring-2 ring-primary",
                !template.is_active && "opacity-70",
              )}
              onClick={() => handleTemplateAction("select", template)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="min-w-0 flex-1">
                    <CardTitle className="text-sm font-medium leading-tight mb-1 line-clamp-2">
                      {template.name}
                    </CardTitle>
                    <CardDescription className="text-xs line-clamp-1">
                      {template.subject || "Không có tiêu đề"}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-1 ml-2">
                    <Badge variant="outline" className="text-xs">
                      {messageTemplateService.getCategoryDisplayName(
                        template.category,
                      )}
                    </Badge>
                    {showActions && (
                      <DropdownMenu>
                        <DropdownMenuTrigger
                          asChild
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <MoreHorizontal className="h-3 w-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuLabel>Hành động</DropdownMenuLabel>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleTemplateAction("preview", template);
                            }}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            Xem trước
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleTemplateAction("edit", template);
                            }}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Chỉnh sửa
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleTemplateAction("copy", template);
                            }}
                          >
                            <Copy className="mr-2 h-4 w-4" />
                            Sao chép nội dung
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleTemplateAction("duplicate", template);
                            }}
                          >
                            <Copy className="mr-2 h-4 w-4" />
                            Nhân bản template
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleTemplateAction("delete", template);
                            }}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Xóa
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-muted-foreground line-clamp-2 mb-3">
                  {template.content}
                </p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">
                      {template.type === "email" ? (
                        <Mail className="w-3 h-3 mr-1" />
                      ) : (
                        <MessageSquare className="w-3 h-3 mr-1" />
                      )}
                      {messageTemplateService.getTypeDisplayName(template.type)}
                    </Badge>
                    {template.variables && template.variables.length > 0 && (
                      <Badge variant="outline" className="text-xs">
                        {template.variables.length} biến
                      </Badge>
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {template.messages_count || 0} sử dụng
                  </div>
                </div>
                {template.created_at && (
                  <div className="text-xs text-muted-foreground mt-2">
                    {safeFormatDistanceToNow(template.created_at, {
                      addSuffix: true,
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {templates.length === 0 && (
          <Card className="p-8 text-center">
            <div className="flex flex-col items-center gap-4">
              <FileText className="w-12 h-12 text-muted-foreground" />
              <div>
                <h3 className="text-lg font-semibold">
                  {hasActiveFilters
                    ? "Không tìm thấy template"
                    : "Chưa có template nào"}
                </h3>
                <p className="text-muted-foreground">
                  {hasActiveFilters
                    ? "Thử điều chỉnh bộ lọc tìm kiếm."
                    : "Tạo template đầu tiên để bắt đầu."}
                </p>
              </div>
              {hasActiveFilters ? (
                <Button variant="outline" onClick={clearFilters}>
                  Xóa bộ lọc
                </Button>
              ) : (
                showCreateButton &&
                onTemplateCreate && (
                  <Button onClick={onTemplateCreate} className="gap-2">
                    <Plus className="w-4 h-4" />
                    Tạo Template đầu tiên
                  </Button>
                )
              )}
            </div>
          </Card>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-center gap-2 mt-6">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage <= 1}
              onClick={() => setCurrentPage(currentPage - 1)}
            >
              Trước
            </Button>
            <span className="text-sm text-muted-foreground px-2">
              Trang {currentPage} / {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage >= totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}
            >
              Sau
            </Button>
          </div>
        )}
      </>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Quản lý Template</h2>
          <p className="text-sm text-muted-foreground">
            Tạo và quản lý template tin nhắn tuyển dụng
          </p>
        </div>
        {showCreateButton && onTemplateCreate && (
          <Button onClick={onTemplateCreate} className="gap-2">
            <Plus className="w-4 h-4" />
            Template mới
          </Button>
        )}
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Tìm kiếm template..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 rounded-xl"
          />
        </div>
        <Select value={filterCategory} onValueChange={setFilterCategory}>
          <SelectTrigger className="w-full sm:w-40 rounded-xl">
            <SelectValue placeholder="Danh mục" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tất cả danh mục</SelectItem>
            <SelectItem value="interview">Phỏng vấn</SelectItem>
            <SelectItem value="offer">Đề nghị</SelectItem>
            <SelectItem value="feedback">Phản hồi</SelectItem>
            <SelectItem value="reminder">Nhắc nhở</SelectItem>
            <SelectItem value="rejection">Từ chối</SelectItem>
            <SelectItem value="welcome">Chào mừng</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="w-full sm:w-32 rounded-xl">
            <SelectValue placeholder="Loại" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tất cả loại</SelectItem>
            <SelectItem value="email">Email</SelectItem>
            <SelectItem value="sms">SMS</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-full sm:w-32 rounded-xl">
            <SelectValue placeholder="Trạng thái" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tất cả</SelectItem>
            <SelectItem value="active">Hoạt động</SelectItem>
            <SelectItem value="inactive">Không hoạt động</SelectItem>
          </SelectContent>
        </Select>
        <Button
          variant="outline"
          onClick={handleRefresh}
          className="rounded-xl gap-2"
        >
          <RefreshCw className="w-4 h-4" />
          Làm mới
        </Button>
      </div>

      {/* Retry loading state */}
      {loading && retryCount > 0 && (
        <Alert>
          <Loader2 className="h-4 w-4 animate-spin" />
          <AlertDescription>
            Đang thử kết nối lại... (l��n thử {retryCount + 1}/3)
          </AlertDescription>
        </Alert>
      )}

      {/* Template Grid */}
      <TemplateGrid />
    </div>
  );
}
