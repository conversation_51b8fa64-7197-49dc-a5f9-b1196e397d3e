import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import {
  Mail,
  Reply,
  Forward,
  Archive,
  Trash2,
  Star,
  MoreHorizontal,
  Send,
  Clock,
  CheckCircle,
  FileText,
  AlertCircle,
  Loader2,
  MessageSquare,
  GitBranch,
  X,
  Eye,
  User,
  Calendar,
  Tag,
  ExternalLink,
  Edit,
  AtSign,
} from "lucide-react";
import {
  Message,
  MessageThread,
  messageService,
  ReplyMessageData,
} from "@/lib/services/messageService";
import { toast } from "sonner";
import { cn, safeFormatDistanceToNow } from "@/lib/utils";
import { useTranslation } from "@/lib/i18n";

interface MessageDetailProps {
  message: Message;
  isOpen: boolean;
  onClose: () => void;
  onReply?: (message: Message) => void;
  onForward?: (message: Message) => void;
  onEdit?: (message: Message) => void;
  onUpdate?: () => void;
  showThread?: boolean;
  className?: string;
}

export default function MessageDetail({
  message,
  isOpen,
  onClose,
  onReply,
  onForward,
  onEdit,
  onUpdate,
  showThread = true,
  className,
}: MessageDetailProps) {
  const { t } = useTranslation();
  const [thread, setThread] = useState<MessageThread | null>(null);
  const [loadingThread, setLoadingThread] = useState(false);
  const [isReplying, setIsReplying] = useState(false);
  const [replyForm, setReplyForm] = useState({
    subject: "",
    content: "",
    priority: 5,
  });
  const [sendingReply, setSendingReply] = useState(false);

  // Load thread when message changes
  useEffect(() => {
    if (showThread && message?.id) {
      loadThread();
    }
  }, [message?.id, showThread]);

  const loadThread = async () => {
    try {
      setLoadingThread(true);
      const response = await messageService.getMessageThread(message.id);
      if (response.status === "success") {
        setThread(response.data);
      }
    } catch (err) {
      console.error("Error loading thread:", err);
      // Don't show error toast for thread loading as it's optional
    } finally {
      setLoadingThread(false);
    }
  };

  const handleReply = () => {
    setIsReplying(true);
    setReplyForm({
      subject: message.subject?.startsWith("Re:")
        ? message.subject
        : `Re: ${message.subject || "Không có tiêu đề"}`,
      content: "",
      priority: Math.min(message.priority + 1, 10),
    });
  };

  const handleSendReply = async () => {
    if (!replyForm.content.trim()) {
      toast.error("Vui lòng nhập nội dung trả lời");
      return;
    }

    try {
      setSendingReply(true);

      const replyData: ReplyMessageData = {
        type: message.type,
        category: message.category,
        to_email: message.from_email || message.candidate?.email,
        to_name: message.from_name || message.candidate?.name,
        subject: replyForm.subject,
        content: replyForm.content,
        priority: replyForm.priority,
      };

      const response = await messageService.replyToMessage(
        message.id,
        replyData,
      );

      if (response.status === "success") {
        toast.success("Phản hồi đã được gửi thành công!");
        setIsReplying(false);
        setReplyForm({ subject: "", content: "", priority: 5 });

        // Reload thread to show new reply
        if (showThread) {
          loadThread();
        }

        // Notify parent to update
        onUpdate?.();
      }
    } catch (err) {
      console.error("Error sending reply:", err);
      toast.error("Không thể gửi phản hồi. Vui lòng thử lại.");
    } finally {
      setSendingReply(false);
    }
  };

  const generateGmailUrl = () => {
    const gmailBaseUrl = "https://mail.google.com/mail/u/0/?fs=1";
    const params = new URLSearchParams();

    if (message.to_email || message?.candidate.email) {
      params.append("to", message.to_email || "");
    }
    if (message.subject) {
      params.append("su", message.subject);
    }
    if (message.content) {
      params.append("body", message.content);
    }
    params.append("tf", "cm");

    return `${gmailBaseUrl}&${params.toString()}`;
  };

  const handleGmailQuickSend = () => {
    if (!message.to_email) {
      toast.error("Vui lòng chọn người nhận trước khi gửi qua Gmail");
      return;
    }

    const gmailUrl = generateGmailUrl();
    window.open(gmailUrl, "_blank");
    toast.success("Mở Gmail để gửi tin nhắn");
  };
  const handleAction = async (action: string) => {
    try {
      switch (action) {
        case "edit":
          if (onEdit) {
            onEdit(message);
          } else {
            toast.info("Chức năng chỉnh sửa không khả dụng");
          }
          break;
        case "reply":
          if (onReply) {
            onReply(message);
          } else {
            handleReply();
          }
          break;
        case "forward":
          onForward?.(message);
          break;
        case "archive":
          // Implement archive functionality
          toast.success("Tin nhắn đã được lưu trữ");
          break;
        case "delete":
          if (window.confirm("Bạn có chắc chắn muốn xóa tin nhắn này?")) {
            await messageService.deleteMessage(message.id);
            toast.success("Tin nhắn đã được xóa");
            onClose();
            onUpdate?.();
          }
          break;
        case "star":
          // Implement star functionality
          toast.success("Tin nhắn đã được đánh dấu");
          break;
        case "mark-read":
          await messageService.updateMessage(message.id, { status: "read" });
          toast.success("Đã đánh dấu là đã đọc");
          onUpdate?.();
          break;
      }
    } catch (err) {
      console.error("Error performing action:", err);
      toast.error("Không thể thực hiện hành động này");
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "sent":
        return <Send className="h-4 w-4 text-blue-600" />;
      case "delivered":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "read":
        return <CheckCircle className="h-4 w-4 text-emerald-600" />;
      case "draft":
        return <FileText className="h-4 w-4 text-gray-600" />;
      case "failed":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      case "queued":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <Mail className="h-4 w-4" />;
    }
  };

  const getStatusDisplayName = (status: string) => {
    return messageService.getStatusDisplayName(status);
  };

  const getStatusColor = (status: string) => {
    return messageService.getStatusColor(status);
  };

  const getPriorityColor = (priority: number) => {
    return messageService.getPriorityColor(priority);
  };

  const getPriorityDisplayName = (priority: number) => {
    return messageService.getPriorityDisplayName(priority);
  };

  if (!message) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <Mail className="w-5 h-5" />
              Chi tiết tin nhắn
            </DialogTitle>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Hành động</DropdownMenuLabel>
                {message.status === "draft" && (
                  <>
                    <DropdownMenuItem onClick={() => handleAction("edit")}>
                      <Edit className="mr-2 h-4 w-4" />
                      Chỉnh sửa nháp
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                  </>
                )}
                <DropdownMenuItem onClick={() => handleAction("reply")}>
                  <Reply className="mr-2 h-4 w-4" />
                  Trả lời
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleAction("forward")}>
                  <Forward className="mr-2 h-4 w-4" />
                  Chuyển tiếp
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleAction("star")}>
                  <Star className="mr-2 h-4 w-4" />
                  Đánh dấu
                </DropdownMenuItem>
                {message.status !== "read" && (
                  <DropdownMenuItem onClick={() => handleAction("mark-read")}>
                    <Eye className="mr-2 h-4 w-4" />
                    Đánh dấu đã đọc
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Archive className="mr-2 h-4 w-4" />
                  Lưu trữ
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="text-destructive"
                  onClick={() => handleAction("delete")}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Xóa
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Message Header */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarFallback>
                        {(message.candidate?.name || message.from_name || "?")
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div className="min-w-0 flex-1">
                      <CardTitle className="text-lg">
                        {message.subject || "Không có tiêu đề"}
                      </CardTitle>
                      <div className="space-y-1 text-sm text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <User className="w-3 h-3" />
                          <span>
                            Từ:{" "}
                            {message.from_name ||
                              message.candidate?.name ||
                              "Không rõ"}
                            {(message.from_email || message.candidate?.email) &&
                              ` (${message.from_email || message.candidate?.email})`}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <User className="w-3 h-3" />
                          <span>
                            Tới: {message.to_name || "Không rõ"}
                            {message.to_email && ` (${message.to_email})`}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="w-3 h-3" />
                          <span>
                            {safeFormatDistanceToNow(message.created_at, {
                              addSuffix: true,
                            })}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-2">
                    <Badge className={getStatusColor(message.status)}>
                      {getStatusDisplayName(message.status)}
                    </Badge>
                    {message.priority > 5 && (
                      <Badge className={getPriorityColor(message.priority)}>
                        {getPriorityDisplayName(message.priority)}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Message Metadata */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Tag className="w-3 h-3 text-muted-foreground" />
                <span className="text-muted-foreground">Loại:</span>
                <Badge variant="outline">
                  {messageService.getTypeDisplayName(message.type)}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <Tag className="w-3 h-3 text-muted-foreground" />
                <span className="text-muted-foreground">Danh mục:</span>
                <Badge variant="outline">
                  {messageService.getCategoryDisplayName(message.category)}
                </Badge>
              </div>
              {message.template && (
                <div className="flex items-center gap-2">
                  <FileText className="w-3 h-3 text-muted-foreground" />
                  <span className="text-muted-foreground">Template:</span>
                  <Badge variant="secondary">{message.template.name}</Badge>
                </div>
              )}
              {message.thread_id && (
                <div className="flex items-center gap-2">
                  <MessageSquare className="w-3 h-3 text-muted-foreground" />
                  <span className="text-muted-foreground">Thread:</span>
                  <Badge variant="outline">{message.thread_id}</Badge>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Message Content */}
          <Card>
            <CardContent className="p-6">
              <div className="prose prose-sm max-w-none">
                <pre className="whitespace-pre-wrap font-sans text-sm leading-relaxed">
                  {message.content}
                </pre>
              </div>
            </CardContent>
          </Card>

          {/* Thread */}
          {showThread && false && (
            <>
              <Separator />
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <GitBranch className="w-4 h-4" />
                  <h3 className="text-lg font-semibold">Chuỗi hội thoại</h3>
                  {loadingThread && (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  )}
                </div>

                {thread && thread.messages.length > 1 ? (
                  <div className="space-y-3">
                    {thread.messages
                      .filter((msg) => msg.id !== message.id)
                      .map((msg) => (
                        <Card
                          key={msg.id}
                          className="border-l-4 border-l-primary/20"
                        >
                          <CardHeader className="pb-2">
                            <div className="flex items-start justify-between">
                              <div className="flex items-center gap-2">
                                <Avatar className="h-6 w-6">
                                  <AvatarFallback className="text-xs">
                                    {(
                                      msg.candidate?.name ||
                                      msg.from_name ||
                                      "?"
                                    )
                                      .split(" ")
                                      .map((n) => n[0])
                                      .join("")}
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <CardTitle className="text-sm">
                                    {msg.subject || "Không có tiêu đề"}
                                  </CardTitle>
                                  <CardDescription className="text-xs">
                                    {msg.candidate?.name || msg.from_name} •{" "}
                                    {safeFormatDistanceToNow(msg.created_at, {
                                      addSuffix: true,
                                    })}
                                  </CardDescription>
                                </div>
                              </div>
                              <div className="flex items-center gap-1">
                                {getStatusIcon(msg.status)}
                                <Badge
                                  className={cn(
                                    "text-xs",
                                    getStatusColor(msg.status),
                                  )}
                                >
                                  {getStatusDisplayName(msg.status)}
                                </Badge>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="pt-0">
                            <p className="text-sm text-muted-foreground line-clamp-3">
                              {msg.content}
                            </p>
                          </CardContent>
                        </Card>
                      ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p>Đây là tin nhắn đầu tiên trong chuỗi</p>
                  </div>
                )}
              </div>
            </>
          )}

          {/* Reply Form */}
          {isReplying && (
            <>
              <Separator />
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Trả lời tin nhắn</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsReplying(false)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="reply-subject">Tiêu đề</Label>
                    <Input
                      id="reply-subject"
                      value={replyForm.subject}
                      onChange={(e) =>
                        setReplyForm((prev) => ({
                          ...prev,
                          subject: e.target.value,
                        }))
                      }
                      className="rounded-xl"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="reply-content">Nội dung *</Label>
                    <Textarea
                      id="reply-content"
                      placeholder="Nhập nội dung trả lời..."
                      value={replyForm.content}
                      onChange={(e) =>
                        setReplyForm((prev) => ({
                          ...prev,
                          content: e.target.value,
                        }))
                      }
                      rows={6}
                      className="rounded-xl resize-none"
                    />
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setIsReplying(false)}
                      disabled={sendingReply}
                    >
                      Hủy
                    </Button>
                    <Button
                      onClick={handleSendReply}
                      disabled={sendingReply || !replyForm.content.trim()}
                      className="gap-2"
                    >
                      {sendingReply ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <Send className="w-4 h-4" />
                      )}
                      Gửi trả lời
                    </Button>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Quick Actions */}
          {!isReplying && (
            <div className="flex justify-end gap-2 pt-4">
              {message.type === "email" && (
                <Button
                  type="button"
                  variant="outline"
                  className="gap-2 rounded-xl text-blue-600 border-blue-200 hover:bg-blue-50"
                  onClick={handleGmailQuickSend}
                >
                  <AtSign className="w-4 h-4" />
                  <ExternalLink className="w-3 h-3" />
                  Gửi qua Gmail
                </Button>
              )}

              {message.status === "draft" && (
                <Button
                  className="gap-2 ai-button"
                  onClick={() => handleAction("edit")}
                >
                  <Edit className="w-4 h-4" />
                  Chỉnh sửa nháp
                </Button>
              )}
              <Button
                variant="outline"
                className="gap-2"
                onClick={() => handleAction("reply")}
              >
                <Reply className="w-4 h-4" />
                Trả lời
              </Button>
              <Button
                variant="outline"
                className="gap-2"
                onClick={() => handleAction("forward")}
              >
                <Forward className="w-4 h-4" />
                Chuyển tiếp
              </Button>
              <Button
                variant="outline"
                className="gap-2"
                onClick={() => handleAction("archive")}
              >
                <Archive className="w-4 h-4" />
                Lưu trữ
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
