import React, { useState, useMemo } from "react";
import { Layout } from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ProtectedRoute } from "@/lib/auth";
import { useSimplePageTitle } from "@/hooks/usePageTitle";
import { useTranslation } from "@/lib/i18n";
import { toast } from "sonner";
import {
  Users,
  Plus,
  Download,
  Printer,
  X,
  Search,
  Filter,
  BarChart3,
  FileText,
  RefreshCw,
} from "lucide-react";
import { CandidateSelector } from "@/components/candidates/comparison/CandidateSelector";
import { ComparisonView } from "@/components/candidates/comparison/ComparisonView";
import { ComparisonControls } from "@/components/candidates/comparison/ComparisonControls";
import { ComparisonSummary } from "@/components/candidates/comparison/ComparisonSummary";
import { useCandidates } from "@/hooks/useApi";
import { Candidate } from "@/data/mockData";
import { candidateAdapters } from "@/lib/adapters";

interface ComparisonCandidate extends Candidate {
  analysisData?: any;
  isLoading?: boolean;
}

export default function CandidateComparison() {
  const { t } = useTranslation();
  useSimplePageTitle("Candidate Comparison");

  const [selectedCandidates, setSelectedCandidates] = useState<
    ComparisonCandidate[]
  >([]);
  const [isSelectorOpen, setIsSelectorOpen] = useState(false);
  const [comparisonMode, setComparisonMode] = useState<"detailed" | "summary">(
    "detailed",
  );
  const [jobFilterId, setJobFilterId] = useState<string>("all");

  // Fetch candidates for selection
  const { data: candidatesResponse, isLoading } = useCandidates({
    per_page: 100,
    sort: "name",
  });

  const availableCandidates = useMemo(() => {
    //convert to candidateUI data
    return candidatesResponse?.data.map((c: any) => {
      return candidateAdapters.fromApi(c);
    });
  }, [candidatesResponse]);

  const handleAddCandidate = (candidate: Candidate) => {
    if (selectedCandidates.length >= 4) {
      toast.error("Maximum 4 candidates can be compared at once");
      return;
    }

    if (selectedCandidates.some((c) => c.id === candidate.id)) {
      toast.error("Candidate already selected for comparison");
      return;
    }

    setSelectedCandidates((prev) => [
      ...prev,
      { ...candidate, isLoading: true },
    ]);
    setIsSelectorOpen(false);
    toast.success(`${candidate.name} added to comparison`);
  };

  const handleRemoveCandidate = (candidateId: string) => {
    setSelectedCandidates((prev) => prev.filter((c) => c.id !== candidateId));
    toast.success("Candidate removed from comparison");
  };

  const handleClearAll = () => {
    setSelectedCandidates([]);
    toast.success("All candidates removed from comparison");
  };

  const handleExportComparison = async () => {
    try {
      const comparisonData = {
        timestamp: new Date().toISOString(),
        candidates: selectedCandidates.map((c) => ({
          id: c.id,
          name: c.name,
          position: c.position,
          skills: c.skills,
          experience: c.experience,
          rating: c.rating,
          aiScore: c.aiScore,
          analysisData: c.analysisData,
        })),
        jobFilter: jobFilterId,
        mode: comparisonMode,
      };

      const blob = new Blob([JSON.stringify(comparisonData, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `candidate_comparison_${new Date().toISOString().split("T")[0]}.json`;
      a.click();
      URL.revokeObjectURL(url);

      toast.success("Comparison data exported successfully");
    } catch (error) {
      toast.error("Failed to export comparison data");
    }
  };

  return (
    <ProtectedRoute>
      <Layout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
                So sánh ứng viên
              </h1>
              <p className="text-muted-foreground">
                So sánh nhiều ứng viên cạnh nhau để đưa ra quyết định tuyển dụng
                sáng suốt
              </p>
            </div>

            <div className="flex gap-2">
              <Button
                className="ai-button gap-2"
                onClick={() => setIsSelectorOpen(true)}
                disabled={selectedCandidates.length >= 4}
              >
                <Plus className="w-4 h-4" />
                Thêm ứng viên
              </Button>
            </div>
          </div>

          {/* Comparison Controls */}
          <ComparisonControls
            selectedCount={selectedCandidates.length}
            comparisonMode={comparisonMode}
            onModeChange={setComparisonMode}
            jobFilterId={jobFilterId}
            onJobFilterChange={setJobFilterId}
            onClearAll={handleClearAll}
          />

          {/* Main Content */}
          {selectedCandidates.length === 0 ? (
            <Card className="border-dashed">
              <CardContent className="flex flex-col items-center justify-center py-16">
                <Users className="w-16 h-16 text-muted-foreground mb-4" />
                <h3 className="text-xl font-semibold mb-2">
                  Chưa có ứng viên được chọn
                </h3>
                <p className="text-muted-foreground text-center mb-6 max-w-md">
                  Bắt đầu bằng cách thêm các ứng viên để so sánh hồ sơ, kỹ năng
                  và kết quả phân tích AI của họ.
                </p>
                <Button
                  onClick={() => setIsSelectorOpen(true)}
                  className="ai-button gap-2"
                >
                  <Plus className="w-4 h-4" />
                  Chọn ứng viên đầu tien
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {/* Comparison Summary */}
              {selectedCandidates.length > 1 && (
                <ComparisonSummary
                  candidates={selectedCandidates}
                  jobFilterId={jobFilterId}
                />
              )}

              {/* Comparison View */}
              <ComparisonView
                candidates={selectedCandidates}
                mode={comparisonMode}
                jobFilterId={jobFilterId}
                onRemoveCandidate={handleRemoveCandidate}
                onUpdateCandidate={(candidateId, updates) => {
                  setSelectedCandidates((prev) =>
                    prev.map((c) =>
                      c.id === candidateId ? { ...c, ...updates } : c,
                    ),
                  );
                }}
              />
            </div>
          )}

          {/* Candidate Selector Modal */}
          <CandidateSelector
            isOpen={isSelectorOpen}
            onClose={() => setIsSelectorOpen(false)}
            candidates={availableCandidates}
            selectedIds={selectedCandidates.map((c) => c.id)}
            onSelect={handleAddCandidate}
            isLoading={isLoading}
            jobFilterId={jobFilterId}
          />
        </div>
      </Layout>
    </ProtectedRoute>
  );
}
