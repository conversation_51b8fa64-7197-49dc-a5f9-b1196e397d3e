/**
 * Candidate Comparison Page
 * Main page for candidate comparison functionality with selection and comparison views
 */

import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  GitCompare,
  Users,
  Loader2,
  AlertCircle,
  RefreshCw,
  BookOpen,
} from "lucide-react";
import { toast } from "sonner";
import { CandidateSelectionForComparison } from "@/components/candidates/CandidateSelectionForComparison";
import { CandidateComparisonView } from "@/components/candidates/CandidateComparisonView";
import { useCandidates } from "@/hooks/useApi";
import { useCandidateComparison } from "@/hooks/useCandidateComparison";
import { useSimplePageTitle } from "@/hooks/usePageTitle";
import { ProtectedRoute } from "@/lib/auth";
import { useTranslation } from "@/lib/i18n";

export default function CandidateComparison() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  
  // Set page title
  useSimplePageTitle("Candidate Comparison");

  // Get job posting ID from URL params if available
  const jobPostingId = searchParams.get("job_id") || undefined;
  const preSelectedIds = searchParams.get("candidates")?.split(",") || [];

  // State
  const [currentStep, setCurrentStep] = useState<"selection" | "comparison">(
    preSelectedIds.length >= 2 ? "comparison" : "selection"
  );

  // Hooks
  const {
    selectedCandidates,
    comparisonResult,
    isLoading,
    error,
    canCompare,
    runComparison,
    clearSelection,
    exportComparison,
    initializeWithCandidates,
  } = useCandidateComparison({
    jobPostingId,
    includeAnalysis: true,
    includeTimeline: false,
    includeInterviews: true,
  });

  // Fetch candidates data
  const {
    data: candidatesResponse,
    isLoading: isLoadingCandidates,
    error: candidatesError,
    refetch: refetchCandidates,
  } = useCandidates({
    per_page: 100,
    sort: "rating",
    ...(jobPostingId && { filter: { job_posting_id: jobPostingId } }),
  });

  const candidates = candidatesResponse?.data || [];

  // Initialize candidates from URL parameters
  useEffect(() => {
    console.log("CandidateComparison: URL params effect", {
      preSelectedIds,
      selectedCandidatesLength: selectedCandidates.length,
      currentStep
    });

    if (preSelectedIds.length >= 2) {
      // Initialize with pre-selected candidates
      initializeWithCandidates(preSelectedIds);

      if (selectedCandidates.length === 0) {
        console.log("CandidateComparison: Auto-running comparison for pre-selected candidates");
        // Auto-run comparison after state is set
        setTimeout(() => {
          setCurrentStep("comparison");
          runComparison();
        }, 1000);
      }
    }
  }, [preSelectedIds, initializeWithCandidates]);

  // Run comparison when candidates are ready
  useEffect(() => {
    if (currentStep === "comparison" && selectedCandidates.length >= 2 && !comparisonResult && !isLoading) {
      console.log("CandidateComparison: Running comparison for selected candidates");
      runComparison();
    }
  }, [currentStep, selectedCandidates, comparisonResult, isLoading, runComparison]);

  // Update URL when candidates are selected
  useEffect(() => {
    if (selectedCandidates.length > 0) {
      const params = new URLSearchParams(searchParams);
      params.set("candidates", selectedCandidates.join(","));
      setSearchParams(params);
    }
  }, [selectedCandidates, searchParams, setSearchParams]);

  const handleCompareClick = async (candidateIds: string[]) => {
    if (candidateIds.length < 2) {
      toast.error("Please select at least 2 candidates to compare");
      return;
    }

    setCurrentStep("comparison");
    toast.info("Generating comparison analysis...");
    
    try {
      await runComparison();
    } catch (error) {
      console.error("Comparison failed:", error);
      toast.error("Failed to generate comparison. Please try again.");
      setCurrentStep("selection");
    }
  };

  const handleBackToSelection = () => {
    setCurrentStep("selection");
    // Clear comparison result to force fresh data
  };

  const handleStartOver = () => {
    clearSelection();
    setCurrentStep("selection");
    const params = new URLSearchParams(searchParams);
    params.delete("candidates");
    setSearchParams(params);
    toast.info("Selection cleared. Choose new candidates to compare.");
  };

  const handleExportComparison = async (format: "pdf" | "excel" | "json") => {
    await exportComparison(format);
  };

  // Loading state
  if (isLoadingCandidates) {
    return (
      <ProtectedRoute>
        <Layout>
          <div className="flex items-center justify-center h-96">
            <div className="flex items-center gap-2">
              <Loader2 className="w-6 h-6 animate-spin" />
              <span>Loading candidates...</span>
            </div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  // Error state
  if (candidatesError) {
    return (
      <ProtectedRoute>
        <Layout>
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Failed to load candidates</h3>
              <p className="text-muted-foreground mb-4">
                There was an error loading candidate data
              </p>
              <Button onClick={() => refetchCandidates()}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Try again
              </Button>
            </div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <Layout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => navigate(-1)}
                className="gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Back
              </Button>
              
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <GitCompare className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
                    Candidate Comparison
                  </h1>
                  <p className="text-muted-foreground">
                    Compare candidates side-by-side to make informed hiring decisions
                  </p>
                </div>
              </div>
            </div>

            {/* Status indicator */}
            <div className="flex items-center gap-2">
              {jobPostingId && (
                <Badge variant="outline" className="gap-1">
                  <BookOpen className="w-3 h-3" />
                  Job-specific comparison
                </Badge>
              )}
              
              {selectedCandidates.length > 0 && (
                <Badge variant="secondary" className="gap-1">
                  <Users className="w-3 h-3" />
                  {selectedCandidates.length} selected
                </Badge>
              )}

              {currentStep === "comparison" && (
                <Button
                  variant="outline"
                  onClick={handleStartOver}
                  className="gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  Start Over
                </Button>
              )}
            </div>
          </div>

          {/* Progress indicator */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                currentStep === "selection" ? "bg-primary text-primary-foreground" : "bg-green-500 text-white"
              }`}>
                {currentStep === "comparison" ? "✓" : "1"}
              </div>
              <span className={currentStep === "selection" ? "font-medium" : "text-muted-foreground"}>
                Select Candidates
              </span>
            </div>
            
            <div className={`flex-1 h-0.5 ${currentStep === "comparison" ? "bg-green-500" : "bg-border"}`} />
            
            <div className="flex items-center gap-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                currentStep === "comparison" ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
              }`}>
                {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : "2"}
              </div>
              <span className={currentStep === "comparison" ? "font-medium" : "text-muted-foreground"}>
                Compare & Analyze
              </span>
            </div>
          </div>

          {/* Main Content */}
          <div className="min-h-[600px]">
            {currentStep === "selection" && (
              <CandidateSelectionForComparison
                candidates={candidates}
                onCompare={handleCompareClick}
                preSelectedIds={preSelectedIds}
                jobPostingId={jobPostingId}
                maxSelection={5}
              />
            )}

            {currentStep === "comparison" && (
              <div className="space-y-6">
                {isLoading && !comparisonResult && (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <Loader2 className="w-12 h-12 animate-spin mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">Generating Comparison</h3>
                      <p className="text-muted-foreground">
                        Analyzing {selectedCandidates.length} candidates and generating insights...
                      </p>
                      <div className="mt-4 text-sm text-muted-foreground">
                        This may take a few moments as we process AI analysis data
                      </div>
                    </CardContent>
                  </Card>
                )}

                {error && (
                  <Card className="border-red-200 bg-red-50">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-3">
                        <AlertCircle className="w-5 h-5 text-red-600" />
                        <div>
                          <h3 className="font-semibold text-red-800">Comparison Failed</h3>
                          <p className="text-sm text-red-600 mt-1">
                            {error?.message || "An error occurred while generating the comparison"}
                          </p>
                        </div>
                      </div>
                      <div className="mt-4 flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => runComparison()}
                          className="text-red-700 border-red-200"
                        >
                          <RefreshCw className="w-4 h-4 mr-1" />
                          Retry
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handleBackToSelection}
                          className="text-red-700"
                        >
                          Back to Selection
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {comparisonResult && !isLoading && (
                  <div>
                    {/* Back to selection button */}
                    <div className="mb-6">
                      <Button
                        variant="outline"
                        onClick={handleBackToSelection}
                        className="gap-2"
                      >
                        <ArrowLeft className="w-4 h-4" />
                        Back to Selection
                      </Button>
                    </div>

                    <CandidateComparisonView
                      comparisonResult={comparisonResult}
                      onExport={handleExportComparison}
                    />
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Empty state for no candidates */}
          {candidates.length === 0 && currentStep === "selection" && (
            <Card>
              <CardContent className="p-8 text-center">
                <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Candidates Available</h3>
                <p className="text-muted-foreground">
                  {jobPostingId 
                    ? "No candidates found for this job posting. Try selecting a different job or check your filters."
                    : "No candidates found in the system. Add some candidates first to enable comparison."
                  }
                </p>
                <div className="mt-4">
                  <Button
                    variant="outline"
                    onClick={() => navigate("/candidates")}
                    className="gap-2"
                  >
                    <Users className="w-4 h-4" />
                    View All Candidates
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </Layout>
    </ProtectedRoute>
  );
}
