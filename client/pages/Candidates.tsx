import { useState, useMemo, useEffect, useCallback } from "react";
import { Layout } from "@/components/layout/Layout";
import {
  useCandidates,
  useCreateCandidate,
  useUpdateCandidate,
  useDeleteCandidate,
  useJobs,
  useUpdateCandidateStatus,
} from "@/hooks/useApi";
import { candidateAdapters, filterAdapters } from "@/lib/adapters";
import { ProtectedRoute } from "@/lib/auth";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Progress } from "@/components/ui/progress";
import { CandidateDetailModal } from "@/components/candidates/CandidateDetailModal";
import { BulkActionsBar } from "@/components/candidates/BulkActionsBar";
import { AdvancedFilters } from "@/components/candidates/AdvancedFilters";
import { CandidateModal } from "@/components/candidates/EditCandidateModal";
import { ImportCandidatesModal } from "@/components/candidates/ImportCandidatesModal";
import { AIScoreBadge } from "@/components/candidates/AIScoreBadge";
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  MapPin,
  Calendar,
  Star,
  Mail,
  Phone,
  ExternalLink,
  User,
  Download,
  Upload,
  List,
  Table as TableIcon,
  SortAsc,
  SortDesc,
  Eye,
  Send,
  Clock,
  Zap,
  Info,
  Edit,
  Briefcase,
  MessageSquare,
  Video,
  Copy,
  Share2,
  Linkedin,
  Github,
  FileText,
  Building,
} from "lucide-react";
import { Candidate, mockJobs } from "@/data/mockData";
import { formatDistanceToNow } from "date-fns";
import { safeFormatDistanceToNow } from "@/lib/utils";
import { toast } from "sonner";
import { useTranslation } from "@/lib/i18n";
import { cn } from "@/lib/utils";
import { useSimplePageTitle } from "@/hooks/usePageTitle";

export default function Candidates() {
  const { t } = useTranslation();
  const { toast } = useToast();

  // Set page title
  useSimplePageTitle("nav.candidates");

  // Local state
  const [selectedCandidates, setSelectedCandidates] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sourceFilter, setSourceFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("appliedDate");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [viewMode, setViewMode] = useState<"list" | "table">("table");
  const [isAdvancedFiltersOpen, setIsAdvancedFiltersOpen] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(
    null,
  );
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  // API hooks
  const filters = {
    search: searchTerm,
    status: statusFilter === "all" ? "" : statusFilter,
    source: sourceFilter === "all" ? "" : sourceFilter,
  };

  const {
    data: candidatesData,
    isLoading,
    error,
    refetch,
  } = useCandidates({
    page: currentPage,
    ...filterAdapters.candidateFilters(filters),
  });

  const { data: jobsData } = useJobs({ page: 1, per_page: 100 });
  const createCandidateMutation = useCreateCandidate();
  const updateCandidateMutation = useUpdateCandidate();
  const deleteCandidateMutation = useDeleteCandidate();
  const updateStatusMutation = useUpdateCandidateStatus();

  // Transform API data
  const candidates = candidatesData
    ? candidateAdapters.fromPaginatedApi(candidatesData).candidates
    : [];

  // Filter and sort candidates
  const filteredCandidates = useMemo(() => {
    let filtered = candidates.filter((candidate) => {
      const matchesSearch =
        candidate.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        candidate.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        candidate.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
        candidate.skills.some((skill) =>
          skill.toLowerCase().includes(searchTerm.toLowerCase()),
        );

      const matchesStatus =
        statusFilter === "all" || candidate.status === statusFilter;

      const matchesSource =
        sourceFilter === "all" || candidate.source === sourceFilter;

      return matchesSearch && matchesStatus && matchesSource;
    });

    // Sort candidates
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof Candidate];
      let bValue: any = b[sortBy as keyof Candidate];

      if (sortBy === "appliedDate") {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      if (typeof aValue === "string") {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === "asc") {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [candidates, searchTerm, statusFilter, sourceFilter, sortBy, sortOrder]);

  // Enhanced handler functions
  const handleCreateCandidate = async (candidateData: any) => {
    try {
      const apiCandidateData = candidateAdapters.toApi(candidateData);
      await createCandidateMutation.mutateAsync(apiCandidateData);
      toast({
        title: "Thành công",
        description: `Ứng viên ${candidateData.name} đã được tạo thành công`,
      });
      setIsAddModalOpen(false);
      refetch();
    } catch (error: any) {
      throw error;
    }
  };

  const handleUpdateCandidate = async (candidateData: any) => {
    if (!selectedCandidate) return;

    try {
      const apiCandidateData = candidateAdapters.toApi(candidateData);
      await updateCandidateMutation.mutateAsync({
        id: selectedCandidate.id,
        data: apiCandidateData,
      });
      toast({
        title: "Thành công",
        description: `Ứng viên ${candidateData.name} đã được cập nhật thành công`,
      });
      setIsEditModalOpen(false);
      setSelectedCandidate(null);
      refetch();
    } catch (error: any) {
      throw error;
    }
  };

  const handleEditCandidate = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setIsDetailModalOpen(false);
    setIsEditModalOpen(true);
  };

  const handleDeleteCandidate = async (candidateId: string) => {
    try {
      await deleteCandidateMutation.mutateAsync(candidateId);
      toast({
        title: "Success",
        description: "Candidate deleted successfully",
      });
      setSelectedCandidates(
        selectedCandidates.filter((id) => id !== candidateId),
      );
      refetch();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete candidate",
        variant: "destructive",
      });
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedCandidates.length === 0) return;

    try {
      switch (action) {
        case "email":
          toast({
            title: "📧 Bulk Email",
            description: `Composing email for ${selectedCandidates.length} candidates`,
          });
          break;
        case "export":
          toast({
            title: "📁 Bulk Export",
            description: `Exporting ${selectedCandidates.length} candidates`,
          });
          break;
        case "move-to-screening":
          await Promise.all(
            selectedCandidates.map((candidateId) =>
              updateStatusMutation.mutateAsync({
                id: candidateId,
                status: "screening",
                notes: "Moved to screening via bulk action",
              }),
            ),
          );
          setSelectedCandidates([]);
          toast({
            title: "Success",
            description: `Moved ${selectedCandidates.length} candidates to screening`,
          });
          break;
        case "move-to-interview":
          await Promise.all(
            selectedCandidates.map((candidateId) =>
              updateStatusMutation.mutateAsync({
                id: candidateId,
                status: "interview",
                notes: "Moved to interview via bulk action",
              }),
            ),
          );
          setSelectedCandidates([]);
          toast({
            title: "Success",
            description: `Moved ${selectedCandidates.length} candidates to interview`,
          });
          break;
        case "reject":
          await Promise.all(
            selectedCandidates.map((candidateId) =>
              updateStatusMutation.mutateAsync({
                id: candidateId,
                status: "rejected",
                notes: "Rejected via bulk action",
              }),
            ),
          );
          setSelectedCandidates([]);
          toast({
            title: "Success",
            description: `Rejected ${selectedCandidates.length} candidates`,
          });
          break;
        case "delete":
          await Promise.all(
            selectedCandidates.map((id) =>
              deleteCandidateMutation.mutateAsync(id),
            ),
          );
          toast({
            title: "Success",
            description: `${selectedCandidates.length} candidates deleted successfully`,
          });
          setSelectedCandidates([]);
          refetch();
          break;
        default:
          toast({
            title: "Action Not Implemented",
            description: `${action} functionality will be available soon`,
          });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to perform ${action} action`,
        variant: "destructive",
      });
      console.error(`Bulk ${action} error:`, error);
    }
  };

  // Enhanced action handlers
  const handleSendEmail = (candidate: Candidate) => {
    if (!candidate) return;
    toast({
      title: `📧 Composing email for ${candidate.name}`,
      description: "Opening email composer...",
    });
  };

  const handleScheduleInterview = (candidate: Candidate) => {
    if (!candidate) return;
    toast({
      title: `📅 Scheduling interview with ${candidate.name}`,
      description: "Opening calendar scheduler...",
    });
  };

  const handleVideoCall = (candidate: Candidate) => {
    if (!candidate) return;
    toast({
      title: `📹 Starting video call with ${candidate.name}`,
      description: "Connecting to video platform...",
    });
  };

  const handleDownloadResume = (candidate: Candidate) => {
    if (!candidate) return;
    toast({
      title: `📄 Downloading ${candidate.name}'s resume`,
      description: "Resume download started...",
    });
  };

  const handleViewLinkedIn = (candidate: Candidate) => {
    if (!candidate) return;
    if (candidate.linkedinUrl) {
      window.open(candidate.linkedinUrl, "_blank");
    } else {
      toast({
        title: "LinkedIn profile not available",
        description: `No LinkedIn URL found for ${candidate.name}`,
        variant: "destructive",
      });
    }
  };

  const handleCopyEmail = (candidate: Candidate) => {
    if (!candidate) return;
    navigator.clipboard.writeText(candidate.email);
    toast({
      title: "📋 Email copied",
      description: `${candidate.email} copied to clipboard`,
    });
  };

  const handleShareProfile = (candidate: Candidate) => {
    if (!candidate) return;
    toast({
      title: `🔗 Sharing ${candidate.name}'s profile`,
      description: "Profile link copied to clipboard",
    });
  };

  const getJobTitle = (jobId: string) => {
    const job = mockJobs.find((j) => j.id === jobId);
    return job ? job.title : "Unknown Position";
  };

  const getStatusColor = (status: Candidate["status"]) => {
    switch (status) {
      case "sourced":
        return "bg-slate-100 text-slate-700 border-slate-200";
      case "applied":
        return "bg-blue-100 text-blue-700 border-blue-200";
      case "screening":
        return "bg-amber-100 text-amber-700 border-amber-200";
      case "interview":
        return "bg-violet-100 text-violet-700 border-violet-200";
      case "offer":
        return "bg-emerald-100 text-emerald-700 border-emerald-200";
      case "hired":
        return "bg-green-100 text-green-700 border-green-200";
      case "rejected":
        return "bg-red-100 text-red-700 border-red-200";
      default:
        return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };
  const getStatusText = (status: Candidate["status"]) => {
    switch (status) {
      case "sourced":
        return "Đã tìm hiểu";
      case "applied":
        return "Ứng_tuyển";
      case "screening":
        return "Sàng lọc";
      case "interview":
        return "Phỏng vấn";
      case "offer":
        return "Đề nghị";
      case "hired":
        return "Đã tuyển";
      case "rejected":
        return "Từ chối";
      default:
        return status;
    }
  };

  const handleSelectCandidate = (candidateId: string) => {
    setSelectedCandidates((prev) =>
      prev.includes(candidateId)
        ? prev.filter((id) => id !== candidateId)
        : [...prev, candidateId],
    );
  };

  const handleSelectAll = () => {
    if (selectedCandidates.length === filteredCandidates.length) {
      setSelectedCandidates([]);
    } else {
      setSelectedCandidates(filteredCandidates.map((c) => c.id));
    }
  };

  const handleViewCandidate = (candidate: Candidate) => {
    if (!candidate) return;
    setSelectedCandidate(candidate);
    setIsDetailModalOpen(true);
  };

  const calculateStats = () => {
    const totalCandidates = candidates.length;
    const topRated = candidates.filter(
      (c) => c.rating && c.rating >= 4.5,
    ).length;
    const newApplications = candidates.filter(
      (c) =>
        new Date(c.appliedDate) >
        new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    ).length;
    const interviewScheduled = candidates.filter(
      (c) => c.status === "interview",
    ).length;

    return { totalCandidates, topRated, newApplications, interviewScheduled };
  };

  const stats = calculateStats();

  // Enhanced List View Component
  const EnhancedCandidateListCard = ({
    candidate,
    index,
  }: {
    candidate: Candidate;
    index: number;
  }) => {
    const isSelected = selectedCandidates.includes(candidate.id);

    return (
      <Card
        className={cn(
          "group hover:shadow-lg transition-all duration-200 cursor-pointer border-l-4",
          isSelected
            ? "border-l-primary bg-primary/5 shadow-md"
            : "border-l-transparent hover:border-l-primary/50",
          "hover:bg-muted/30",
        )}
        onDoubleClick={() => handleViewCandidate(candidate)}
      >
        <CardContent className="p-6">
          <div className="flex items-center gap-6">
            {/* Selection and Avatar */}
            <div className="flex items-center gap-4">
              <Checkbox
                checked={isSelected}
                onCheckedChange={() => handleSelectCandidate(candidate.id)}
                onClick={(e) => e.stopPropagation()}
                className="transition-all duration-200 hover:scale-110"
              />
              <div className="relative">
                <Avatar className="h-16 w-16 ring-2 ring-border shadow-lg group-hover:ring-primary/30 transition-all">
                  <AvatarImage src={candidate.avatar} />
                  <AvatarFallback className="bg-gradient-to-br from-primary/20 to-secondary/20 text-primary text-lg font-semibold">
                    {candidate.initials}
                  </AvatarFallback>
                </Avatar>
                {/* Status indicator */}
                <div
                  className={cn(
                    "absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-2 border-background shadow-md",
                    candidate.status === "hired"
                      ? "bg-green-500"
                      : candidate.status === "interview"
                        ? "bg-purple-500"
                        : candidate.status === "offer"
                          ? "bg-emerald-500"
                          : candidate.status === "screening"
                            ? "bg-yellow-500"
                            : candidate.status === "applied"
                              ? "bg-blue-500"
                              : "bg-gray-400",
                  )}
                />
              </div>
            </div>

            {/* Main Information Grid */}
            <div className="flex-1 grid grid-cols-1 lg:grid-cols-5 gap-6">
              {/* Name and Position */}
              <div className="space-y-1 lg:col-span-1">
                <div className="flex items-center gap-2">
                  <h3 className="font-bold text-lg text-foreground group-hover:text-primary transition-colors">
                    {candidate.name}
                  </h3>
                </div>
                <p className="text-muted-foreground font-medium">
                  {candidate.position}
                </p>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Briefcase className="h-4 w-4" />
                  <span>{candidate.experience}</span>
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-2 lg:col-span-1">
                <div
                  className="flex items-center gap-2 text-sm group/item hover:text-primary cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCopyEmail(candidate);
                  }}
                >
                  <Mail className="h-4 w-4 text-muted-foreground group-hover/item:text-primary" />
                  <span className="truncate hover:text-clip">
                    {candidate.email}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span>{candidate.phone}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span>{candidate.location}</span>
                </div>
              </div>

              {/* Skills and Source */}
              <div className="space-y-3 lg:col-span-1">
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    Skills
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {candidate.skills.slice(0, 3).map((skill, idx) => (
                      <Badge
                        key={skill}
                        variant="secondary"
                        className={cn(
                          "text-xs",
                          idx === 0
                            ? "bg-primary/10 text-primary border-primary/20"
                            : "",
                        )}
                      >
                        {skill}
                      </Badge>
                    ))}
                    {candidate.skills.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{candidate.skills.length - 3}
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="text-sm">
                  <span className="text-muted-foreground">Source:</span>
                  <span className="ml-1 font-medium">{candidate.source}</span>
                </div>
              </div>

              {/* Progress and Analytics */}
              <div className="space-y-3 lg:col-span-1">
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-muted-foreground">
                      Match Score
                    </span>
                  </div>
                </div>
                <AIScoreBadge candidate={candidate} />
                <div className="text-xs text-muted-foreground">
                  Applied{" "}
                  {safeFormatDistanceToNow(candidate.appliedDate, {
                    addSuffix: true,
                  })}
                </div>
              </div>

              {/* Status and Actions */}
              <div className="flex flex-col justify-between lg:col-span-1">
                <div className="space-y-2">
                  <Badge className={getStatusColor(candidate.status)}>
                    {candidate.status}
                  </Badge>

                  {/* Quick metrics */}
                  <div className="flex gap-2">
                    <div className="text-center bg-muted/50 rounded-lg p-2 min-w-[3rem]">
                      <div className="text-xs text-muted-foreground">Views</div>
                      <div className="font-bold text-sm">12</div>
                    </div>
                    <div className="text-center bg-muted/50 rounded-lg p-2 min-w-[3rem]">
                      <div className="text-xs text-muted-foreground">Score</div>
                      <div className="font-bold text-sm text-primary">A+</div>
                    </div>
                  </div>
                </div>

                {/* Enhanced Quick Actions */}
                <div className="flex gap-2 mt-4">
                  <Button
                    size="sm"
                    variant="outline"
                    className="flex-1 hover:bg-primary hover:text-primary-foreground transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewCandidate(candidate);
                    }}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditCandidate(candidate);
                    }}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Table View Component
  const CandidateTableView = () => (
    <Card>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[20px]">
              <Checkbox
                checked={
                  selectedCandidates.length === filteredCandidates.length
                }
                onCheckedChange={handleSelectAll}
              />
            </TableHead>
            <TableHead>Ứng viên</TableHead>
            <TableHead>Vị trí</TableHead>
            <TableHead>Trạng thái</TableHead>
            <TableHead>K/N</TableHead>
            <TableHead>Skills</TableHead>
            <TableHead>Ngày</TableHead>
            <TableHead></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredCandidates.map((candidate) => (
            <TableRow
              key={candidate.id}
              className="hover:bg-muted/50 cursor-pointer"
              onDoubleClick={() => handleViewCandidate(candidate)}
            >
              <TableCell>
                <Checkbox
                  checked={selectedCandidates.includes(candidate.id)}
                  onCheckedChange={() => handleSelectCandidate(candidate.id)}
                  onClick={(e) => e.stopPropagation()}
                />
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={candidate.avatar} />
                      <AvatarFallback>{candidate.initials}</AvatarFallback>
                    </Avatar>
                    <div
                      className={cn(
                        "absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-background",
                        candidate.status === "hired"
                          ? "bg-green-500"
                          : candidate.status === "interview"
                            ? "bg-purple-500"
                            : candidate.status === "offer"
                              ? "bg-emerald-500"
                              : candidate.status === "screening"
                                ? "bg-yellow-500"
                                : candidate.status === "applied"
                                  ? "bg-blue-500"
                                  : "bg-gray-400",
                      )}
                    />
                  </div>
                  <div>
                    <div className="font-medium">{candidate.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {candidate.email}
                    </div>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div>
                  <div className="font-medium">{candidate.position}</div>
                  <div className="text-sm text-muted-foreground">
                    {candidate.location}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div className="text-sm text-muted-foreground">
                  <Badge className={getStatusColor(candidate.status)}>
                    {getStatusText(candidate.status)}
                  </Badge>
                </div>
              </TableCell>
              <TableCell>{candidate.experience}</TableCell>
              <TableCell>
                <div className="flex flex-wrap gap-1">
                  {candidate.skills.slice(0, 2).map((skill) => (
                    <Badge key={skill} variant="outline" className="text-xs">
                      {skill}
                    </Badge>
                  ))}
                  {candidate.skills.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{candidate.skills.length - 2}
                    </Badge>
                  )}
                </div>
              </TableCell>

              <TableCell>
                <div className="text-sm">
                  {safeFormatDistanceToNow(candidate.appliedDate, {
                    addSuffix: true,
                  })}
                </div>
              </TableCell>
              <TableCell>
                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewCandidate(candidate);
                    }}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditCandidate(candidate);
                    }}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button size="sm" variant="ghost" className="hidden">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      <DropdownMenuItem
                        className="gap-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSendEmail(candidate);
                        }}
                      >
                        <Mail className="h-4 w-4" />
                        Send Email
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="gap-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleScheduleInterview(candidate);
                        }}
                      >
                        <Calendar className="h-4 w-4" />
                        Schedule Interview
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="gap-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewLinkedIn(candidate);
                        }}
                      >
                        <Linkedin className="h-4 w-4" />
                        LinkedIn
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="gap-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDownloadResume(candidate);
                        }}
                      >
                        <FileText className="h-4 w-4" />
                        Resume
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Card>
  );

  return (
    <ProtectedRoute>
      <Layout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
                {t.candidates?.title || "Candidates"}
              </h1>
              <p className="text-muted-foreground">
                {t.candidates?.subtitle || "Manage your candidate database"}
              </p>
            </div>
            <div className="flex gap-2">
              <Button
                className="ai-button gap-2"
                onClick={() => setIsAddModalOpen(true)}
              >
                <Plus className="w-4 h-4" />
                {t.candidates?.addCandidate || "Add Candidate"}
              </Button>
            </div>
          </div>

          {/* Enhanced Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-emerald-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/20 rounded-xl">
                    <User className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {t.candidates?.totalCandidates || "Total Candidates"}
                    </p>
                    <p className="text-2xl font-bold">
                      {stats.totalCandidates}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="border-yellow-500/20 bg-gradient-to-br from-yellow-500/5 to-orange-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-yellow-500/20 rounded-xl">
                    <Star className="h-5 w-5 text-yellow-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {t.candidates?.topRated || "Top Rated"}
                    </p>
                    <p className="text-2xl font-bold">{stats.topRated}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="border-blue-500/20 bg-gradient-to-br from-blue-500/5 to-indigo-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-500/20 rounded-xl">
                    <Zap className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {t.candidates?.newThisWeek || "New This Week"}
                    </p>
                    <p className="text-2xl font-bold">
                      {stats.newApplications}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="border-purple-500/20 bg-gradient-to-br from-purple-500/5 to-pink-500/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-500/20 rounded-xl">
                    <Clock className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {t.candidates?.interviewReady || "Interview Ready"}
                    </p>
                    <p className="text-2xl font-bold">
                      {stats.interviewScheduled}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                id="search-input"
                placeholder="🔍 Tìm kiếm ứng viên theo tên, kỹ năng, email, vị trí..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 rounded-xl"
              />
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px] rounded-xl">
                  <SelectValue placeholder={t.common?.status || "Status"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    All {t.common?.status || "Status"}
                  </SelectItem>
                  <SelectItem value="sourced">
                    {t.status?.sourced || "Sourced"}
                  </SelectItem>
                  <SelectItem value="applied">
                    {t.status?.applied || "Applied"}
                  </SelectItem>
                  <SelectItem value="screening">
                    {t.status?.screening || "Screening"}
                  </SelectItem>
                  <SelectItem value="interview">
                    {t.status?.interview || "Interview"}
                  </SelectItem>
                  <SelectItem value="offer">
                    {t.status?.offer || "Offer"}
                  </SelectItem>
                  <SelectItem value="hired">
                    {t.status?.hired || "Hired"}
                  </SelectItem>
                  <SelectItem value="rejected">
                    {t.status?.rejected || "Rejected"}
                  </SelectItem>
                </SelectContent>
              </Select>
              <Select value={sourceFilter} onValueChange={setSourceFilter}>
                <SelectTrigger className="w-[150px] rounded-xl">
                  <SelectValue placeholder="Source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sources</SelectItem>
                  <SelectItem value="LinkedIn">LinkedIn</SelectItem>
                  <SelectItem value="Indeed">Indeed</SelectItem>
                  <SelectItem value="Company Website">
                    Company Website
                  </SelectItem>
                  <SelectItem value="Referral">Referral</SelectItem>
                  <SelectItem value="AngelList">AngelList</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                className="gap-2 rounded-xl hidden"
                onClick={() => setIsAdvancedFiltersOpen(!isAdvancedFiltersOpen)}
              >
                <Filter className="w-4 h-4" />
                Filters
              </Button>
              {/* View Controls */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  {selectedCandidates.length > 0 && (
                    <div className="flex items-center gap-2">
                      <Checkbox
                        checked={
                          selectedCandidates.length ===
                          filteredCandidates.length
                        }
                        onCheckedChange={handleSelectAll}
                      />
                      <span className="text-sm text-muted-foreground">
                        {selectedCandidates.length} of{" "}
                        {filteredCandidates.length} selected
                      </span>
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-4">
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-[150px] rounded-xl">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="name">Name</SelectItem>
                      <SelectItem value="appliedDate">Applied Date</SelectItem>
                      <SelectItem value="status">Status</SelectItem>
                      <SelectItem value="rating">Rating</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                    }
                    className="rounded-xl"
                  >
                    {sortOrder === "asc" ? (
                      <SortAsc className="w-4 h-4" />
                    ) : (
                      <SortDesc className="w-4 h-4" />
                    )}
                  </Button>
                  <div className="border rounded-xl p-1 flex">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant={viewMode === "list" ? "default" : "ghost"}
                            size="sm"
                            onClick={() => setViewMode("list")}
                            className="rounded-lg gap-2"
                          >
                            <List className="w-4 h-4" />
                            <span className="hidden sm:inline">List</span>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>List view - Detailed information</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant={viewMode === "table" ? "default" : "ghost"}
                            size="sm"
                            onClick={() => setViewMode("table")}
                            className="rounded-lg gap-2"
                          >
                            <TableIcon className="w-4 h-4" />
                            <span className="hidden sm:inline">Table</span>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Table view - Compact data view</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedCandidates.length > 0 && (
            <BulkActionsBar
              selectedCount={selectedCandidates.length}
              onBulkAction={handleBulkAction}
              onClearSelection={() => setSelectedCandidates([])}
            />
          )}

          {/* Advanced Filters */}
          {isAdvancedFiltersOpen && (
            <AdvancedFilters
              onFiltersChange={(filters) => {
                // Apply the filters
                console.log("Filters changed:", filters);
                toast({
                  title: "Filters Applied",
                  description: "Advanced filters applied",
                });
              }}
              activeFiltersCount={0}
            />
          )}

          {/* Candidates Display */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold">
                {filteredCandidates.length} Candidates
              </h2>
            </div>

            {viewMode === "list" ? (
              <div className="space-y-4">
                {filteredCandidates.map((candidate, index) => (
                  <EnhancedCandidateListCard
                    key={candidate.id}
                    candidate={candidate}
                    index={index}
                  />
                ))}
              </div>
            ) : (
              <CandidateTableView />
            )}

            {/* Loading States */}
            {isLoading && (
              <div className="space-y-4">
                {viewMode === "list" ? (
                  <div className="space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <Card key={i} className="animate-pulse">
                        <CardContent className="p-6">
                          <div className="flex items-center gap-6">
                            <div className="h-16 w-16 bg-muted rounded-full"></div>
                            <div className="flex-1 space-y-3">
                              <div className="h-4 bg-muted rounded w-1/4"></div>
                              <div className="h-3 bg-muted rounded w-1/3"></div>
                              <div className="h-3 bg-muted rounded w-1/2"></div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Card>
                    <div className="p-6">
                      <div className="space-y-4">
                        {[...Array(8)].map((_, i) => (
                          <div
                            key={i}
                            className="flex items-center gap-4 animate-pulse"
                          >
                            <div className="h-10 w-10 bg-muted rounded-full"></div>
                            <div className="flex-1 space-y-2">
                              <div className="h-4 bg-muted rounded w-1/4"></div>
                              <div className="h-3 bg-muted rounded w-1/3"></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </Card>
                )}
              </div>
            )}

            {/* Empty State */}
            {filteredCandidates.length === 0 && !isLoading && (
              <Card className="p-12 text-center border-dashed border-2">
                <div className="flex flex-col items-center gap-6">
                  <div className="p-6 bg-muted/50 rounded-full">
                    <User className="w-12 h-12 text-muted-foreground" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold">
                      {searchTerm ||
                      statusFilter !== "all" ||
                      sourceFilter !== "all"
                        ? "No candidates match your filters"
                        : "No candidates yet"}
                    </h3>
                    <p className="text-muted-foreground max-w-sm mx-auto">
                      {searchTerm ||
                      statusFilter !== "all" ||
                      sourceFilter !== "all"
                        ? "Try adjusting your search criteria or filters to find candidates"
                        : "Get started by adding your first candidate to the system"}
                    </p>
                  </div>
                  <div className="flex gap-3">
                    {(searchTerm ||
                      statusFilter !== "all" ||
                      sourceFilter !== "all") && (
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSearchTerm("");
                          setStatusFilter("all");
                          setSourceFilter("all");
                        }}
                      >
                        Clear Filters
                      </Button>
                    )}
                    <Button
                      className="ai-button gap-2"
                      onClick={() => setIsAddModalOpen(true)}
                    >
                      <Plus className="w-4 h-4" />
                      {candidates.length === 0
                        ? "Add First Candidate"
                        : "Add Candidate"}
                    </Button>
                  </div>
                </div>
              </Card>
            )}
          </div>
        </div>

        {/* Modals */}
        <CandidateDetailModal
          candidate={selectedCandidate}
          isOpen={isDetailModalOpen}
          onClose={() => {
            setIsDetailModalOpen(false);
            setSelectedCandidate(null);
          }}
          onEdit={handleEditCandidate}
        />

        <CandidateModal
          mode="add"
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          onCreate={handleCreateCandidate}
        />

        <CandidateModal
          mode="edit"
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedCandidate(null);
          }}
          onUpdate={handleUpdateCandidate}
          candidate={selectedCandidate}
        />
      </Layout>
    </ProtectedRoute>
  );
}
