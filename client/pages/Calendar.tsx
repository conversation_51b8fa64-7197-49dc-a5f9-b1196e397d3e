import { useState, useMemo, useEffect } from "react";
import { Layout } from "@/components/layout/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MonthlyCalendar } from "@/components/calendar/MonthlyCalendar";
import { InterviewListView } from "@/components/calendar/InterviewListView";
import { ViewToggle } from "@/components/calendar/ViewToggle";
import { CalendarSummaryCard } from "@/components/calendar/CalendarSummaryCard";
import { EnhancedInterviewDetailModal } from "@/components/calendar/EnhancedInterviewDetailModal";
import { EditInterviewModal } from "@/components/calendar/EditInterviewModal";
import { Plus, AlertTriangle, Loader2, RefreshCw } from "lucide-react";
import { Interview } from "@/data/mockData";
import { toast } from "sonner";
import { useTranslation } from "@/lib/i18n";
import { ProtectedRoute } from "@/lib/auth";
import { useSimplePageTitle } from "@/hooks/usePageTitle";
import {
  useInterviews,
  useScheduleInterview,
  useUpdateInterviewStatus,
} from "@/hooks/useApi";
import { useQueryClient } from "@tanstack/react-query";

// Convert API interview data to Calendar format with enhanced data
const convertApiInterviewToCalendarFormat = (apiInterview: any): Interview => {
  const converted = {
    id: apiInterview.id?.toString() || `interview-${Date.now()}`,
    candidateId: apiInterview.candidate_id?.toString() || "",
    candidateName:
      apiInterview.candidate?.name ||
      apiInterview.candidate_name ||
      "Unknown Candidate",
    candidateEmail:
      apiInterview.candidate?.email || apiInterview.candidate_email || "",
    candidatePhone:
      apiInterview.candidate?.phone || apiInterview.candidate_phone || "",
    candidatePosition:
      apiInterview.candidate?.position || apiInterview.candidate_position || "",
    candidateRating:
      apiInterview.candidate?.rating || apiInterview.candidate_rating || 0,
    candidateAiScore:
      apiInterview.candidate?.ai_score || apiInterview.candidate_ai_score || 0,
    jobId: apiInterview.job_posting_id?.toString() || "",
    jobTitle:
      apiInterview.job_posting?.title ||
      apiInterview.job_title ||
      "Unknown Position",
    jobDepartment:
      apiInterview.job_posting?.department || apiInterview.job_department || "",
    jobLocation:
      apiInterview.job_posting?.location || apiInterview.job_location || "",
    jobSalary: apiInterview.job_posting?.salary_range || "",
    jobPriority:
      apiInterview.job_posting?.priority ||
      apiInterview.job_priority ||
      "medium",
    date: apiInterview.scheduled_at
      ? apiInterview.scheduled_at.split("T")[0]
      : apiInterview.date || new Date().toISOString().split("T")[0],
    time: apiInterview.scheduled_at
      ? new Date(apiInterview.scheduled_at).toLocaleTimeString("en-GB", {
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        })
      : apiInterview.time || "09:00",
    duration: apiInterview.duration || 60,
    type: mapInterviewType(apiInterview.type),
    interviewer:
      apiInterview.interviewer?.name ||
      apiInterview.interviewer?.user?.name ||
      apiInterview.interviewer_name ||
      "Unknown Interviewer",
    interviewerId: apiInterview.interviewer_id?.toString() || "",
    interviewerDepartment:
      apiInterview.interviewer?.department ||
      apiInterview.interviewer?.user?.department ||
      "",
    interviewerExpertise: Array.isArray(apiInterview.interviewer?.expertise)
      ? apiInterview.interviewer.expertise
      : [],
    status: mapInterviewStatus(apiInterview.status),
    location: apiInterview.location || "",
    notes: apiInterview.notes || "",
    meetingLink: apiInterview.meeting_link || "",
    meetingPassword: apiInterview.meeting_password || "",
    agenda: Array.isArray(apiInterview.agenda) ? apiInterview.agenda : [],
    feedback: apiInterview.feedback || "",
    round: apiInterview.round || 1,
    interviewType: apiInterview.interview_type || "technical",
    reminderSent: apiInterview.reminder_sent || false,
    apiData: apiInterview, // Keep original API data for reference
  };
  console.log("Converted interview:", converted); // Debug log

  // Validate critical fields
  if (!converted.date) {
    console.error("❌ Converted interview missing date field:", converted);
  }
  if (!converted.id) {
    console.error("❌ Converted interview missing id field:", converted);
  }

  console.log("Final converted date:", converted.date);
  return converted;
};

const mapInterviewType = (apiType: string): Interview["type"] => {
  switch (apiType?.toLowerCase()) {
    case "video":
    case "online":
      return "video";
    case "phone":
    case "call":
      return "phone";
    case "onsite":
    case "in-person":
    case "offline":
      return "in-person";
    default:
      return "video";
  }
};

const mapInterviewStatus = (apiStatus: string): Interview["status"] => {
  switch (apiStatus?.toLowerCase()) {
    case "scheduled":
      return "scheduled";
    case "completed":
    case "finished":
      return "completed";
    case "cancelled":
    case "canceled":
      return "cancelled";
    case "rescheduled":
    case "postponed":
      return "rescheduled";
    case "in_progress":
    case "ongoing":
      return "scheduled"; // Treat as scheduled for calendar purposes
    default:
      return "scheduled";
  }
};

type ViewMode = "calendar" | "list";

export default function Calendar() {
  const { t } = useTranslation();

  // Set page title
  //useSimplePageTitle("pageTitle.interviews.calendar");
  useSimplePageTitle("nav.calendar");
  const queryClient = useQueryClient();

  // View mode state with localStorage persistence
  const [viewMode, setViewMode] = useState<ViewMode>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("calendar-view-mode");
      return (saved as ViewMode) || "list";
    }
    return "calendar";
  });

  const [selectedDate, setSelectedDate] = useState(new Date());
  const [filterType, setFilterType] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterInterviewer, setFilterInterviewer] = useState<string>("all");
  const [interviews, setInterviews] = useState<Interview[]>([]);
  const [selectedInterview, setSelectedInterview] = useState<Interview | null>(
    null,
  );
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isInterviewModalOpen, setIsInterviewModalOpen] = useState(false);
  const [editingInterview, setEditingInterview] = useState<Interview | null>(
    null,
  );

  // Persist view mode to localStorage
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("calendar-view-mode", viewMode);
    }
  }, [viewMode]);

  // Calculate date range for API query (current month +/- 1 month for better view)
  const dateRange = useMemo(() => {
    const startDate = new Date(
      selectedDate.getFullYear(),
      selectedDate.getMonth() - 1,
      1,
    );
    const endDate = new Date(
      selectedDate.getFullYear(),
      selectedDate.getMonth() + 2,
      0,
    );
    return {
      date_from: startDate.toISOString().split("T")[0],
      date_to: endDate.toISOString().split("T")[0],
    };
  }, [selectedDate]);

  // Debug auth token
  const authToken =
    typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;
  console.log("Auth token exists:", !!authToken); // Debug log

  // API Hooks with enhanced parameters
  const {
    data: interviewsResponse,
    isLoading: isLoadingInterviews,
    error: interviewsError,
    refetch: refetchInterviews,
  } = useInterviews({
    per_page: 200, // Get more interviews for calendar view
    sort: "date", // Use the correct field from Laravel API
    direction: "desc", // Most recent first
    date_from: dateRange.date_from,
    date_to: dateRange.date_to,
    // Add status filter if needed
    ...(filterStatus !== "all" && { status: filterStatus }),
    // Add type filter if needed
    ...(filterType !== "all" && { type: filterType }),
  });

  const scheduleInterviewMutation = useScheduleInterview();
  const updateInterviewStatusMutation = useUpdateInterviewStatus();

  // Update interviews when API data changes
  useEffect(() => {
    if (
      interviewsResponse?.status === "success" &&
      interviewsResponse?.data &&
      Array.isArray(interviewsResponse.data)
    ) {
      const convertedInterviews = interviewsResponse.data.map(
        convertApiInterviewToCalendarFormat,
      );
      setInterviews(convertedInterviews);
    }
    // Fallback for paginated response format
    else if (
      interviewsResponse?.data &&
      Array.isArray(interviewsResponse.data)
    ) {
      const convertedInterviews = interviewsResponse.data.map(
        convertApiInterviewToCalendarFormat,
      );
      setInterviews(convertedInterviews);
    }
    // Legacy format fallback
    else if (
      interviewsResponse?.interviews &&
      Array.isArray(interviewsResponse.interviews)
    ) {
      const convertedInterviews = interviewsResponse.interviews.map(
        convertApiInterviewToCalendarFormat,
      );

      setInterviews(convertedInterviews);
    } else if (interviewsResponse) {
      setInterviews([]);
    }
  }, [interviewsResponse]);

  // Log API errors for debugging
  useEffect(() => {
    if (interviewsError) {
      console.error("API error:", interviewsError);
    }
  }, [interviewsError]);

  // Filter interviews based on current filters
  const filteredInterviews = useMemo(() => {
    const filtered = interviews.filter((interview) => {
      const matchesType = filterType === "all" || interview.type === filterType;
      const matchesStatus =
        filterStatus === "all" || interview.status === filterStatus;
      const matchesInterviewer =
        filterInterviewer === "all" ||
        interview.interviewer === filterInterviewer;

      const passes = matchesType && matchesStatus && matchesInterviewer;
      console.log(
        `Interview ${interview.id}: type=${interview.type} status=${interview.status} interviewer=${interview.interviewer} passes=${passes}`,
      );

      return passes;
    });

    return filtered;
  }, [interviews, filterType, filterStatus, filterInterviewer]);

  // Calculate calendar statistics
  const calendarStats = useMemo(() => {
    const currentMonth = new Date(
      selectedDate.getFullYear(),
      selectedDate.getMonth(),
      1,
    );
    const nextMonth = new Date(
      selectedDate.getFullYear(),
      selectedDate.getMonth() + 1,
      1,
    );

    const monthInterviews = filteredInterviews.filter((interview) => {
      const interviewDate = new Date(interview.date);
      return interviewDate >= currentMonth && interviewDate < nextMonth;
    });

    const today = new Date().toISOString().split("T")[0];
    const todayInterviews = filteredInterviews.filter(
      (interview) => interview.date === today,
    );

    const statusCounts = monthInterviews.reduce(
      (acc, interview) => {
        acc[interview.status] = (acc[interview.status] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const typeCounts = monthInterviews.reduce(
      (acc, interview) => {
        acc[interview.type] = (acc[interview.type] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    return {
      total: monthInterviews.length,
      today: todayInterviews.length,
      scheduled: statusCounts.scheduled || 0,
      completed: statusCounts.completed || 0,
      cancelled: statusCounts.cancelled || 0,
      rescheduled: statusCounts.rescheduled || 0,
      video: typeCounts.video || 0,
      phone: typeCounts.phone || 0,
      inPerson: typeCounts["in-person"] || 0,
      completionRate:
        monthInterviews.length > 0
          ? (
              ((statusCounts.completed || 0) / monthInterviews.length) *
              100
            ).toFixed(1)
          : "0",
    };
  }, [filteredInterviews, selectedDate]);

  const handleInterviewClick = (interview: Interview) => {
    setSelectedInterview(interview);
    setIsDetailModalOpen(true);
  };

  const handleInterviewEdit = (interview: Interview) => {
    setEditingInterview(interview);
    setIsInterviewModalOpen(true);
    setIsDetailModalOpen(false);
  };

  const handleEditSuccess = () => {
    // Refresh interviews after successful edit with force refetch
    queryClient.invalidateQueries({ queryKey: ["interviews"] });
    queryClient.invalidateQueries({ queryKey: ["calendar-events"] });

    // Also trigger a manual refetch to ensure data is updated
    refetchInterviews();
  };

  const handleStatusChange = async (
    interviewId: string,
    newStatus: Interview["status"],
  ) => {
    try {
      await updateInterviewStatusMutation.mutateAsync({
        id: interviewId,
        status: newStatus,
      });

      // Update local state immediately for better UX
      setInterviews(
        interviews.map((interview) =>
          interview.id === interviewId
            ? { ...interview, status: newStatus }
            : interview,
        ),
      );
    } catch (error) {
      toast.error("Có lỗi xảy ra khi cập nhật trạng thái phỏng vấn");
    }
  };

  const exportCalendarData = () => {
    const calendarData = {
      month: selectedDate.toLocaleDateString("vi-VN", {
        month: "long",
        year: "numeric",
      }),
      exportDate: new Date().toISOString(),
      statistics: calendarStats,
      interviews: filteredInterviews,
      totalInterviews: interviews.length,
      apiSource: "HireFlow ATS API v2.0.1",
    };

    const blob = new Blob([JSON.stringify(calendarData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `hireflow_calendar_${selectedDate.getFullYear()}_${selectedDate.getMonth() + 1}.json`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success("Đã xuất dữ liệu lịch thành công");
  };

  // Loading state
  if (isLoadingInterviews && interviews.length === 0) {
    return (
      <ProtectedRoute>
        <Layout>
          <div className="flex items-center justify-center h-96">
            <div className="flex items-center gap-2">
              <Loader2 className="w-6 h-6 animate-spin" />
              <span>Đang tải lịch phỏng vấn...</span>
            </div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  // Error state (only show if no data and there's an error)
  if (interviewsError && interviews.length === 0) {
    return (
      <ProtectedRoute>
        <Layout>
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Có lỗi xảy ra</h3>
              <p className="text-muted-foreground mb-4">
                Không thể tải dữ liệu lịch phỏng vấn
              </p>
              <Button onClick={() => refetchInterviews()}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Thử lại
              </Button>
            </div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <Layout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
                {t.calendar?.title || "Lịch Phỏng Vấn"}
              </h1>
              <p className="text-muted-foreground">
                {t.calendar?.subtitle || "Quản lý và theo dõi lịch phỏng vấn"}
              </p>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                className="gap-2 rounded-xl"
                onClick={() => refetchInterviews()}
                disabled={isLoadingInterviews}
              >
                {isLoadingInterviews ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
                Làm mới
              </Button>

              <Button
                className="ai-button gap-2"
                onClick={() => {
                  setEditingInterview(null); // Clear any existing interview for create mode
                  setIsInterviewModalOpen(true);
                }}
                disabled={scheduleInterviewMutation.isPending}
              >
                {scheduleInterviewMutation.isPending ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Plus className="w-4 h-4" />
                )}
                {t.calendar?.scheduleInterview || "Lên lịch phỏng vấn"}
              </Button>
            </div>
          </div>

          {/* Enhanced Statistics Dashboard */}
          <CalendarSummaryCard stats={calendarStats} />

          {/* Enhanced Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex gap-2 flex-wrap">
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-[140px] rounded-xl">
                  <SelectValue placeholder="Tất cả loại" />
                </SelectTrigger>
                <SelectContent className="rounded-xl">
                  <SelectItem value="all">Tất cả loại</SelectItem>
                  <SelectItem value="video">Video</SelectItem>
                  <SelectItem value="phone">Điện thoại</SelectItem>
                  <SelectItem value="in-person">Trực tiếp</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-[140px] rounded-xl">
                  <SelectValue placeholder="Tất cả trạng thái" />
                </SelectTrigger>
                <SelectContent className="rounded-xl">
                  <SelectItem value="all">Tất cả trạng thái</SelectItem>
                  <SelectItem value="scheduled">Đã lên lịch</SelectItem>
                  <SelectItem value="completed">Hoàn thành</SelectItem>
                  <SelectItem value="cancelled">Đã hủy</SelectItem>
                  <SelectItem value="rescheduled">Dời lịch</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={filterInterviewer}
                onValueChange={setFilterInterviewer}
              >
                <SelectTrigger className="w-[160px] rounded-xl">
                  <SelectValue placeholder="Tất cả người PV" />
                </SelectTrigger>
                <SelectContent className="rounded-xl max-h-60">
                  <SelectItem value="all">Tất cả người phỏng vấn</SelectItem>
                  {Array.from(new Set(interviews.map((i) => i.interviewer)))
                    .filter(Boolean)
                    .sort()
                    .map((interviewer) => (
                      <SelectItem key={interviewer} value={interviewer}>
                        {interviewer}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Dual View System */}
          <div className="space-y-6">
            <ViewToggle
              viewMode={viewMode}
              onViewModeChange={setViewMode}
              totalInterviews={interviews.length}
              filteredInterviews={filteredInterviews.length}
              onExport={exportCalendarData}
            />

            {/* View Content */}
            {viewMode === "calendar" ? (
              <MonthlyCalendar
                interviews={filteredInterviews}
                selectedDate={selectedDate}
                onDateChange={setSelectedDate}
                onInterviewClick={handleInterviewClick}
              />
            ) : (
              <InterviewListView
                interviews={filteredInterviews}
                onInterviewClick={handleInterviewClick}
                onStatusChange={handleStatusChange}
                isLoading={isLoadingInterviews}
              />
            )}
          </div>

          {/* Enhanced Interview Detail Modal */}
          <EnhancedInterviewDetailModal
            interview={selectedInterview}
            isOpen={isDetailModalOpen}
            onClose={() => {
              setIsDetailModalOpen(false);
              setSelectedInterview(null);
            }}
            onEdit={handleInterviewEdit}
            onStatusChange={handleStatusChange}
          />

          {/* Unified Interview Modal - handles both create and edit */}
          <EditInterviewModal
            isOpen={isInterviewModalOpen}
            onClose={() => {
              setIsInterviewModalOpen(false);
              setEditingInterview(null);
            }}
            interview={editingInterview}
            onSuccess={handleEditSuccess}
            selectedDate={selectedDate}
          />
        </div>
      </Layout>
    </ProtectedRoute>
  );
}
