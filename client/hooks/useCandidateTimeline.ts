import { useQuery, useQueryClient } from "@tanstack/react-query";
import { apiService } from "@/lib/api";
import { TimelineResponse, TimelineFilters } from "@/lib/types/candidateTimeline";
import { timelineConfig } from "@/lib/config/timeline";

export const useCandidateTimeline = (
  candidateId: string | number,
  filters: TimelineFilters = {}
) => {
  const queryKey = ["candidateTimeline", candidateId, filters];

  return useQuery({
    queryKey,
    queryFn: async (): Promise<TimelineResponse> => {
      try {
        const response = await apiService.getCandidateTimeline(candidateId, filters);

        // Validate response structure
        if (!response.data || typeof response.data !== 'object') {
          throw new Error('Invalid response format');
        }

        return response as TimelineResponse;
      } catch (error: any) {
        // Transform API errors to user-friendly messages
        if (error.response?.status === 404) {
          throw new Error('Ứng viên không tồn tại');
        } else if (error.response?.status === 403) {
          throw new Error('Bạn không có quyền xem timeline của ứng viên này');
        } else if (error.response?.status >= 500) {
          throw new Error('Lỗi server, vui lòng thử lại sau');
        } else {
          throw new Error(error.message || 'Không thể tải timeline hoạt động');
        }
      }
    },
    enabled: !!candidateId,
    staleTime: timelineConfig.staleTime,
    refetchOnWindowFocus: false,
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors
      if (error.response?.status >= 400 && error.response?.status < 500) {
        return false;
      }
      return failureCount < timelineConfig.maxRetries;
    },
    retryDelay: (attemptIndex) => Math.min(timelineConfig.retryDelay * 2 ** attemptIndex, 30000),
  });
};

export const useRefreshTimeline = () => {
  const queryClient = useQueryClient();
  
  return (candidateId: string | number) => {
    queryClient.invalidateQueries({
      queryKey: ["candidateTimeline", candidateId],
    });
  };
};

// Hook for real-time updates (if needed)
export const useTimelineSubscription = (candidateId: string | number) => {
  const refreshTimeline = useRefreshTimeline();
  
  // This could be extended to use WebSockets or Server-Sent Events
  // For now, we'll just provide a manual refresh mechanism
  return {
    refresh: () => refreshTimeline(candidateId),
  };
};
