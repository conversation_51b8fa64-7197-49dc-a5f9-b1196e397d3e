// Enhanced candidate notes API hooks
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiService } from "@/lib/api";
import { useNotifications } from "./use-notifications";
import { useCurrentUser } from "./useCurrentUser";
import {
  CandidateNotes,
  CandidateNote,
  CreateNoteRequest,
  UpdateNoteRequest,
  createNote,
  updateNote,
  validateNoteContent,
} from "@/lib/types/candidateNotes";
import { queryKeys } from "./useApi";

// Hook for adding a new note to a candidate
export const useAddCandidateNote = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();
  const { profile: currentUser } = useCurrentUser();

  return useMutation({
    mutationFn: async ({
      candidateId,
      content,
    }: {
      candidateId: string;
      content: string;
    }) => {
      if (!currentUser) {
        throw new Error("User authentication required");
      }

      // Validate note content
      const validation = validateNoteContent(content);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(", "));
      }

      // Get current candidate data to update notes array
      const candidateResponse = await apiService.getCandidate(candidateId);
      const currentNotes: CandidateNotes =
        candidateResponse.data.notes || [];

      // Create new note with user context
      const newNote: CandidateNote = {
        content: content.trim(),
        created_at: new Date().toISOString(),
        created_by: currentUser.fullName,
        created_id: parseInt(currentUser.id),
        updated_at: null,
        updated_by: null,
        updated_id: null,
      };

      // Add new note to the array
      const updatedNotes = [...currentNotes, newNote];

      // Update candidate with new notes array
      return apiService.updateCandidate(candidateId, {
        notes: updatedNotes,
      });
    },
    onSuccess: (_, variables) => {
      // Invalidate candidate queries to refresh the data
      queryClient.invalidateQueries({
        queryKey: queryKeys.candidate(variables.candidateId),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.candidates(),
      });
      notifications.success.created();
    },
    onError: (error: any) => {
      notifications.showApiError(error);
    },
  });
};

// Hook for updating an existing note
export const useUpdateCandidateNote = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();
  const { profile: currentUser } = useCurrentUser();

  return useMutation({
    mutationFn: async ({
      candidateId,
      noteIndex,
      content,
    }: {
      candidateId: string;
      noteIndex: number;
      content: string;
    }) => {
      if (!currentUser) {
        throw new Error("User authentication required");
      }

      // Validate note content
      const validation = validateNoteContent(content);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(", "));
      }

      // Get current candidate data
      const candidateResponse = await apiService.getCandidate(candidateId);
      const currentNotes: CandidateNotes =
        candidateResponse.data.notes || [];

      if (noteIndex < 0 || noteIndex >= currentNotes.length) {
        throw new Error("Invalid note index");
      }

      // Update the specific note
      const updatedNotes = [...currentNotes];
      updatedNotes[noteIndex] = {
        ...updatedNotes[noteIndex],
        content: content.trim(),
        updated_at: new Date().toISOString(),
        updated_by: currentUser.fullName,
        updated_id: parseInt(currentUser.id),
      };

      // Update candidate with modified notes array
      return apiService.updateCandidate(candidateId, {
        notes: updatedNotes,
      });
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.candidate(variables.candidateId),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.candidates(),
      });
      notifications.success.updated();
    },
    onError: (error: any) => {
      notifications.showApiError(error);
    },
  });
};

// Hook for deleting a note
export const useDeleteCandidateNote = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: async ({
      candidateId,
      noteIndex,
    }: {
      candidateId: string;
      noteIndex: number;
    }) => {
      // Get current candidate data
      const candidateResponse = await apiService.getCandidate(candidateId);
      const currentNotes: CandidateNotes =
        candidateResponse.data.notes || [];

      if (noteIndex < 0 || noteIndex >= currentNotes.length) {
        throw new Error("Invalid note index");
      }

      // Remove the note at the specified index
      const updatedNotes = currentNotes.filter((_, index) => index !== noteIndex);

      // Update candidate with modified notes array
      return apiService.updateCandidate(candidateId, {
        notes: updatedNotes,
      });
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.candidate(variables.candidateId),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.candidates(),
      });
      notifications.success.deleted();
    },
    onError: (error: any) => {
      notifications.showApiError(error);
    },
  });
};

// Hook for bulk notes operations (useful for form submissions)
export const useUpdateCandidateNotes = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();

  return useMutation({
    mutationFn: async ({
      candidateId,
      notes,
    }: {
      candidateId: string;
      notes: CandidateNotes;
    }) => {
      // Validate all notes
      for (const note of notes) {
        const validation = validateNoteContent(note.content);
        if (!validation.isValid) {
          throw new Error(`Invalid note: ${validation.errors.join(", ")}`);
        }
      }

      // Update candidate with new notes array
      return apiService.updateCandidate(candidateId, {
        notes,
      });
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.candidate(variables.candidateId),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.candidates(),
      });
      notifications.success.updated();
    },
    onError: (error: any) => {
      notifications.showApiError(error);
    },
  });
};

// Custom hook to provide all notes operations for a candidate
export const useCandidateNotesOperations = (candidateId: string) => {
  const addNote = useAddCandidateNote();
  const updateNote = useUpdateCandidateNote();
  const deleteNote = useDeleteCandidateNote();
  const updateAllNotes = useUpdateCandidateNotes();

  // If no candidateId provided, return disabled operations
  const isDisabled = !candidateId || candidateId.trim() === "";

  return {
    addNote: isDisabled
      ? async (content: string) => Promise.reject(new Error("No candidate ID provided"))
      : (content: string) => addNote.mutateAsync({ candidateId, content }),
    updateNote: isDisabled
      ? async (noteIndex: number, content: string) => Promise.reject(new Error("No candidate ID provided"))
      : (noteIndex: number, content: string) => updateNote.mutateAsync({ candidateId, noteIndex, content }),
    deleteNote: isDisabled
      ? async (noteIndex: number) => Promise.reject(new Error("No candidate ID provided"))
      : (noteIndex: number) => deleteNote.mutateAsync({ candidateId, noteIndex }),
    updateAllNotes: isDisabled
      ? async (notes: CandidateNotes) => Promise.reject(new Error("No candidate ID provided"))
      : (notes: CandidateNotes) => updateAllNotes.mutateAsync({ candidateId, notes }),
    isLoading: isDisabled
      ? false
      : (addNote.isPending || updateNote.isPending || deleteNote.isPending || updateAllNotes.isPending),
    error: isDisabled
      ? null
      : (addNote.error || updateNote.error || deleteNote.error || updateAllNotes.error),
    isDisabled,
  };
};

// Hook for adding notes during candidate creation
export const useCreateCandidateWithNotes = () => {
  const queryClient = useQueryClient();
  const notifications = useNotifications();
  const { profile: currentUser } = useCurrentUser();

  return useMutation({
    mutationFn: async ({
      candidateData,
      initialNotes = [],
    }: {
      candidateData: any;
      initialNotes?: string[];
    }) => {
      if (!currentUser) {
        throw new Error("User authentication required");
      }

      // Convert initial notes to structured format
      const structuredNotes: CandidateNotes = initialNotes
        .filter(content => content.trim().length > 0)
        .map(content => {
          const validation = validateNoteContent(content);
          if (!validation.isValid) {
            throw new Error(`Invalid note: ${validation.errors.join(", ")}`);
          }

          return {
            content: content.trim(),
            created_at: new Date().toISOString(),
            created_by: currentUser.fullName,
            created_id: parseInt(currentUser.id),
            updated_at: null,
            updated_by: null,
            updated_id: null,
          };
        });

      // Create candidate with structured notes
      return apiService.createCandidate({
        ...candidateData,
        notes: structuredNotes,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.candidates() });
      notifications.success.created();
    },
    onError: (error: any) => {
      notifications.showApiError(error);
    },
  });
};
