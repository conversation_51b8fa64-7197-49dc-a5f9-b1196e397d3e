import "./global.css";

import { Toaster } from "@/components/ui/toaster";
import { createRoot } from "react-dom/client";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { I18nProvider } from "@/lib/i18n";
import { ThemeProvider } from "@/components/theme-provider";
import { AuthProvider } from "@/lib/auth";
import Landing from "./pages/Landing";
import EnhancedDashboard from "./pages/EnhancedDashboard";
import Candidates from "./pages/Candidates";
import Jobs from "./pages/Jobs";
import Pipeline from "./pages/Pipeline";
import Calendar from "./pages/Calendar";
import { Interviewers } from "./pages/Interviewers";
import Analytics from "./pages/Analytics";
import Profile from "./pages/Profile";
import TeamManagement from "./pages/TeamManagement";
import Settings from "./pages/Settings";
import Login from "./pages/Login";
import CandidateDetail from "./pages/CandidateDetail";
import CandidateComparison from "./pages/CandidateComparison";
import JobDetail from "./pages/JobDetail";
import NotFound from "./pages/NotFound";
import MessagesWithTemplates from "./pages/MessagesWithTemplates";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <ThemeProvider defaultTheme="system" storageKey="hireflow-ui-theme">
        <I18nProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <Routes>
                <Route path="/" element={<EnhancedDashboard />} />
                {/* <Route path="/" element={<Landing />} /> */}
                <Route path="/dashboard" element={<EnhancedDashboard />} />
                <Route path="/candidates" element={<Candidates />} />
                <Route
                  path="/candidates/detail/:id"
                  element={<CandidateDetail />}
                />
                <Route
                  path="/candidates/compare"
                  element={<CandidateComparison />}
                />
                <Route path="/jobs" element={<Jobs />} />
                <Route path="/jobs/detail/:id" element={<JobDetail />} />
                <Route path="/pipeline" element={<Pipeline />} />
                <Route path="/calendar" element={<Calendar />} />
                <Route path="/interviewers" element={<Interviewers />} />
                <Route path="/messages" element={<MessagesWithTemplates />} />
                <Route path="/analytics" element={<Analytics />} />
                <Route path="/profile" element={<Profile />} />
                <Route path="/team" element={<TeamManagement />} />
                <Route path="/settings" element={<Settings />} />
                <Route path="/login" element={<Login />} />
                {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
          </TooltipProvider>
        </I18nProvider>
      </ThemeProvider>
    </AuthProvider>
  </QueryClientProvider>
);

// Ensure root is only created once to prevent warnings
const container = document.getElementById("root")!;
let root = (container as any)._reactRoot;
if (!root) {
  root = createRoot(container);
  (container as any)._reactRoot = root;
}
root.render(<App />);
