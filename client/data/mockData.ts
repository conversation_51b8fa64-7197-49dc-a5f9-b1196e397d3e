// Mockup data for HireFlow ATS system

import { CandidateNotes } from "@/lib/types/candidateNotes";

export interface Candidate {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  initials: string;
  position: string;
  experience: string;
  skills: string[]; // v2.0.1: simplified string array
  status:
    | "sourced"
    | "applied"
    | "screening"
    | "interview"
    | "offer"
    | "hired"
    | "rejected";
  appliedDate: string;
  source: string;
  location: string;
  salary?: string;
  notes?: CandidateNotes;
  resumeUrl?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  portfolioUrl?: string; // v2.0.1: added portfolio URL
  rating?: number;
  interviewDate?: string;
  jobId?: string; // v2.0.1: made optional (nullable)
  // v2.0.1: added new fields
  tags?: string[]; // simplified string array
  education?: string; // TEXT field instead of complex objects
  workHistory?: string; // TEXT field instead of complex objects
  aiScore?: number;
  salaryExpectationMin?: number;
  salaryExpectationMax?: number;
  salaryCurrency?: string;
  salaryExpectation?: {
    min: string;
    max: string;
    currency: string;
    range: string;
  };
  jobPosting?: {
    id: number;
    title: string;
    department: string;
  };
  createdBy?: {
    id: number;
    name: string;
    email: string;
  };
  assignedTo?: {
    id: number;
    name: string;
    email: string;
  };
  createdAt?: string;
  updatedAt?: string;
}

export interface Job {
  positions: number;
  id: string;
  title: string;
  department: string;
  location: string;
  type: "full-time" | "part-time" | "contract" | "internship";
  workLocation?: "onsite" | "remote" | "hybrid"; // v2.0.1: added work location
  salary: {
    min: string;
    max: string;
    currency: string;
    range?: string;
  };
  description: string;
  requirements: string[]; // v2.0.1: simplified string array
  benefits: string[]; // v2.0.1: simplified string array
  responsibilities?: string[]; // v2.0.1: added responsibilities array
  skills?: string[]; // v2.0.1: added skills array
  status: "draft" | "active" | "paused" | "closed";
  postedDate: string;
  closingDate: string;
  applicantCount: number;
  viewCount?: number; // v2.0.1: added view count
  hiringManager: string;
  hiringManagerId?: number; // v2.0.1: added hiring manager ID
  recruiter?: string; // v2.0.1: added recruiter
  recruiterId?: number; // v2.0.1: added recruiter ID
  priority: "low" | "medium" | "high" | "urgent"; // v2.0.1: added urgent priority
  experienceLevel?: "entry" | "mid" | "senior" | "lead"; // v2.0.1: added experience level
  educationRequired?: string; // v2.0.1: added education requirement
  companyCulture?: string; // v2.0.1: added company culture
}

export interface Interview {
  id: string;
  candidateId: string;
  candidateName: string;
  candidateEmail?: string;
  candidatePhone?: string;
  candidatePosition?: string;
  candidateRating?: number;
  candidateAiScore?: number;
  jobId?: string;
  jobTitle: string;
  jobDepartment?: string;
  jobLocation?: string;
  jobSalary?: string;
  jobPriority?: "low" | "medium" | "high" | "urgent";
  date: string;
  time: string;
  duration?: number;
  type: "phone" | "video" | "in-person";
  interviewer: string;
  interviewerId?: string;
  interviewerDepartment?: string;
  interviewerExpertise?: string[];
  status: "scheduled" | "completed" | "cancelled" | "rescheduled";
  location?: string;
  meetingLink?: string;
  meetingPassword?: string;
  notes?: string;
  agenda?: string[];
  round?: number;
  interviewType?: string;
  reminderSent?: boolean;
  apiData?: any; // Keep original API data for reference
  feedback?: {
    rating: number;
    comments: string;
    recommend: boolean;
  };
}

export interface Message {
  id: string;
  type: "email" | "note" | "sms";
  subject: string;
  content: string;
  sender: string;
  recipient: string;
  timestamp: string;
  status: "sent" | "delivered" | "read" | "draft";
  candidateId?: string;
  jobId?: string;
}

export interface Activity {
  id: string;
  type:
    | "application"
    | "interview"
    | "offer"
    | "hire"
    | "reject"
    | "note"
    | "status_change";
  candidateId: string;
  candidateName: string;
  candidateAvatar?: string;
  candidateInitials: string;
  jobTitle: string;
  timestamp: string;
  description: string;
  performer: string;
  details?: any;
}
export const mockInterviews: Interview[] = [];
// Mock Data
export const mockCandidates: Candidate[] = [];

export const mockJobs: Job[] = [];

export const mockActivities: Activity[] = [];

export const mockMessages: Message[] = [];
