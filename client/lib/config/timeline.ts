// Timeline configuration
export const timelineConfig = {
  // Default pagination settings
  defaultPageSize: 20,
  maxPageSize: 100,

  // Refresh intervals
  staleTime: 30000, // 30 seconds
  refetchInterval: 0, // Disable automatic refetch

  // Error retry settings
  maxRetries: 2,
  retryDelay: 1000,

  // Real-time update settings
  enableRealTimeUpdates: false, // Can be enabled with WebSocket support

  // UI settings
  compactMaxItems: 5,
  showUserAvatars: true,
  enableFiltering: true,
  enableSearch: false, // Not implemented yet

  // Accessibility
  announceUpdates: true,
  keyboardNavigation: true,
};

export type TimelineConfig = typeof timelineConfig;
