// Types for enhanced candidate notes functionality
// Based on backend implementation in candidate-notes-implementation.md

export interface CandidateNote {
  id?: number; // Index in the array (from API response)
  content: string; // The actual note content (max 2000 characters)
  created_at: string; // ISO timestamp when the note was created
  created_by: string; // Name of the user who created the note
  created_id: number; // ID of the user who created the note
  updated_at?: string | null; // ISO timestamp when the note was last updated
  updated_by?: string | null; // Name of the user who last updated the note
  updated_id?: number | null; // ID of the user who last updated the note
}

// For creating new notes - only content is required, metadata will be set automatically
export interface CreateNoteRequest {
  content: string;
  created_by?: string; // Optional - will be set from current user context
  created_id?: number; // Optional - will be set from current user context
  created_at?: string; // Optional - will be set to current timestamp
}

// For updating existing notes
export interface UpdateNoteRequest {
  content: string;
  updated_by?: string; // Optional - will be set from current user context
  updated_id?: number; // Optional - will be set from current user context
  updated_at?: string; // Optional - will be set to current timestamp
}

// Notes array structure as stored in the database
export type CandidateNotes = CandidateNote[];

// Props for notes-related API calls
export interface NotesApiParams {
  candidateId: string;
  notes: CandidateNotes;
}

// Props for individual note operations
export interface NoteOperationParams {
  candidateId: string;
  noteIndex: number;
  noteData?: UpdateNoteRequest | CreateNoteRequest;
}

// Response structure for candidate data with enhanced notes
export interface CandidateWithEnhancedNotes {
  id: string;
  name: string;
  email: string;
  // ... other candidate fields
  notes: CandidateNotes; // Array of structured notes instead of simple string
}

// Utility type for note validation
export interface NoteValidation {
  isValid: boolean;
  errors: string[];
}

// Props for the enhanced CandidateNotes component
export interface EnhancedCandidateNotesProps {
  candidateId: string;
  notes: CandidateNotes;
  onAddNote: (content: string) => Promise<void>;
  onUpdateNote: (noteIndex: number, content: string) => Promise<void>;
  onDeleteNote: (noteIndex: number) => Promise<void>;
  disabled?: boolean;
  currentUser?: {
    id: number;
    name: string;
    avatar?: string;
    initials: string;
  };
  maxNoteLength?: number; // Default: 2000 characters
}

// Helper functions for note operations
export const createNote = (
  content: string,
  user: { id: number; name: string }
): CreateNoteRequest => ({
  content,
  created_by: user.name,
  created_id: user.id,
  created_at: new Date().toISOString(),
});

export const updateNote = (
  content: string,
  user: { id: number; name: string }
): UpdateNoteRequest => ({
  content,
  updated_by: user.name,
  updated_id: user.id,
  updated_at: new Date().toISOString(),
});

// Validation function for note content
export const validateNoteContent = (
  content: string,
  maxLength: number = 2000
): NoteValidation => {
  const errors: string[] = [];

  if (!content || content.trim().length === 0) {
    errors.push("Note content is required");
  }

  if (content.length > maxLength) {
    errors.push(`Note content must be ${maxLength} characters or less`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Helper to format note dates for display
export const formatNoteDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleString("vi-VN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch {
    return dateString;
  }
};

// Helper to get the latest note
export const getLatestNote = (notes: CandidateNotes): CandidateNote | null => {
  if (!notes || notes.length === 0) return null;
  
  return notes.reduce((latest, current) => {
    const latestDate = new Date(latest.created_at);
    const currentDate = new Date(current.created_at);
    return currentDate > latestDate ? current : latest;
  });
};

// Helper to count notes
export const getNotesCount = (notes: CandidateNotes): number => {
  return notes ? notes.length : 0;
};

// Helper to check if candidate has notes
export const hasNotes = (notes: CandidateNotes): boolean => {
  return notes && notes.length > 0;
};
