export interface TimelineUser {
  id: number;
  name: string;
  email: string;
}

export interface TimelineMetadata {
  old_status?: string;
  new_status?: string;
  notes?: string;
  score?: number;
  recommendation?: string;
  interview_id?: number;
  message_type?: string;
  subject?: string;
  recipient?: string;
  field_name?: string;
  old_value?: any;
  new_value?: any;
  source?: string;
  position?: string;
  analysis_type?: string;
  completion_status?: string;
  [key: string]: any;
}

export interface TimelineActivity {
  id: string;
  type: 
    | "status_change"
    | "interview_scheduled"
    | "interview_feedback"
    | "communication"
    | "profile_analysis"
    | "profile_updated"
    | "notes_updated"
    | "candidate_created";
  category: "candidate" | "interview" | "communication";
  title: string;
  description: string;
  timestamp: string;
  user: TimelineUser;
  metadata: TimelineMetadata;
  icon: string;
  color: string;
}

export interface TimelinePagination {
  current_page: number;
  per_page: number;
  total: number;
  last_page: number;
  from: number;
  to: number;
}

export interface TimelineResponse {
  status: "success" | "error";
  data: {
    activities: TimelineActivity[];
    pagination: TimelinePagination;
  };
}

export interface TimelineFilters {
  page?: number;
  per_page?: number;
  type?: string;
  category?: string;
  date_from?: string;
  date_to?: string;
}

export type ActivityType = TimelineActivity["type"];
export type ActivityCategory = TimelineActivity["category"];

// Color mapping for different status types
export const STATUS_COLORS = {
  sourced: "blue",
  applied: "indigo", 
  screening: "yellow",
  interview: "purple",
  offer: "orange",
  hired: "green",
  rejected: "red",
} as const;

export const MESSAGE_STATUS_COLORS = {
  sent: "green",
  delivered: "blue",
  read: "purple",
  failed: "red",
  queued: "yellow",
  draft: "gray",
} as const;

export const SCORE_COLORS = {
  excellent: "green", // 80+
  good: "blue",      // 60-79
  fair: "yellow",    // 40-59
  poor: "red",       // <40
} as const;

// Icon mapping for activity types
export const ACTIVITY_ICONS = {
  status_change: "UserCheck",
  interview_scheduled: "Calendar",
  interview_feedback: "MessageSquare",
  communication: "Mail",
  profile_analysis: "Brain",
  profile_updated: "Edit",
  notes_updated: "FileText",
  candidate_created: "UserPlus",
} as const;

export const getScoreColor = (score: number): keyof typeof SCORE_COLORS => {
  if (score >= 80) return "excellent";
  if (score >= 60) return "good";
  if (score >= 40) return "fair";
  return "poor";
};

export const getStatusColor = (status: string): string => {
  return STATUS_COLORS[status as keyof typeof STATUS_COLORS] || "gray";
};

export const getMessageStatusColor = (status: string): string => {
  return MESSAGE_STATUS_COLORS[status as keyof typeof MESSAGE_STATUS_COLORS] || "gray";
};
