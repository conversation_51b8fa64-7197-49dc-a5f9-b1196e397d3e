// TypeScript interfaces for adapter input/output types

import { PaginationData } from "./utils";
import { CandidateNotes } from "@/lib/types/candidateNotes";

// API Response wrapper interface
export interface ApiResponse<T = any> {
  status: string;
  data: T;
  message?: string;
}

export interface PaginatedApiResponse<T = any> extends ApiResponse {
  data: T[];
  meta: {
    current_page: number;
    last_page: number;
    total: number;
    per_page: number;
  };
  links: {
    next?: string;
    prev?: string;
  };
}

// Candidate interfaces
export interface ApiCandidate {
  id: number;
  name: string;
  email: string;
  phone?: string;
  position?: string;
  experience?: string;
  skills: string[];
  status: string;
  applied_date?: string;
  source?: string;
  location?: string;
  salary_expectation_min?: number;
  salary_expectation_max?: number;
  salary_currency?: string;
  salary_expectation_range?: string;
  rating?: number;
  interview_date?: string;
  job_posting_id?: number;
  social_links?: any;
  avatar?: string;
  avatar_url?: string;
  resume_url?: string;
  notes?: string | CandidateNotes; // Support both legacy string and new array structure
  ai_score?: number;
  tags: string[];
  education?: string; // v2.0.1: simplified to TEXT field
  work_history?: string; // v2.0.1: simplified to TEXT field
  job_posting?: any;
  created_by?: any;
  assigned_to?: any;
  created_at?: string;
  updated_at?: string;
}

export interface UiCandidate {
  id: string;
  name: string;
  email: string;
  phone?: string;
  initials: string;
  position?: string;
  experience?: string;
  skills: string[];
  status: string;
  appliedDate?: string;
  source?: string;
  location?: string;
  salary: string;
  rating?: number;
  interviewDate?: string;
  jobId?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  portfolioUrl?: string;
  avatar?: string;
  resumeUrl?: string;
  notes?: string | CandidateNotes; // Support both legacy string and new array structure
  aiScore: number;
  tags: string[];
  education: string;
  workHistory: string;
  salaryExpectationMin?: number;
  salaryExpectationMax?: number;
  salaryCurrency?: string;
  salaryExpectation?: any;
  jobPosting?: any;
  createdBy?: any;
  assignedTo?: any;
  createdAt?: string;
  updatedAt?: string;
}

export interface CandidateListResponse {
  candidates: UiCandidate[];
  pagination: PaginationData;
}

// Job interfaces
export interface ApiJob {
  id: number;
  title: string;
  department: string;
  location: string;
  type: string;
  work_location?: string;
  salary: any;
  currency?: string;
  description?: string;
  requirements: string[]; // v2.0.1: simplified to string array
  responsibilities: string[]; // v2.0.1: simplified to string array
  benefits: string[]; // v2.0.1: simplified to string array
  skills: string[]; // v2.0.1: simplified to string array
  status: string;
  posted_date?: string;
  closing_date?: string;
  applicant_count?: number;
  view_count?: number;
  hiring_manager?: any;
  hiring_manager_name?: string;
  hiring_manager_id?: number;
  recruiter?: any;
  recruiter_name?: string;
  recruiter_id?: number;
  priority?: string;
  experience_level?: string;
  education_required?: string; // v2.0.1: education requirement
  company_culture?: string; // v2.0.1: company culture
  positions?: number; // v2.0.1: number of positions
  created_at?: string;
  updated_at?: string;
  salary_min?: string;
  salary_max?: string;
}

export interface UiJob {
  id: string;
  title: string;
  department: string;
  location: string;
  type: string;
  workLocation?: string;
  salary: {
    min?: string;
    max?: string;
    currency?: string;
    range?: string;
  };
  description?: string;
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
  skills: string[];
  status: string;
  postedDate?: string;
  closingDate?: string;
  applicantCount: number;
  viewCount: number;
  hiringManager?: string;
  hiringManagerId?: number;
  recruiter?: string;
  recruiterId?: number;
  priority?: string;
  experienceLevel?: string;
  educationRequired?: string;
  companyCulture?: string;
  positions: number;
}

export interface JobListResponse {
  jobs: UiJob[];
  pagination: PaginationData;
}

// Interview interfaces
export interface ApiInterview {
  id: number;
  candidate_id?: number;
  candidate?: any;
  candidate_name?: string;
  candidate_email?: string;
  job_posting_id?: number;
  job_posting?: any;
  job_title?: string;
  // Laravel API uses scheduled_at instead of separate date/time
  scheduled_at?: string; // ISO datetime string from Laravel
  date?: string; // Fallback for compatibility
  time?: string; // Fallback for compatibility
  duration: number;
  type: string;
  interviewer?: any;
  interviewer_name?: string;
  interviewer_id?: number;
  interviewer_email?: string;
  status: string;
  meeting_link?: string;
  meeting_password?: string;
  location?: string;
  notes?: string;
  agenda?: any[];
  feedback?: any;
  tags?: string[];
  round?: number;
  interview_type?: string;
  score?: number; // Laravel API includes score
  created_at?: string;
  updated_at?: string;
}

export interface UiInterview {
  id: string;
  candidateId?: string;
  candidateName?: string;
  candidateEmail?: string;
  candidateAvatar?: string;
  jobId?: string;
  jobTitle?: string;
  date: string;
  time: string;
  duration: number;
  type: string;
  interviewer?: string;
  interviewerId?: string;
  interviewerEmail?: string;
  status: string;
  meetingLink?: string;
  meetingPassword?: string;
  location?: string;
  notes?: string;
  agenda: any[];
  feedback?: any;
  tags: string[];
  round?: number;
  interviewType?: string;
}

export interface InterviewListResponse {
  interviews: UiInterview[];
  pagination: PaginationData;
}

// Dashboard interfaces
export interface ApiDashboardData {
  summary_cards?: {
    total_candidates?: number;
    new_candidates_this_month?: number;
    interviews_this_week?: number;
    total_hires?: number;
  };
  recent_activities?: Array<{
    id: number;
    type: string;
    candidate_id?: number;
    candidate_name?: string;
    candidate_avatar?: string;
    job_title?: string;
    created_at?: string;
    timestamp?: string;
    description?: string;
    performer?: string;
    user_name?: string;
    priority?: string;
  }>;
  recruitment_pipeline?: {
    by_status?: Record<string, number>;
    total_candidates?: number;
  };
  upcoming_interviews?: Array<{
    id: number;
    candidate_name?: string;
    candidate_avatar?: string;
    job_title?: string;
    date: string;
    time: string;
    type: string;
    interviewer_name?: string;
  }>;
  top_performing_jobs?: Array<{
    id: number;
    title: string;
    department?: string;
    candidates_count?: number;
    status?: string;
    priority?: string;
  }>;
}

export interface UiDashboardData {
  overview: {
    totalCandidates: number;
    newApplications: number;
    scheduledInterviews: number;
    successfulHires: number;
    trends: {
      candidatesGrowth: number;
      applicationsGrowth: number;
      interviewsGrowth: number;
      hiresGrowth: number;
    };
  };
  recentActivity: Array<{
    id: string;
    type: string;
    candidateId?: string;
    candidateName?: string;
    candidateInitials?: string;
    candidateAvatar?: string;
    jobTitle?: string;
    timestamp?: string;
    description?: string;
    performer?: string;
    priority: string;
  }>;
  pipelineOverview: {
    stages: Array<{
      stage: string;
      count: number;
      percentage: number;
      color: string;
    }>;
    maxCount: number;
  };
  upcomingInterviews: Array<{
    id: string;
    candidateName?: string;
    candidateAvatar?: string;
    jobTitle?: string;
    date: string;
    time: string;
    type: string;
    interviewer?: string;
  }>;
  topJobs: Array<{
    id: string;
    title: string;
    department?: string;
    applicantCount?: number;
    status?: string;
    priority?: string;
  }>;
  recentHires: any[];
}

// Filter interfaces
export interface CandidateFilters {
  search?: string;
  status?: string;
  experience?: string;
  location?: string;
  source?: string;
  jobId?: string;
  skills?: string[];
  tags?: string[];
  sort?: string;
  direction?: string;
}

export interface JobFilters {
  search?: string;
  status?: string;
  department?: string;
  location?: string;
  type?: string;
  workLocation?: string;
  experienceLevel?: string;
  priority?: string;
  hiringManagerId?: string;
  recruiterId?: string;
  skills?: string[];
  sort?: string;
  direction?: string;
}

export interface InterviewFilters {
  status?: string;
  type?: string;
  candidateId?: string;
  interviewerId?: string;
  dateFrom?: string;
  dateTo?: string;
}
