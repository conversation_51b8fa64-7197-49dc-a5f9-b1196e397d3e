// Adapter utilities for candidate notes API integration
import {
  CandidateNote,
  CandidateNotes,
  CreateNoteRequest,
  UpdateNoteRequest,
} from "@/lib/types/candidateNotes";

// Transform API response notes to frontend structure
export const transformApiNotesToFrontend = (apiNotes: any): CandidateNotes => {
  if (!apiNotes) return [];

  // Handle both legacy string notes and new array structure
  if (typeof apiNotes === "string") {
    // Legacy string notes - convert to structured format
    if (apiNotes.trim()) {
      return [
        {
          id: 0,
          content: apiNotes,
          created_at: new Date().toISOString(),
          created_by: "Sistema Legacy",
          created_id: 0,
          updated_at: null,
          updated_by: null,
          updated_id: null,
        },
      ];
    }
    return [];
  }

  // New array structure
  if (Array.isArray(apiNotes)) {
    return apiNotes.map((note, index) => ({
      id: note.id ?? index,
      content: note.content || "",
      created_at: note.created_at || new Date().toISOString(),
      created_by: note.created_by || "Unknown",
      created_id: note.created_id || 0,
      updated_at: note.updated_at || null,
      updated_by: note.updated_by || null,
      updated_id: note.updated_id || null,
    }));
  }

  return [];
};

// Transform frontend notes to API request format
export const transformFrontendNotesToApi = (notes: CandidateNotes): any[] => {
  return notes.map((note) => ({
    content: note.content,
    created_at: note.created_at,
    created_by: note.created_by,
    created_id: note.created_id,
    updated_at: note.updated_at,
    updated_by: note.updated_by,
    updated_id: note.updated_id,
  }));
};

// Validate notes structure before sending to API
export const validateNotesForApi = (
  notes: CandidateNotes,
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!Array.isArray(notes)) {
    errors.push("Notes must be an array");
    return { isValid: false, errors };
  }

  notes.forEach((note, index) => {
    if (!note.content || typeof note.content !== "string") {
      errors.push(
        `Note ${index + 1}: Content is required and must be a string`,
      );
    }

    if (note.content && note.content.length > 2000) {
      errors.push(`Note ${index + 1}: Content must be 2000 characters or less`);
    }

    if (!note.created_at) {
      errors.push(`Note ${index + 1}: Created date is required`);
    }

    if (!note.created_by) {
      errors.push(`Note ${index + 1}: Creator name is required`);
    }

    if (typeof note.created_id !== "number") {
      errors.push(`Note ${index + 1}: Creator ID must be a number`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Create a new note structure for API requests
export const createNoteForApi = (
  content: string,
  user: { id: number; name: string },
): CandidateNote => {
  return {
    content: content.trim(),
    created_at: new Date().toISOString(),
    created_by: user.name,
    created_id: user.id,
    updated_at: null,
    updated_by: null,
    updated_id: null,
  };
};

// Update an existing note structure for API requests
export const updateNoteForApi = (
  existingNote: CandidateNote,
  newContent: string,
  user: { id: number; name: string },
): CandidateNote => {
  return {
    ...existingNote,
    content: newContent.trim(),
    updated_at: new Date().toISOString(),
    updated_by: user.name,
    updated_id: user.id,
  };
};

// Helper to ensure backward compatibility
export const ensureNotesCompatibility = (candidateData: any): any => {
  if (!candidateData.notes) {
    return candidateData;
  }

  // If notes is already an array, validate and transform it
  if (Array.isArray(candidateData.notes)) {
    const validation = validateNotesForApi(candidateData.notes);
    if (!validation.isValid) {
      console.warn("Invalid notes structure:", validation.errors);
      // Fall back to empty array if validation fails
      return {
        ...candidateData,
        notes: [],
      };
    }

    return {
      ...candidateData,
      notes: transformFrontendNotesToApi(candidateData.notes),
    };
  }

  // If notes is a string, convert to structured format
  if (typeof candidateData.notes === "string" && candidateData.notes.trim()) {
    return {
      ...candidateData,
      notes: [
        {
          content: candidateData.notes.trim(),
          created_at: new Date().toISOString(),
          created_by: "Sistema",
          created_id: 1,
        },
      ],
    };
  }

  // Default to empty array
  return {
    ...candidateData,
    notes: [],
  };
};

// Helper to get notes count from API response
export const getNotesCountFromApi = (apiResponse: any): number => {
  const notes = transformApiNotesToFrontend(apiResponse?.notes);
  return notes.length;
};

// Helper to get latest note from API response
export const getLatestNoteFromApi = (
  apiResponse: any,
): CandidateNote | null => {
  const notes = transformApiNotesToFrontend(apiResponse?.notes);
  if (notes.length === 0) return null;

  return notes.reduce((latest, current) => {
    const latestDate = new Date(latest.created_at);
    const currentDate = new Date(current.created_at);
    return currentDate > latestDate ? current : latest;
  });
};

// Export all utilities
export const candidateNotesAdapters = {
  transformApiNotesToFrontend,
  transformFrontendNotesToApi,
  validateNotesForApi,
  createNoteForApi,
  updateNoteForApi,
  ensureNotesCompatibility,
  getNotesCountFromApi,
  getLatestNoteFromApi,
};
