#!/bin/bash

# HireFlow ATS Backup Script
# This script creates backups of the MySQL database and application files

set -e

# Configuration
BAC<PERSON>UP_DIR="/backups"
MYSQL_BACKUP_DIR="$BACKUP_DIR/mysql"
APP_BACKUP_DIR="$BACKUP_DIR/backend"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}

# Database configuration
DB_HOST=${DB_HOST:-mysql}
DB_DATABASE=${DB_DATABASE:-hireflow_ats}
DB_USERNAME=${DB_USERNAME:-hireflow_user}
DB_PASSWORD=${DB_PASSWORD}

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Create backup directories
mkdir -p "$MYSQL_BACKUP_DIR" "$APP_BACKUP_DIR"

# MySQL Database Backup
log "Starting MySQL database backup..."
MYSQL_BACKUP_FILE="$MYSQL_BACKUP_DIR/hireflow_db_$DATE.sql.gz"

if mysqldump -h "$DB_HOST" -u "$DB_USERNAME" -p"$DB_PASSWORD" \
    --single-transaction \
    --routines \
    --triggers \
    --events \
    --add-drop-database \
    --databases "$DB_DATABASE" | gzip > "$MYSQL_BACKUP_FILE"; then
    log "MySQL backup completed: $MYSQL_BACKUP_FILE"
else
    log "ERROR: MySQL backup failed"
    exit 1
fi

# Application Files Backup
log "Starting application files backup..."
APP_BACKUP_FILE="$APP_BACKUP_DIR/hireflow_storage_$DATE.tar.gz"

if tar -czf "$APP_BACKUP_FILE" -C /var/www/html/storage .; then
    log "Application files backup completed: $APP_BACKUP_FILE"
else
    log "ERROR: Application files backup failed"
    exit 1
fi

# Cleanup old backups
log "Cleaning up old backups (older than $RETENTION_DAYS days)..."

# Remove old MySQL backups
find "$MYSQL_BACKUP_DIR" -name "hireflow_db_*.sql.gz" -mtime +$RETENTION_DAYS -delete
MYSQL_REMOVED=$(find "$MYSQL_BACKUP_DIR" -name "hireflow_db_*.sql.gz" -mtime +$RETENTION_DAYS 2>/dev/null | wc -l)

# Remove old application backups
find "$APP_BACKUP_DIR" -name "hireflow_storage_*.tar.gz" -mtime +$RETENTION_DAYS -delete
APP_REMOVED=$(find "$APP_BACKUP_DIR" -name "hireflow_storage_*.tar.gz" -mtime +$RETENTION_DAYS 2>/dev/null | wc -l)

log "Cleanup completed. Removed $MYSQL_REMOVED MySQL backups and $APP_REMOVED application backups."

# Backup verification
log "Verifying backup integrity..."

# Verify MySQL backup
if gzip -t "$MYSQL_BACKUP_FILE"; then
    MYSQL_SIZE=$(du -h "$MYSQL_BACKUP_FILE" | cut -f1)
    log "MySQL backup verification passed. Size: $MYSQL_SIZE"
else
    log "ERROR: MySQL backup verification failed"
    exit 1
fi

# Verify application backup
if tar -tzf "$APP_BACKUP_FILE" >/dev/null; then
    APP_SIZE=$(du -h "$APP_BACKUP_FILE" | cut -f1)
    log "Application backup verification passed. Size: $APP_SIZE"
else
    log "ERROR: Application backup verification failed"
    exit 1
fi

# Summary
log "Backup process completed successfully!"
log "MySQL backup: $MYSQL_BACKUP_FILE ($MYSQL_SIZE)"
log "Application backup: $APP_BACKUP_FILE ($APP_SIZE)"

# Optional: Send notification (uncomment and configure as needed)
# curl -X POST -H 'Content-type: application/json' \
#     --data '{"text":"HireFlow ATS backup completed successfully"}' \
#     YOUR_SLACK_WEBHOOK_URL
